# Implementation Guide: Update APS Reset Fields in Database

## Overview
This implementation addresses missing TemplateRolePermission entries for APS factory reset functionality and updates the display names/descriptions to be more descriptive while maintaining backend permission identifiers.

## Requirements Summary
1. **Add TemplateRolePermission entries**: All template roles need entries for both `org_aps_factory_reset` and `synapse_aps_factory_reset` permissions
2. **Permission values**: Only `mun_admin` and `syn_admin` should have `true` values, all others should be `false`
3. **Update display names**: Change "APS Factory Reset" to "APS Password Factory Reset" in names and descriptions
4. **Preserve identifiers**: Do NOT change permission identifiers (`org_aps_factory_reset`, `synapse_aps_factory_reset`)

## Current State Analysis

### Existing Template Roles
Based on codebase analysis, the following template roles exist:

**Municipality roles:**
- `mun_admin` - Admin (should have `true` for org_aps_factory_reset)
- `mun_manager` - Manager 
- `mun_technician` - Signal Technician
- `mun_anonymous` - Anonymous
- `mun_traffic_engineers` - Traffic Engineers
- `mun_construction_contractors` - Construction Contractors
- `mun_maintenance_contractors` - Maintenance Contractors
- `mun_it` - IT
- `mun_consultant` - Consultant

**Synapse roles:**
- `syn_admin` - Admin (should have `true` for synapse_aps_factory_reset)
- `syn_manager` - Manager
- `syn_onboarder` - Onboarder
- `syn_anonymous` - Anonymous

### Current Permission Definitions
```sql
-- Current names and descriptions (to be updated)
('synapse_aps_factory_reset', 'synapse', 'synapse', 6.00, 'Get APS factory reset', 'Grants ability to get factory reset for an APS.')
('org_aps_factory_reset', 'organization', 'municipality', 11.00, 'Get APS factory reset', 'Grants ability to get factory reset for an APS.')
```

## Implementation Steps

### Step 1: Create Database Migration Script
Create a new migration script: `schemas/data-core-pg/1.0/updates/CAT-XXX-update-aps-reset-permissions.sql`

### Step 2: Update Permission Names and Descriptions
Update the existing permission entries to use "APS Password Factory Reset" instead of "APS Factory Reset":

```sql
-- Update permission names and descriptions
UPDATE {{Permission}} 
SET Name = 'Get APS Password Factory Reset',
    Description = 'Grants ability to get password factory reset for an APS.'
WHERE Identifier = 'synapse_aps_factory_reset';

UPDATE {{Permission}} 
SET Name = 'Get APS Password Factory Reset',
    Description = 'Grants ability to get password factory reset for an APS.'
WHERE Identifier = 'org_aps_factory_reset';
```

### Step 3: Add Missing TemplateRolePermission Entries
Add entries for all template roles with appropriate default values:

```sql
-- Add TemplateRolePermission entries for org_aps_factory_reset (municipality roles)
INSERT INTO {{TemplateRolePermission}} (TemplateRoleIdentifier, PermissionIdentifier, DefaultValue) VALUES
  ('mun_admin', 'org_aps_factory_reset', true),                    -- Only admin gets true
  ('mun_manager', 'org_aps_factory_reset', false),
  ('mun_technician', 'org_aps_factory_reset', false),
  ('mun_anonymous', 'org_aps_factory_reset', false),
  ('mun_traffic_engineers', 'org_aps_factory_reset', false),
  ('mun_construction_contractors', 'org_aps_factory_reset', false),
  ('mun_maintenance_contractors', 'org_aps_factory_reset', false),
  ('mun_it', 'org_aps_factory_reset', false),
  ('mun_consultant', 'org_aps_factory_reset', false)
ON CONFLICT (TemplateRoleIdentifier, PermissionIdentifier) DO UPDATE 
SET DefaultValue = EXCLUDED.DefaultValue;

-- Add TemplateRolePermission entries for synapse_aps_factory_reset (synapse roles)
INSERT INTO {{TemplateRolePermission}} (TemplateRoleIdentifier, PermissionIdentifier, DefaultValue) VALUES
  ('syn_admin', 'synapse_aps_factory_reset', true),               -- Only admin gets true
  ('syn_manager', 'synapse_aps_factory_reset', false),
  ('syn_onboarder', 'synapse_aps_factory_reset', false),
  ('syn_anonymous', 'synapse_aps_factory_reset', false)
ON CONFLICT (TemplateRoleIdentifier, PermissionIdentifier) DO UPDATE 
SET DefaultValue = EXCLUDED.DefaultValue;
```

### Step 4: Handle Existing Partial Entries
Some entries may already exist (found evidence of `mun_admin` having `org_aps_factory_reset` in dev.sql). The `ON CONFLICT` clause ensures existing entries are updated to the correct values.

### Step 5: Verification Queries
Add verification queries to ensure all entries are correctly set:

```sql
-- Verify all municipality template roles have org_aps_factory_reset entries
SELECT trp.TemplateRoleIdentifier, trp.PermissionIdentifier, trp.DefaultValue
FROM {{TemplateRolePermission}} trp
JOIN {{TemplateRole}} tr ON tr.Identifier = trp.TemplateRoleIdentifier
WHERE tr.OrgTypeIdentifier = 'municipality' 
  AND trp.PermissionIdentifier = 'org_aps_factory_reset'
ORDER BY trp.TemplateRoleIdentifier;

-- Verify all synapse template roles have synapse_aps_factory_reset entries  
SELECT trp.TemplateRoleIdentifier, trp.PermissionIdentifier, trp.DefaultValue
FROM {{TemplateRolePermission}} trp
JOIN {{TemplateRole}} tr ON tr.Identifier = trp.TemplateRoleIdentifier
WHERE tr.OrgTypeIdentifier = 'synapse' 
  AND trp.PermissionIdentifier = 'synapse_aps_factory_reset'
ORDER BY trp.TemplateRoleIdentifier;

-- Verify permission names were updated
SELECT Identifier, Name, Description 
FROM {{Permission}} 
WHERE Identifier IN ('org_aps_factory_reset', 'synapse_aps_factory_reset');
```

## Testing Considerations

### Database Testing
1. **Run migration script** on development environment
2. **Verify all template roles** have the required permission entries
3. **Confirm only admin roles** have `true` values
4. **Check permission names** are updated to "APS Password Factory Reset"

### Application Testing
1. **Permission middleware**: Verify existing permission checks still work (identifiers unchanged)
2. **Frontend display**: Check if UI shows updated permission names
3. **Role creation**: Test that new custom roles inherit correct default values
4. **Admin functionality**: Ensure admin users can still access APS reset functionality

### Rollback Plan
If issues arise, the changes can be rolled back by:
1. Reverting permission names/descriptions to original values
2. Removing the added TemplateRolePermission entries (if needed)
3. The permission identifiers remain unchanged, so existing functionality should not break

## Files to Modify

### New Files
- `schemas/data-core-pg/1.0/updates/CAT-XXX-update-aps-reset-permissions.sql` (main migration script)

### Files That May Need Updates (if UI displays permission names)
- Frontend components that display permission names
- Any documentation referencing "APS Factory Reset"

## Dependencies
- Database migration system
- No code changes required (permission identifiers unchanged)
- Frontend may need updates if it displays permission names from database

## Success Criteria
1. ✅ All template roles have entries for both APS reset permissions
2. ✅ Only `mun_admin` and `syn_admin` have `true` values
3. ✅ Permission names updated to "APS Password Factory Reset"
4. ✅ Permission identifiers remain unchanged
5. ✅ Existing functionality continues to work
6. ✅ New custom roles inherit correct default permission values

## Complete Migration Script Example

```sql
-- ================================================
-- CAT-XXX: Update APS reset fields in the database
-- ================================================

-- Update permission names and descriptions to be more descriptive
UPDATE {{Permission}}
SET Name = 'Get APS Password Factory Reset',
    Description = 'Grants ability to get password factory reset for an APS.'
WHERE Identifier = 'synapse_aps_factory_reset';

UPDATE {{Permission}}
SET Name = 'Get APS Password Factory Reset',
    Description = 'Grants ability to get password factory reset for an APS.'
WHERE Identifier = 'org_aps_factory_reset';

-- Add TemplateRolePermission entries for org_aps_factory_reset (municipality roles)
-- Only mun_admin should have true, all others false
INSERT INTO {{TemplateRolePermission}} (TemplateRoleIdentifier, PermissionIdentifier, DefaultValue) VALUES
  ('mun_admin', 'org_aps_factory_reset', true),
  ('mun_manager', 'org_aps_factory_reset', false),
  ('mun_technician', 'org_aps_factory_reset', false),
  ('mun_anonymous', 'org_aps_factory_reset', false),
  ('mun_traffic_engineers', 'org_aps_factory_reset', false),
  ('mun_construction_contractors', 'org_aps_factory_reset', false),
  ('mun_maintenance_contractors', 'org_aps_factory_reset', false),
  ('mun_it', 'org_aps_factory_reset', false),
  ('mun_consultant', 'org_aps_factory_reset', false)
ON CONFLICT (TemplateRoleIdentifier, PermissionIdentifier) DO UPDATE
SET DefaultValue = EXCLUDED.DefaultValue;

-- Add TemplateRolePermission entries for synapse_aps_factory_reset (synapse roles)
-- Only syn_admin should have true, all others false
INSERT INTO {{TemplateRolePermission}} (TemplateRoleIdentifier, PermissionIdentifier, DefaultValue) VALUES
  ('syn_admin', 'synapse_aps_factory_reset', true),
  ('syn_manager', 'synapse_aps_factory_reset', false),
  ('syn_onboarder', 'synapse_aps_factory_reset', false),
  ('syn_anonymous', 'synapse_aps_factory_reset', false)
ON CONFLICT (TemplateRoleIdentifier, PermissionIdentifier) DO UPDATE
SET DefaultValue = EXCLUDED.DefaultValue;

-- Verification: Check all municipality roles have org_aps_factory_reset
SELECT 'Municipality APS Reset Permissions' as check_type,
       trp.TemplateRoleIdentifier,
       trp.DefaultValue,
       CASE WHEN trp.DefaultValue = true THEN 'ADMIN ACCESS' ELSE 'NO ACCESS' END as access_level
FROM {{TemplateRolePermission}} trp
JOIN {{TemplateRole}} tr ON tr.Identifier = trp.TemplateRoleIdentifier
WHERE tr.OrgTypeIdentifier = 'municipality'
  AND trp.PermissionIdentifier = 'org_aps_factory_reset'
ORDER BY trp.TemplateRoleIdentifier;

-- Verification: Check all synapse roles have synapse_aps_factory_reset
SELECT 'Synapse APS Reset Permissions' as check_type,
       trp.TemplateRoleIdentifier,
       trp.DefaultValue,
       CASE WHEN trp.DefaultValue = true THEN 'ADMIN ACCESS' ELSE 'NO ACCESS' END as access_level
FROM {{TemplateRolePermission}} trp
JOIN {{TemplateRole}} tr ON tr.Identifier = trp.TemplateRoleIdentifier
WHERE tr.OrgTypeIdentifier = 'synapse'
  AND trp.PermissionIdentifier = 'synapse_aps_factory_reset'
ORDER BY trp.TemplateRoleIdentifier;

-- Verification: Check permission names were updated
SELECT 'Updated Permission Names' as check_type,
       Identifier,
       Name,
       Description
FROM {{Permission}}
WHERE Identifier IN ('org_aps_factory_reset', 'synapse_aps_factory_reset');
```

## Post-Implementation Validation

After running the migration, verify the following:

1. **Count verification**: Ensure all 13 template roles have the appropriate permission entries
2. **Access verification**: Only `mun_admin` and `syn_admin` should have `true` values
3. **Name verification**: Permission names should show "APS Password Factory Reset"
4. **Functional testing**: Admin users should still be able to access APS reset functionality
