Feature: User registration
  As a visitor,
  I want to create a new account,
  So that I can access the application.

  Scenario: Successful registration creates an account and logs in
    Given I navigate to "/account/register"
    When I fill the registration form with valid user data
    And I submit the registration form
    Then I should see a success modal with the title "Account has been created!"
    When I click the "Continue" button on the modal
    Then I should be redirected back to the application
    And I should see a logout button

  Sc<PERSON>rio: Required field validation messages show when fields are empty
    Given I navigate to "/account/register"
    When I focus and blur the "firstname" field
    And I focus and blur the "lastname" field
    And I focus and blur the "email" field
    And I focus and blur the "username" field
    And I focus and blur the "password" field
    And I focus and blur the "confirmPassword" field
    Then I should see the validation message "Please enter your first name"
    And I should see the validation message "Please enter your last name"
    And I should see the validation message "Please enter your email"
    And I should see the validation message "Please enter a username"
    And I should see the validation message "Please enter a password"
    And I should see the validation message "Please confirm your password"

  Scenario: Invalid email shows email validation message
    Given I navigate to "/account/register"
    When I enter "not-an-email" into the "email" field
    And I blur the "email" field
    Then I should see the validation message "Please enter a valid email"

  Scenario: Weak password shows policy validation message
    Given I navigate to "/account/register"
    When I enter "short" into the "password" field
    And I blur the "password" field
    Then I should see the validation message "Password does not meet the required policy"

  Scenario: Password mismatch shows mismatch validation message
    Given I navigate to "/account/register"
    When I enter "Aa1!strong" into the "password" field
    And I enter "different" into the "confirmPassword" field
    And I blur the "confirmPassword" field
    Then I should see the validation message "Passwords do not match"


