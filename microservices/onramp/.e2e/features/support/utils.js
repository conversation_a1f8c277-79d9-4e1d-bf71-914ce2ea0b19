const { By, until, Key } = require('selenium-webdriver');

async function waitForPageLoad(driver, timeout = 15000) {
  try {
    await driver.executeScript('return document.readyState').then(state => {
      if (state !== 'complete') {
        return driver.wait(() => driver.executeScript('return document.readyState === "complete"'), timeout);
      }
    });
  } catch (err) {
    console.error('Error waiting for page load:', err);
    console.error('Page source:', await driver.getPageSource());
    throw err;
  }
}
async function retry(fn, retries = 3, delay = 500) {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (err) {
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw err;
    }
  }
}

async function closeAllModals(driver, excludeSelector = null) {
  try {
    // More comprehensive modal selectors including the problematic nz-modal-confirm-container
    const modalSelectors = [
      '.ant-modal-wrap',
      '.nz-modal-confirm-container',
      '.ant-modal-confirm',
      'nz-modal-confirm-container',
      '.ant-modal-mask',
      '.cdk-overlay-backdrop',
      '.ant-modal'
    ];

    for (const selector of modalSelectors) {
      try {
        const modals = await driver.findElements(By.css(selector));
        for (const modal of modals) {
          try {
            if (excludeSelector && (await modal.getAttribute('class')).includes(excludeSelector.split('.')[1])) {
              continue;
            }
            if (await modal.isDisplayed()) {
              // Try multiple approaches to close the modal
              await driver.executeScript('arguments[0].style.display = "none";', modal);
              await driver.executeScript('arguments[0].style.visibility = "hidden";', modal);
              await driver.executeScript('arguments[0].remove();', modal);
            }
          } catch (err) {
            // Ignore stale element errors when closing modals
            if (err.name !== 'StaleElementReferenceError') {
              console.warn('Error closing individual modal:', err.message);
            }
          }
        }
      } catch (err) {
        console.warn(`Error finding modals with selector ${selector}:`, err.message);
      }
    }

    // Also try to close any modal by pressing Escape key
    try {
      await driver.actions().sendKeys(Key.ESCAPE).perform();
      await driver.sleep(500);
    } catch (err) {
      console.warn('Error sending Escape key:', err.message);
    }

  } catch (err) {
    console.warn('Error in closeAllModals:', err.message);
  }
}

async function waitForMessage(driver, expected, timeout = 15000) {
  return driver.wait(async () => {
    const elements = await driver.findElements(
      By.css('.ant-notification-notice-message, .ant-message-notice-content, [role="alert"], .notification, .message, .success-message, .toast')
    );

    for (const el of elements) {
      try {
        const text = await el.getText();
        if (text && text.includes(expected)) {
          return true;
        }
      } catch (err) {
        if (err.name === 'StaleElementReferenceError') {
          continue;
        } else {
          throw err;
        }
      }
    }
    return false;
  }, timeout, `Did not see message "${expected}" within ${timeout}ms`);
}


async function retryOnStaleElement(driver, action, maxAttempts = 3) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await action();
    } catch (error) {
      const isStaleError = error.name === 'StaleElementReferenceError' ||
        error.message.includes('stale element reference') ||
        error.message.includes('element not found in the current frame');

      if (isStaleError && attempt < maxAttempts) {
        console.log(`Attempt ${attempt} failed with stale element error, retrying...`);
        await driver.sleep(1000 * attempt); // Progressive delay: 1s, 2s, 3s, etc.
        continue;
      }
      throw error;
    }
  }
}

async function safeClick(driver, locator, buttonText = '', maxAttempts = 5) {
  return await retryOnStaleElement(driver, async () => {
    const element = await driver.wait(until.elementLocated(locator), 20000, `Timeout waiting for element`);

    await driver.wait(until.elementIsVisible(element), 5000);
    await driver.wait(until.elementIsEnabled(element), 5000);
    await driver.executeScript('arguments[0].scrollIntoView(true);', element);

    // Small delay to allow DOM to stabilize
    await driver.sleep(500);

    // Verify button text if provided
    if (buttonText) {
      const elementText = await element.getText();
      if (!elementText.toLowerCase().includes(buttonText.toLowerCase())) {
        throw new Error(`Expected element text "${buttonText}", but found "${elementText}"`);
      }
    }

    try {
      await element.click();
    } catch (clickErr) {
      if (clickErr.name === 'ElementClickInterceptedError') {
        await driver.executeScript('arguments[0].click();', element);
      } else {
        throw clickErr;
      }
    }
  }, maxAttempts);
}

module.exports = { waitForPageLoad, closeAllModals, retry, waitForMessage, retryOnStaleElement, safeClick };
