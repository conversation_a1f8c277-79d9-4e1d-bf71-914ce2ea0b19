Feature: Organizations page
  As a user,
  I want to view, search, add, and edit organizations,
  So that I can manage organization data effectively and ensure changes persist.
  
Scenario: Display the organizations page title
  Given I am on the home page
  Then I should see the login page with the title "Login" in an h2 tag
  When I click the "Synapse SSO" button with id "btn-keycloak"
  And I complete the Keycloak login form with username "<EMAIL>" and password "puppies1234"
  Then I should be redirected back to the application
  Given I am on the organizations page
  Then I see the page title "All Organizations"
  Then I see the organization list with at least one record
  And the first organization's name is not empty
  When I click the add organization button
  And I fill in the organization name "New Test Organization" and description "Organization Description"
  And I submit the new organization form
  Then I see "New Test Organization" in the organization list
  When I click the edit button of the first organization
  And I change the organization name to "Updated Test Org"
  And I submit the edit form
  Then I see "Updated Test Org" in the organization list
  When I check the database for "Test Organization 2"
  Then the database contains "Test Organization 2"
