Feature: Home page
  As a user
  I want to see the Onramp landing page
  So that I know the app is up and running

  Scenario: Visit the home page
    Given I open the home page
    Then I should see the login page with the title "Login" in an h2 tag
    When I click the "Synapse SSO" button with id "btn-keycloak"
    And I complete the Keycloak login form with username "<EMAIL>" and password "puppies1234"
    Then I should be redirected back to the application
    Then I should see the title "Home"
