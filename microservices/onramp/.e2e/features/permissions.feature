Feature: Permissions Management
  As a user,
  I want to manage roles and permissions,
  So that I can effectively control access within an organization.
  
  Scenario: Access Permissions page from Organizations
    Given I am on the home page
    Then I should see the login page with the title "Login" in an h2 tag
    When I click the "Synapse SSO" button with id "btn-keycloak"
    And I complete the Keycloak login form with username "<EMAIL>" and password "puppies1234"
    Then I should be redirected back to the application
    When I am on the organizations page
    Then I see the page title "All Organizations"
    And I click the "Permissions" button with id "btn-permission" in that row
    Then I am redirected to the "Permissions" page
    And I see the page title "Roles and Permissions" with id "title-roles-permissions"
    And the table contains at least one record
    When I click the button with id "btn-create-role-modal"
    Then I see a modal with class "modal-create-role"
    And I see the modal title "Create Role"
    When I fill in the name field with "create role test"
    When I fill in the description field with "test add description"
    And I click the button with id "btn-create-role"
    Then I see the message "Role created successfully!"
    And the table with id "permissions-list" contains "create role test"
