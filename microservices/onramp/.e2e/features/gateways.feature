Feature: Gateways Screen Functionality
  As a user,
  I want to interact with the Gateways screen to view, and check details of gateways.
  
  Scenario: Verify Gateways screen displays the list of gateways
    Given I am on the home page
    Then I should see the login page with the title "Login" in an h2 tag
    When I complete the login form with username "<EMAIL>" and password "puppies1234"
    Then I should be redirected back to the application
    Given I am on the gateways page
    Then I see the page title gateways
    And the first gateways name is not empty
    When I click the add gateways button
    When I fill in the gateway name "New Test Gateway" and machine key "tesd5c29fc65d7dc9e0798f68e4" and description "Gateways Description"
    And I submit the create gateway form
    Then I see "New Test Gateway" in the gateways list
    When I click the edit button of the first gateway
    And I change the gateway name to "Updated Test Gateway"
    And I submit the edit gateway form
    When I click the delete button of the first gateway
    Then I click the delete button in modal
    And I confirm the deletion gateway 