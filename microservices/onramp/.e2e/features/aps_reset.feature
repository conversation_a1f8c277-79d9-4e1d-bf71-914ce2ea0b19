Feature: APS Reset Management
  As a user,
  I want to input a challenge code and receive a response code
  So that I can reset APS for an organization

Scenario: Display the APS Reset page title
    Given I am on the home page
    Then I should see the login page with the title "Login" in an h2 tag
    When I complete the login form with username "<EMAIL>" and password "puppies1234"
    Then I should be redirected back to the application
    When I am on the aps reset page
    When the user enters a valid challenge code "12345678"
    And the user confirms the action on the pop-up
    Then the user will see the response code "87654321"