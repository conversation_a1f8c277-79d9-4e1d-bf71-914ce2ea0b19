const { When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const assert = require('assert');
const { waitForPageLoad } = require('../support/utils');
const fs = require('fs').promises;
async function retryOnStaleElement(driver, action, maxAttempts = 3) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await action();
    } catch (error) {
      if (error.name === 'StaleElementReferenceError' && attempt < maxAttempts) {
        await driver.sleep(500);
        continue;
      }
      throw error;
    }
  }
}

async function safeClick(driver, locator, timeout = 10000) {
  const end = Date.now() + timeout;
  while (Date.now() < end) {
    try {
      const el = await driver.findElement(locator);
      await driver.wait(until.elementIsVisible(el), 2000);
      await driver.wait(until.elementIsEnabled(el), 2000);
      await el.click();
      return;
    } catch (err) {
      if (err.name !== 'StaleElementReferenceError') throw err;
    }
  }
  throw new Error(`Could not click element ${locator} within ${timeout}ms`);
}


Then('The Users In Organization table contains at least one record', async function () {
  await waitForPageLoad(this.driver, 15000);
  await retryOnStaleElement(this.driver, async () => {
    await this.driver.wait(until.elementLocated(By.css('#users-list')), 10000);
    const rows = await this.driver.findElements(By.css('#users-list tr'));
    if (rows.length <= 1) {
      throw new Error('No records found in the permissions table');
    }
  });
});

When('I fill in the email field with {string}', async function (name) {
  await waitForPageLoad(this.driver, 15000);
  await retryOnStaleElement(this.driver, async () => {
    const nameField = await this.driver.wait(until.elementLocated(By.id('input-user-email')), 10000);
    const nameEl = await this.driver.wait(until.elementIsVisible(nameField), 5000);
    await nameEl.clear();
    await nameEl.sendKeys(name);
  });
  await this.driver.switchTo().defaultContent();
});

When('I fill in the message field with {string}', async function (description) {
  await waitForPageLoad(this.driver, 15000);
  await retryOnStaleElement(this.driver, async () => {
    const descriptionField = await this.driver.wait(until.elementLocated(By.id('input-user-message')), 10000);
    const descriptionEl = await this.driver.wait(until.elementIsVisible(descriptionField), 5000);
    await descriptionEl.clear();
    await descriptionEl.sendKeys(description);
  });
  await this.driver.switchTo().defaultContent();
});

When('I find the row with name {string} and click the Users button', async function (orgName) {
  try {
    await waitForPageLoad(this.driver, 15000);

    await retryOnStaleElement(this.driver, async () => {
      const row = await this.driver.wait(until.elementLocated(By.xpath(`//tr[contains(.,'${orgName}')]`)), 20000);
      const usersButton = await row.findElement(By.id('btn-users'));
      const isVisible = await usersButton.isDisplayed();
      const isEnabled = await usersButton.isEnabled();
      if (!isVisible || !isEnabled) throw new Error('Users button not visible or enabled');

      await this.driver.executeScript('arguments[0].scrollIntoView(true);', usersButton);
      await usersButton.click();
    });
  } catch (error) {
    throw new Error(`Error in finding row with name "${orgName}" and clicking Users button: ${error.message}`);
  }
});

When('I confirm the deletion', async function () {
  const okButton = await this.driver.findElement(By.xpath("//button[contains(@class, 'ant-btn-primary') and contains(., 'OK')]"));
  await okButton.click();
});

Then('the first invitations name is not empty', async function () {
  await this.driver.wait(until.elementLocated(By.id('invitations-list')), 30000);
  const invitationRows = await this.driver.findElements(By.css('#invitations-list .ant-table-row'));
  if (invitationRows.length === 0) {
    throw new Error('No invitations found in the list. Table shows "No Data".');
  }
  const firstOrgRoleElement = await this.driver.wait(
    until.elementLocated(By.css('#invitations-list .ant-table-row:first-child td:nth-child(1)')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(firstOrgRoleElement), 15000);
  const firstOrgRoleText = await firstOrgRoleElement.getText();
  if (!firstOrgRoleText.trim()) {
    throw new Error('First invitations orgRole is empty');
  }
});

When('I click the delete button with id {string}', async function (buttonId) {
  const locator = By.css(`.ant-table-row:first-child #${buttonId}`);

  let deleteButton;

  await this.driver.wait(until.elementLocated(locator), 20000, `Delete button with ID ${buttonId} not found`);

  for (let attempt = 0; attempt < 3; attempt++) {
    try {
      deleteButton = await this.driver.findElement(locator);

      await this.driver.wait(until.elementIsVisible(deleteButton), 5000);
      await this.driver.wait(until.elementIsEnabled(deleteButton), 5000);

      await this.driver.executeScript('arguments[0].scrollIntoView(true);', deleteButton);
      await deleteButton.click();

      return;
    } catch (err) {
      if (err.name === 'StaleElementReferenceError' && attempt < 2) {
        console.warn(`Stale element, retrying... (${attempt + 1})`);
        await this.driver.sleep(500);
        continue;
      }
      throw err;
    }
  }
});

Then('I click the delete user button in modal', async function () {
  const modal = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal')),
    15000,
    'Timeout waiting for delete confirmation modal'
  );
  await this.driver.wait(until.elementIsVisible(modal), 5000);
  const okButton = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal-confirm .ant-btn-primary')),
    10000,
    'Timeout waiting for OK button in modal'
  );
  await this.driver.wait(until.elementIsVisible(okButton), 5000);
  await this.driver.executeScript('arguments[0].click();', okButton);
  console.log('Delete button in modal clicked'); // Debug
});
Then('the confirmation modal should be displayed', async function () {
  const modal = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal')),
    15000,
    'Confirmation modal not found'
  );
  await this.driver.wait(until.elementIsVisible(modal), 10000, 'Confirmation modal not visible');
  const isDisplayed = await modal.isDisplayed();
  assert.strictEqual(isDisplayed, true, 'Confirmation modal is not displayed');
});
