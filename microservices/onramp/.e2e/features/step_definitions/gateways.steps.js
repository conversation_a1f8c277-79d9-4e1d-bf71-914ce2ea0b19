const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const { waitForPageLoad } = require('../support/utils');
const path = require('path');
const fs = require('fs').promises;

let gatewayCounter = 0;
function getNextUniqueString(baseName) {
  gatewayCounter += 1;
  return `${baseName}${gatewayCounter}`;
}

function generateNextMachineKey(baseMachineKey) {
  const extraChar = Math.random().toString(36).charAt(2);
  return baseMachineKey + extraChar;
}

async function retryOnStaleElement(driver, fn, maxAttempts = 3, timeout = 45000) {
  let lastError;
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await driver.wait(fn, timeout);
    } catch (error) {
      lastError = error;
      if (error.name === 'StaleElementReferenceError' || error.name === 'NoSuchElementError') {
        await driver.sleep(2000);
      } else {
        throw error;
      }
    }
  }
  throw lastError;
}

When('I complete the login form with username {string} and password {string}', async function (username, password) {
  await waitForPageLoad(this.driver, 20000);

  let usernameField = await this.driver.wait(until.elementLocated(By.id("username")), 10000)
  let userNameEl = await this.driver.wait(until.elementIsVisible(usernameField), 3000);
  await userNameEl.clear();
  await userNameEl.sendKeys(username);
  let passwordField = await this.driver.wait(until.elementLocated(By.id("password")), 10000)
  let passwordEl = await this.driver.wait(until.elementIsVisible(passwordField), 3000);

  await passwordEl.clear();
  await passwordEl.sendKeys(password);

  let submitButton = await this.driver.wait(until.elementLocated(By.id("kc-login")), 10000)
  await this.driver.wait(until.elementIsVisible(submitButton), 3000);
  await this.driver.wait(until.elementIsEnabled(submitButton), 5000);
  await this.driver.actions()
    .move({ origin: submitButton })
    .click()
    .perform();

  let logoutElm = await this.driver.wait(until.elementLocated(By.id("btn-logout")), 20000)
  await this.driver.wait(until.elementIsVisible(logoutElm), 10000);

});

Given('I am on the gateways page', async function () {
  await waitForPageLoad(this.driver, 20000);
  let selectButton = await this.driver.wait(until.elementLocated(By.id("select-gateway0")), 15000);
  let menuItem = await this.driver.wait(until.elementIsVisible(selectButton), 15000);
  await menuItem.click();
  if (typeof this.waitForAngularStable === 'function') {
    await this.waitForAngularStable();
  }
  await waitForPageLoad(this.driver, 20000);
});

Then('I see the page title gateways', async function () {
  await waitForPageLoad(this.driver, 20000);
  await this.driver.wait(until.elementLocated(By.id('title-gateways')));
});

Then('the first gateways name is not empty', async function () {
  const firstOrgNameElement = await this.driver.wait(
    until.elementLocated(By.css('#gateways-list .ant-table-row:first-child td:first-child span[nz-tooltip]')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(firstOrgNameElement), 15000);
  const firstOrgName = await firstOrgNameElement.getText();
  if (!firstOrgName.trim()) {
    throw new Error('First gateways name is empty');
  }
});

When('I click the add gateways button', async function () {
  const addButton = await this.driver.wait(
    until.elementLocated(By.css('#btn-create-gateways')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(addButton), 5000);
  await this.driver.wait(until.elementIsEnabled(addButton), 5000);
  await addButton.click();

  const modal = await this.driver.wait(
    until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(modal), 10000);
});
When('I fill in the gateway name {string} and machine key {string} and description {string}', async function (baseGatewayName, baseMachineKey, description) {
  try {
    const gatewayName = getNextUniqueString(baseGatewayName);
    let machineKey = generateNextMachineKey(baseMachineKey);

    const modal = await this.driver.wait(
      until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')),
      20000,
      `Modal not found for gatewayName: ${gatewayName}`
    );
    await this.driver.wait(until.elementIsVisible(modal), 10000, `Modal not visible for gatewayName: ${gatewayName}`);

    const nameInput = await retryOnStaleElement(this.driver, async () => {
      return await this.driver.wait(
        until.elementLocated(
          By.css('.cdk-overlay-pane .ant-modal input[id="input-gateway"], .cdk-overlay-pane .ant-modal input[name="name"]')
        ),
        20000,
        `Name input not found for gatewayName: ${gatewayName}`
      );
    });
    await this.driver.wait(until.elementIsVisible(nameInput), 10000, `Name input not visible for gatewayName: ${gatewayName}`);
    await nameInput.clear();
    await this.driver.sleep(500);
    await nameInput.sendKeys(gatewayName);

    const machineKeyInputField = await retryOnStaleElement(this.driver, async () => {
      return await this.driver.wait(
        until.elementLocated(By.id('input-machinekey')),
        20000,
        `Machine key input not found for gatewayName: ${gatewayName}`
      );
    });
    await this.driver.wait(until.elementIsVisible(machineKeyInputField), 10000, `Machine key input not visible for gatewayName: ${gatewayName}`);
    await machineKeyInputField.clear();
    await this.driver.sleep(500);
    await machineKeyInputField.sendKeys(machineKey);

    const descriptionInput = await retryOnStaleElement(this.driver, async () => {
      return await this.driver.wait(
        until.elementLocated(
          By.css('.cdk-overlay-pane .ant-modal input[id="input-description"], .cdk-overlay-pane .ant-modal input[name="description"]')
        ),
        20000,
        `Description input not found for gatewayName: ${gatewayName}`
      );
    });
    await this.driver.wait(until.elementIsVisible(descriptionInput), 10000, `Description input not visible for gatewayName: ${gatewayName}`);
    await descriptionInput.clear();
    await this.driver.sleep(500);
    await descriptionInput.sendKeys(description);

    this.gatewayName = gatewayName;
    this.machineKey = machineKey;
  } catch (error) {
    throw new Error(`Error in fill step for gatewayName "${baseGatewayName}": ${error.message}`);
  }
});

When('I submit the create gateway form', async function () {
  try {
    const submitButton = await this.driver.wait(
      until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button.ant-btn-primary')),
      15000,
      'Submit button not found'
    );
    await this.driver.wait(until.elementIsEnabled(submitButton), 5000, 'Submit button not enabled');
    await this.driver.executeScript('arguments[0].click();', submitButton);
    await this.driver.wait(until.stalenessOf(submitButton), 20000, 'Modal did not close after submit');
    const gatewaysList = await this.driver.wait(
      until.elementLocated(By.id('gateways-list')),
      30000,
      'Gateways list not found after submit'
    );
    await this.driver.wait(until.elementIsVisible(gatewaysList), 10000, 'Gateways list not visible');
    let attempts = 0;
    const maxAttempts = 3;
    while (attempts < maxAttempts) {
      try {
        await this.driver.wait(
          async () => {
            const nameCells = await this.driver.findElements(
              By.css('tbody tr td.ant-table-cell:first-child span')
            );
            for (let el of nameCells) {
              const txt = (await el.getText()).trim();
              if (txt === this.gatewayName) {
                return true;
              }
            }
            return false;
          },
          10000,
          `Gateway "${this.gatewayName}" not found in list after submit (attempt ${attempts + 1})`
        );
        break;
      } catch (retryError) {
        attempts++;
        if (attempts === maxAttempts) {
          throw retryError;
        }
        await this.driver.sleep(2000);
      }
    }
  } catch (error) {
    throw new Error(`Error in submit step: ${error.message}`);
  }
});

Then('I see {string} in the gateways list', async function (baseGatewayName) {
  let attempts = 0;
  const maxAttempts = 3;
  try {
    const gatewaysList = await this.driver.wait(
      until.elementLocated(By.id('gateways-list')),
      30000,
      `Gateways list element not found in DOM (attempt ${attempts + 1})`
    );
    await this.driver.wait(until.elementIsVisible(gatewaysList), 10000, `Gateways list not visible (attempt ${attempts + 1})`);

    let found = false;
    await this.driver.wait(
      async () => {
        const nameCells = await this.driver.findElements(
          By.css('tbody tr td.ant-table-cell:first-child span')
        );
        const names = [];
        for (let el of nameCells) {
          const txt = (await el.getText()).trim();
          names.push(txt);
          if (txt === this.gatewayName) {
            const isVisible = await el.isDisplayed();
            if (isVisible) {
              found = true;
              return true;
            }
          }
        }
        return false;
      },
      15000,
      `Did not find "${this.gatewayName}" in gateways list`
    );

    if (!found) {
      throw new Error(`Expected to see "${this.gatewayName}" but not found`);
    }
  } catch (error) {
    throw new Error(`Error in checking gateways list for "${this.gatewayName}": ${error.message}`);
  }
});

When('I click the edit button of the first gateway', async function () {
  await this.driver.wait(until.elementLocated(By.id('gateways-list')), 30000);
  const editButton = await this.driver.wait(
    until.elementLocated(By.css('#gateways-list .ant-table-row:first-child td.btn-action button.ant-btn-primary')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(editButton), 15000);
  await editButton.click();
  const modal = await this.driver.wait(until.elementLocated(By.css('.ant-modal')), 15000);
  let isVisible = await modal.isDisplayed();
  let attempts = 0;
  while (!isVisible && attempts < 3) {
    await this.driver.sleep(2000);
    isVisible = await modal.isDisplayed();
    attempts++;
  }
  if (!isVisible) {
    throw new Error('Modal is not visible after waiting');
  }
});

When('I change the gateway name to {string}', async function (newName) {
  const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
  let isVisible = await modal.isDisplayed();
  let modalAttempts = 0;
  while (!isVisible && modalAttempts < 3) {
    await this.driver.sleep(2000);
    isVisible = await modal.isDisplayed();
    modalAttempts++;
  }
  if (!isVisible) {
    throw new Error('Modal is not visible after waiting');
  }
  const nameInput = await this.driver.wait(
    until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="input-gateway"], .cdk-overlay-pane .ant-modal input[name="name"]')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(nameInput), 5000);
  await nameInput.clear();
  await nameInput.sendKeys(newName);
});

When('I submit the edit gateway form', async function () {
  const submitButton = await this.driver.wait(
    until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button[nzType="primary"]:not([nzType="default"])')),
    15000
  );
  await this.driver.wait(until.elementIsEnabled(submitButton), 5000);
  await submitButton.click();
  await this.driver.sleep(3000);
  await this.driver.wait(until.elementLocated(By.id('gateways-list')), 20000);
});
When('I click the delete button of the first gateway', async function () {
  let attempts = 0;
  const maxAttempts = 3;
  try {
    const gatewaysList = await this.driver.wait(
      until.elementLocated(By.id('gateways-list')),
      20000,
      'Gateways list not found in DOM'
    );
    await this.driver.wait(until.elementIsVisible(gatewaysList), 10000, 'Gateways list not visible');
    const editButton = await this.driver.wait(
      until.elementLocated(
        By.css('#gateways-list .ant-table-row:first-child td.btn-action button.ant-btn-primary')
      ),
      15000,
      'Edit button not found for first gateway'
    );
    await this.driver.wait(until.elementIsVisible(editButton), 10000, 'Edit button not visible');
    await this.driver.wait(until.elementIsEnabled(editButton), 5000, 'Edit button not enabled');
    await this.driver.executeScript('arguments[0].click();', editButton);

    const editModal = await this.driver.wait(
      until.elementLocated(By.css('.ant-modal')),
      15000,
      'Edit modal not found'
    );
    await this.driver.wait(until.elementIsVisible(editModal), 10000, 'Edit modal not visible');

    const deleteButton = await this.driver.wait(
      until.elementLocated(By.id('btn-delete-gateway')),
      15000,
      'Delete button not found'
    );
    await this.driver.wait(until.elementIsVisible(deleteButton), 10000, 'Delete button not visible');
    await this.driver.wait(until.elementIsEnabled(deleteButton), 5000, 'Delete button not enabled');
    while (attempts < maxAttempts) {
      try {
        await this.driver.executeScript('arguments[0].click();', deleteButton);
        return;
      } catch (clickError) {
        attempts++;
        if (attempts === maxAttempts) {
          throw new Error(`Failed to click delete button after ${maxAttempts} attempts: ${clickError.message}`);
        }
        await this.driver.sleep(2000);
      }
    }
  } catch (error) {
    throw new Error(`Error in delete step: ${error.message}`);
  }
});

Then('I click the delete button in modal', async function () {
  await this.driver.wait(until.elementLocated(By.css('.ant-modal-confirm')), 15000);
  const confirmDeleteButton = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal-confirm .ant-btn-primary')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(confirmDeleteButton), 15000);
  await confirmDeleteButton.click();
  const confirmModal = await this.driver.wait(until.elementLocated(By.css('.ant-modal-confirm')), 15000);
  let isVisible = await confirmModal.isDisplayed().catch(() => false);
  let attempts = 0;
  while (isVisible && attempts < 3) {
    await this.driver.sleep(2000);
    isVisible = await confirmModal.isDisplayed().catch(() => false);
    attempts++;
  }
  if (isVisible) {
    throw new Error('Confirmation modal is still visible after clicking delete button');
  }
});

Then('I confirm the deletion gateway', async function () {
  await waitForPageLoad(this.driver, 20000);
  const modals = await this.driver.findElements(By.css('.ant-modal-confirm'));
  if (modals.length > 0) {
    const isVisible = await modals[0].isDisplayed();
    if (isVisible) {
      throw new Error('Confirmation modal is still visible after deletion');
    }
  }
});
