const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const { waitForPageLoad } = require('../support/utils');

async function ensureOnRegisterPage(driver) {
  const baseUrl = process.env.BASE_URL || 'http://localhost:4200';
  const currentUrl = await driver.getCurrentUrl();
  if (!currentUrl.startsWith(`${baseUrl}/account/register`)) {
    await driver.get(`${baseUrl}/account/register`);
  }
  await waitForPageLoad(driver, 20000);
}

Given('I navigate to {string}', async function (pathSegment) {
  const baseUrl = process.env.BASE_URL || 'http://localhost:4200';
  await this.driver.get(`${baseUrl}${pathSegment}`);
  await waitForPageLoad(this.driver, 20000);
});

When('I fill the registration form with valid user data', async function () {
  await waitForPageLoad(this.driver, 20000);
  const ts = Date.now();
  const first = `Test${ts}`;
  const last = `User${ts}`;
  const email = `test${ts}@example.com`;
  const username = `testuser${ts}`;
  const password = `Aa1!${ts}`;

  const firstnameEl = await this.driver.wait(until.elementLocated(By.id('firstname')), 10000);
  await this.driver.wait(until.elementIsVisible(firstnameEl), 5000);
  await firstnameEl.clear();
  await firstnameEl.sendKeys(first);

  const lastnameEl = await this.driver.wait(until.elementLocated(By.id('lastname')), 10000);
  await this.driver.wait(until.elementIsVisible(lastnameEl), 5000);
  await lastnameEl.clear();
  await lastnameEl.sendKeys(last);

  const emailEl = await this.driver.wait(until.elementLocated(By.id('email')), 10000);
  await this.driver.wait(until.elementIsVisible(emailEl), 5000);
  await emailEl.clear();
  await emailEl.sendKeys(email);

  const usernameEl = await this.driver.wait(until.elementLocated(By.id('username')), 10000);
  await this.driver.wait(until.elementIsVisible(usernameEl), 5000);
  await usernameEl.clear();
  await usernameEl.sendKeys(username);

  const passwordEl = await this.driver.wait(until.elementLocated(By.id('password')), 10000);
  await this.driver.wait(until.elementIsVisible(passwordEl), 5000);
  await passwordEl.clear();
  await passwordEl.sendKeys(password);

  const confirmEl = await this.driver.wait(until.elementLocated(By.id('confirmPassword')), 10000);
  await this.driver.wait(until.elementIsVisible(confirmEl), 5000);
  await confirmEl.clear();
  await confirmEl.sendKeys(password);

  this.lastRegistration = { first, last, email, username, password };
});

When('I submit the registration form', async function () {
  const submit = await this.driver.wait(until.elementLocated(By.id('btn-submit')), 10000);
  await this.driver.wait(until.elementIsEnabled(submit), 10000);
  await this.driver.executeScript('arguments[0].scrollIntoView(true);', submit);
  try {
    await submit.click();
  } catch (err) {
    await this.driver.executeScript('arguments[0].click();', submit);
  }
});

Then('I should see a success modal with the title {string}', async function (expectedTitle) {
  const modal = await this.driver.wait(
    until.elementLocated(By.css('.register-modal .ant-modal-body')),
    30000,
    'Timed out waiting for success modal'
  );
  await this.driver.wait(until.elementIsVisible(modal), 10000);

  const titleEl = await this.driver.findElement(By.css('.register-modal .ant-modal-body .modal-title-center div:nth-child(2)'));
  const text = await titleEl.getText();
  if (!text || text.trim() !== expectedTitle) {
    throw new Error(`Expected success modal title "${expectedTitle}", but found "${text}"`);
  }
});

When('I click the {string} button on the modal', async function (buttonText) {
  const okBtn = await this.driver.wait(
    until.elementLocated(By.css('nz-modal-confirm-container button')),
    15000,
    `Timed out waiting for ${buttonText} button`
  );
  await this.driver.wait(until.elementIsVisible(okBtn), 15000);
  const text = await okBtn.getText();
  if (!text.toLowerCase().includes(buttonText.toLowerCase())) {
    console.warn(`Modal button text mismatch: expected contains ${buttonText}, got ${text}`);
  }
  try {
    await okBtn.click();
  } catch (err) {
    await this.driver.executeScript('arguments[0].click();', okBtn);
  }
});

Then('I should see a logout button', async function () {
  const logoutBtn = await this.driver.wait(
    until.elementLocated(By.id('btn-logout')),
    30000,
    'Timed out waiting for logout button after registration flow'
  );
  await this.driver.wait(until.elementIsVisible(logoutBtn), 10000);
});


// ---------------------- Validation steps ----------------------

When('I focus and blur the {string} field', async function (fieldId) {
  await ensureOnRegisterPage(this.driver);
  const el = await this.driver.wait(until.elementLocated(By.id(fieldId)), 10000);
  await this.driver.wait(until.elementIsVisible(el), 5000);
  await el.click();
  // Type some text
  await el.sendKeys('x');
  // Manually select all and delete to ensure field is cleared
  const { Key } = require('selenium-webdriver');
  await el.sendKeys(Key.chord(Key.CONTROL, 'a'), Key.BACK_SPACE);
  // Blur the field
  await this.driver.executeScript('arguments[0].blur();', el);
});

When('I enter {string} into the {string} field', async function (value, fieldId) {
  await ensureOnRegisterPage(this.driver);
  const el = await this.driver.wait(until.elementLocated(By.id(fieldId)), 10000);
  await this.driver.wait(until.elementIsVisible(el), 5000);
  await el.clear();
  await el.sendKeys(value);
});

When('I blur the {string} field', async function (fieldId) {
  await ensureOnRegisterPage(this.driver);
  const el = await this.driver.wait(until.elementLocated(By.id(fieldId)), 10000);
  await this.driver.wait(until.elementIsVisible(el), 5000);
  await this.driver.executeScript('arguments[0].blur();', el);
});

Then('I should see the validation message {string}', async function (messageText) {
  /*
  const els = await this.driver.wait(until.elementLocated(By.css('nz-form-item nz-form-control .ant-form-item-explain-error')),
    15000,
    `Timed out waiting for validation message "${messageText}"`
  );
  let found = false;
  let txts = [];
  if (Array.isArray(els)) {
    for (const el of els) {
      const txt = await el.getText();
      txts.push(txt);
      if (txt && txt.includes(messageText)) {
        found = true;
        break;
      }
    }
  } else {
    // If els is a single element, check it directly
    const txt = await els.getText();
    txts.push(txt);
    if (txt && txt.includes(messageText)) {
      found = true;
    }
  }
  if (!found) {
    throw new Error(`Expected validation message "${messageText}" not found. Found: "${txts.join(', ')}" instead.`);
  }
  */

  // Find all elements that could contain the error message
  const errorEls = await this.driver.findElements(By.css('.ant-form-item-explain-error'));
  let foundVisible = false;
  let visibleTxts = [];
  for (const errorEl of errorEls) {
    const isDisplayed = await errorEl.isDisplayed();
    const txt = await errorEl.getText();
    visibleTxts.push(txt);
    if (isDisplayed && txt && txt.includes(messageText)) {
      foundVisible = true;
      break;
    }
  }
  if (!foundVisible) {
    throw new Error(`Expected validation message "${messageText}" not visible. Got: "${visibleTxts.join(', ')}"`);
  }
});

