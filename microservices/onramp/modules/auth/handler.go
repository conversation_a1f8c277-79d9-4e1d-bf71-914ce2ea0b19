package auth

import (
	"context"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	"github.com/gorilla/mux"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/data"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/modules/auth/session"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/util"
)

// OAuth2TokenExchanger defines the interface for OAuth2 token exchange operations
type OAuth2TokenExchanger interface {
	Exchange(ctx context.Context, config *OIDCConfig, code string) (*oauth2.Token, error)
	VerifyIDToken(ctx context.Context, config *OIDCConfig, rawID string) (*oidc.IDToken, error)
	ExtractClaims(idToken *oidc.IDToken) (map[string]any, error)
}

// OAuth2TokenExchangerImpl implements OAuth2TokenExchanger
type OAuth2TokenExchangerImpl struct{}

// NewOAuth2TokenExchanger creates a new OAuth2 token exchanger
func NewOAuth2TokenExchanger() OAuth2TokenExchanger {
	return &OAuth2TokenExchangerImpl{}
}

// Exchange exchanges the authorization code for a token
func (e *OAuth2TokenExchangerImpl) Exchange(ctx context.Context, config *OIDCConfig, code string) (*oauth2.Token, error) {
	return config.OAuth2Config.Exchange(ctx, code)
}

// VerifyIDToken verifies and parses the ID token
func (e *OAuth2TokenExchangerImpl) VerifyIDToken(ctx context.Context, config *OIDCConfig, rawID string) (*oidc.IDToken, error) {
	return config.Verifier.Verify(ctx, rawID)
}

// ExtractClaims extracts claims from the ID token
func (e *OAuth2TokenExchangerImpl) ExtractClaims(idToken *oidc.IDToken) (map[string]any, error) {
	var claims map[string]any
	err := idToken.Claims(&claims)
	return claims, err
}

// Handler handles authentication-related HTTP requests
type Handler struct {
	authService    AuthService
	sessionStore   domain.SessionStore
	tokenExchanger OAuth2TokenExchanger
}

// NewHandler creates a new auth handler instance
func NewHandler(authService AuthService, sessionStore domain.SessionStore) *Handler {
	return &Handler{
		authService:    authService,
		sessionStore:   sessionStore,
		tokenExchanger: NewOAuth2TokenExchanger(),
	}
}

// NewHandlerWithDependencies creates a new auth handler with custom dependencies (for testing)
func NewHandlerWithDependencies(
	authService AuthService,
	sessionStore domain.SessionStore,
	tokenExchanger OAuth2TokenExchanger,
) *Handler {
	return &Handler{
		authService:    authService,
		sessionStore:   sessionStore,
		tokenExchanger: tokenExchanger,
	}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	router.Methods(http.MethodPost).Path("/login").HandlerFunc(h.BasicAuth)
	router.Methods(http.MethodPost).Path("/register").HandlerFunc(h.Register)
	router.Methods(http.MethodGet).Path("/login").HandlerFunc(h.OAuth2Login)
	router.Methods(http.MethodGet).Path("/callback").HandlerFunc(h.OAuth2Callback)
	router.Methods(http.MethodGet).Path("/logout").HandlerFunc(h.OAuth2Logout)
}

// BasicAuth handles POST /login requests
// @Summary User login
// @Description Authenticate user with username and password
// @Tags env:dev, env:qa, authentication
// @Accept application/x-www-form-urlencoded
// @Produce application/json
// @Param username formData string true "Username"
// @Param password formData string true "Password"
// @Success 200 {object} response.SuccessResponse "Login successful"
// @Failure 401 {object} response.UnauthorizedResponse "Unauthorized"
// @Failure 400 {object} response.BadRequestResponse "Bad request"
// @Failure 405 {object} response.MethodNotAllowedResponse "Method not allowed"
// @Router /login [post]
func (h *Handler) BasicAuth(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")
	logger.Debugf("BasicAuth: Starting login for host: %s, isDev: %t", r.Host, isDev)

	// Only allow POST method
	if r.Method != http.MethodPost {
		logger.Warnf("BasicAuth: Invalid method %s", r.Method)
		response.CreateMethodNotAllowedResponse(w)
		return
	}

	// Parse request body
	var req data.BasicAuthRequest
	if err := r.ParseForm(); err != nil {
		response.CreateBadRequestResponse(w)
		return
	}
	req.Username = r.FormValue("username")
	req.Password = r.FormValue("password")

	// Call the auth service
	logger.Debugf("BasicAuth: Attempting authentication for user: %s", req.Username)
	resp, err := h.authService.BasicAuth(r.Context(), &req)
	if err != nil {
		logger.Errorf("error - BasicAuth: %v", err.Error())
		response.CreateUnauthorizedResponse(w)
		return
	}
	logger.Debugf("BasicAuth: Authentication successful for user: %s", req.Username)

	// Create session with user claims
	sessionID := util.RandomString(32)
	logger.Debugf("BasicAuth: Creating session %s for user %s", sessionID, resp.User.ID.String())

	// Create claims with user data (similar to OAuth claims structure)
	claims := map[string]any{
		"sub":   resp.User.ID.String(),
		"name":  resp.User.FirstName + " " + resp.User.LastName,
		"email": resp.Email,
	}

	sessionData := &domain.Session{
		UserID:          resp.User.ID.String(),
		OAuthToken:      nil,
		UserPermissions: resp.UserPermissions,
		Claims:          claims,
	}
	h.sessionStore.SetSession(sessionID, sessionData)

	cookie := &http.Cookie{
		Name:     "session_id",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		SameSite: http.SameSiteStrictMode,
		MaxAge:   session.SessionTTLSeconds,
	}

	logger.Debugf("BasicAuth: Setting cookie - Name: %s, Value: %s, Path: %s, HttpOnly: %t, Secure: %t, SameSite: %v, MaxAge: %d",
		cookie.Name, cookie.Value, cookie.Path, cookie.HttpOnly, cookie.Secure, cookie.SameSite, cookie.MaxAge)

	http.SetCookie(w, cookie)

	response.CreateSuccessResponse(nil, w)
}

// Register handles POST /register requests
// @Summary User registration
// @Description Register a new user account
// @Tags env:dev, env:qa, authentication
// @Accept application/x-www-form-urlencoded
// @Produce application/json
// @Param firstname formData string true "First name"
// @Param lastname formData string true "Last name"
// @Param username formData string true "Username"
// @Param password formData string true "Password"
// @Param email formData string true "Email address"
// @Success 200 {object} response.SuccessResponse "Registration successful"
// @Failure 400 {object} response.BadRequestResponse "Bad request"
// @Failure 405 {object} response.MethodNotAllowedResponse "Method not allowed"
// @Failure 500 {object} response.InternalErrorResponse "Internal server error"
// @Router /register [post]
func (h *Handler) Register(w http.ResponseWriter, r *http.Request) {
	// Only allow POST method
	if r.Method != http.MethodPost {
		response.CreateMethodNotAllowedResponse(w)
		return
	}

	// Parse request body
	var req data.RegisterRequest
	if err := r.ParseForm(); err != nil {
		response.CreateBadRequestResponse(w)
		return
	}
	req.FirstName = r.FormValue("firstname")
	req.LastName = r.FormValue("lastname")
	req.Username = r.FormValue("username")
	req.Password = r.FormValue("password")
	req.Email = r.FormValue("email")

	// Call the auth service
	err := h.authService.Register(r.Context(), &req)
	if err != nil {
		logger.Errorf("error - Register: %v", err.Error())
		response.CreateInternalErrorResponse(w)
		return
	}

	// Return success response
	response.CreateSuccessResponse(nil, w)
}

// OAuth2Login handles GET /login requests
// @Summary OAuth2 login redirect
// @Description Redirects user to OAuth2 provider for authentication
// @Tags env:dev, env:qa, authentication
// @Produce application/json
// @Success 302 {string} string "Redirect to OAuth2 provider"
// @Failure 500 {object} response.InternalErrorResponse "Internal server error"
// @Router /login [get]
func (h *Handler) OAuth2Login(w http.ResponseWriter, r *http.Request) {
	h.handleLogin(w, r)
}

// OAuth2Callback handles GET /callback requests
// @Summary OAuth2 callback
// @Description Handles OAuth2 callback from provider with authorization code
// @Tags env:dev, env:qa, authentication
// @Produce application/json
// @Param state query string true "OAuth2 state parameter"
// @Param code query string true "OAuth2 authorization code"
// @Success 302 {string} string "Redirect to application root"
// @Failure 400 {object} response.BadRequestResponse "Bad request - invalid state or missing code"
// @Failure 500 {object} response.InternalErrorResponse "Internal server error"
// @Router /callback [get]
func (h *Handler) OAuth2Callback(w http.ResponseWriter, r *http.Request) {
	ctx := oidc.ClientContext(r.Context(), LocalhostHTTPProxy)
	isDev := strings.HasPrefix(r.Host, "localhost:4200")

	// Verify state cookie
	st, err := r.Cookie("oauth_state")
	if err != nil || r.URL.Query().Get("state") != st.Value {
		logger.Errorf("error - OAuth2Callback: invalid state")
		response.CreateBadRequestResponse(w)
		return
	}

	// Delete the state cookie so it can't be reused
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Secure:   !isDev,
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		SameSite: http.SameSiteLaxMode,
	})

	// Process OAuth2 callback
	loginResp, err := h.processOAuth2CallbackLogic(ctx, r, isDev)
	if err != nil {
		logger.Errorf("error - OAuth2Callback: %v", err.Error())
		response.CreateInternalErrorResponse(w)
		return
	}

	// Create a session with comprehensive session data
	sessionID := util.RandomString(32)

	// Use claims that were already extracted and verified in processOAuth2CallbackLogic
	claims := loginResp.Claims
	if claims == nil {
		logger.Errorf("error - OAuth2Callback: no claims returned from service for user %s", loginResp.User.ID.String())
		response.CreateInternalErrorResponse(w)
		return
	}

	sessionData := &domain.Session{
		UserID:          loginResp.User.ID.String(),
		OAuthToken:      domain.FromOAuth2Token(loginResp.OAuthToken),
		UserPermissions: loginResp.UserPermissions,
		Claims:          claims, // Store extracted claims
	}
	h.sessionStore.SetSession(sessionID, sessionData)

	http.SetCookie(w, &http.Cookie{
		Name:     "session_id",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		SameSite: http.SameSiteStrictMode,
		MaxAge:   session.SessionTTLSeconds,
	})

	logger.Infof("User authenticated: %s (%s)", loginResp.User.FirstName+" "+loginResp.User.LastName, loginResp.User.ID)
	http.Redirect(w, r, "/", http.StatusFound)
}

// processOAuth2CallbackLogic handles the OAuth2 token exchange and user authentication
func (h *Handler) processOAuth2CallbackLogic(ctx context.Context, r *http.Request, isDev bool) (*data.OAuth2CallbackResponse, error) {
	// Choose the OIDC configuration based on the request host
	oidcConfig := getConfig(isDev)

	// Exchange code for token
	token, err := h.tokenExchanger.Exchange(ctx, oidcConfig, r.URL.Query().Get("code"))
	if err != nil {
		return nil, ErrExchangeFailed
	}

	// Verify the ID Token and extract claims
	rawID, ok := token.Extra(domain.IDTokenKey).(string)
	if !ok {
		return nil, ErrMissingIDToken
	}

	idToken, err := h.tokenExchanger.VerifyIDToken(ctx, oidcConfig, rawID)
	if err != nil {
		return nil, ErrInvalidIDToken
	}

	// Extract claims from ID token
	claims, err := h.tokenExchanger.ExtractClaims(idToken)
	if err != nil {
		return nil, ErrInvalidIDToken
	}

	// Get state cookie for validation
	st, err := r.Cookie("oauth_state")
	stateCookie := ""
	if err == nil {
		stateCookie = st.Value
	}

	// Create callback request
	callbackReq := &data.OAuth2CallbackRequest{
		Code:        r.URL.Query().Get("code"),
		State:       r.URL.Query().Get("state"),
		StateCookie: stateCookie,
		Claims:      claims,
		OAuthToken:  token,
	}

	// Process through service layer
	return h.authService.ProcessOAuth2Callback(r.Context(), callbackReq)
}

// OAuth2Logout handles GET /logout requests
// @Summary User logout
// @Description Logs out the current user and clears session
// @Tags env:dev, env:qa, authentication
// @Produce application/json
// @Success 302 {string} string "Redirect to application root"
// @Router /logout [get]
func (h *Handler) OAuth2Logout(w http.ResponseWriter, r *http.Request) {
	h.handleLogout(w, r)
}

var (
	// synapseOIDC is a global variable that holds the OIDC configuration
	// for the Synapse OAuth2 server.
	SynapseOIDC = OIDCConfig{
		ClientID:     os.Getenv("SYNAPSE_OIDC_CLIENT_ID"),
		ClientSecret: os.Getenv("SYNAPSE_OIDC_CLIENT_SECRET"),
		RedirectURL:  os.Getenv("SYNAPSE_OIDC_CLIENT_CALLBACK_URL"),
		IssuerURL:    os.Getenv("SYNAPSE_OIDC_ISSUER_URL"),
	}

	// synapseOIDCLocal is a local version of the OIDC configuration
	// used for development purposes, pointing to keycloak.
	SynapseOIDCLocal OIDCConfig

	SynapseOIDCScopes = []string{oidc.ScopeOpenID, "profile", "email"}

	// localhostHTTPProxy is a custom HTTP client that rewrites requests
	// to "localhost:8091" to "host.docker.internal:8091". This is necessary
	// for the OIDC provider to communicate with the host machine from within
	// a Docker container, as Docker containers cannot directly access services
	// running on the host machine using "localhost".  This is not a security
	// concern in prod, because in production, the request will simply fail
	// because there is no OIDC provider listening there.
	LocalhostHTTPProxy = &http.Client{Transport: &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			if strings.HasSuffix(addr, "localhost:8091") {
				addr = "host.docker.internal:8091"
			}
			return (&net.Dialer{}).DialContext(ctx, network, addr)
		},
	}}
)

// OIDCConfig holds the configuration for OpenID Connect (OIDC) authentication.
type OIDCConfig struct {
	ClientID     string
	ClientSecret string
	RedirectURL  string
	IssuerURL    string
	Provider     *oidc.Provider
	OAuth2Config *oauth2.Config
	Verifier     *oidc.IDTokenVerifier
	Scope        string
}

func (h *Handler) handleLogin(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")
	state := util.RandomString(32)

	// Set the state cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    state,
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		SameSite: http.SameSiteLaxMode,
	})

	// Choose the OIDC configuration based on the request host
	oidcConfig := getConfig(isDev)

	// Redirect to the OIDC provider's authorization endpoint
	http.Redirect(w, r, oidcConfig.OAuth2Config.AuthCodeURL(state), http.StatusFound)
}

func (h *Handler) handleLogout(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")

	// Grab the session_id cookie (if any)
	cookie, err := r.Cookie("session_id")
	if err == nil {
		// Delete the server-side session
		h.sessionStore.ClearSession(cookie.Value)
	}

	// Clear the cookie in the browser
	http.SetCookie(w, &http.Cookie{
		Name:     "session_id",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		SameSite: http.SameSiteNoneMode,
	})

	// Redirect to its end-session endpoint:
	// NOTE: This would log the user out of the OIDC provider, but it is not
	// strictly necessary for our use case, since we are not using the OIDC
	// provider for anything other than authentication.  This code is left here
	// for reference, in case we want to implement a full logout flow in the
	// future.

	// // Choose the OIDC configuration based on the request host
	// oidcConfig := getConfig(isDev)
	// redirectURI := url.QueryEscape(oidcConfig.RedirectURL)
	// logoutURL := oidcConfig.IssuerURL +
	// 	"/protocol/openid-connect/logout?redirect_uri=" + redirectURI
	// http.Redirect(w, r, logoutURL, http.StatusFound)

	http.Redirect(w, r, "/", http.StatusFound)
}

// GetConfig returns the appropriate OIDC configuration based on environment
var getConfig = func(isDev bool) *OIDCConfig {
	if isDev {
		return &SynapseOIDCLocal
	}
	return &SynapseOIDC
}
