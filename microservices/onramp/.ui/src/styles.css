/* You can add global styles to this file, and also import other style files */

/* Custom ng-zorro theme variables */
:root {
  /* Primary colors */
  --primary-color: #000047;
  --primary-color-hover: rgb(3, 3, 160);

  /* Error colors */
  --error-color: #ff4d4f;

  /* Neutral colors */
  --text-color: #6C7278;
  --text-color-secondary: #8c8c8c;
  --text-color-disabled: #bfbfbf;
  --border-color-base: #d9d9d9;
  --border-color-split: #f0f0f0;
  --background-color-base: #f5f5f5;
  --background-color-light: #fafafa;

  /* Custom fonts */
  --font-family: 'Arial', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;

  /* Border radius */
  --border-radius-base: 6px;
  --border-radius-sm: 4px;
  --border-radius-lg: 12px;


  /* Input specific colors */
  --input-text-color: #1A1C1E;
  --input-placeholder-color: var(--text-color-secondary);
  --input-bg-color: #ffffff;

  /* Input dimensions */
  /* --input-height: 44px;
  --input-height-sm: 36px; */
  --input-height-lg: 52px;

  /* Button dimensions */
  /* --button-height: 44px;
  --button-height-sm: 36px; */
  --button-height-lg: 52px;

  /* Button specific colors */
  --button-border-color: #ACB5BB;
  /* --button-border-color-hover: #9EA6AC; */
  /* darker ~8% */
  --button-border-color-focus: #B3BBC0;
  /* lighter ~8% */
  /* --button-text-color: #ACB5BB;
  --button-text-color-focus: #B3BBC0;
  --button-text-color-active: #ACB5BB; */

}

html,
body {
  width: 100%;
  overflow: auto;
  height: 100%;
}

body {
  margin: 0;
  font-family: var(--font-family);
  color: var(--text-color);
  background-color: var(--background-color-light);
}

/* General Link Styling */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

a:hover {
  color: var(--primary-color-hover);
  text-decoration: underline;
}

a:active {
  color: var(--primary-color-active);
}

a:visited {
  color: var(--primary-color);
}

/* ng-zorro Link Component Overrides */
.ant-link {
  font-family: var(--font-family);
  color: var(--primary-color);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-link:hover {
  color: var(--primary-color-hover);
}

.ant-link:active {
  color: var(--primary-color-active);
}

/* ng-zorro Button Component Overrides */
.ant-btn {
  font-family: var(--font-family);
  border-radius: var(--border-radius-base);
  border-color: var(--button-border-color);
  font-weight: 500abd;
  height: var(--button-height);
  /* line-height: var(--button-height); */
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-btn:focus,
.ant-btn:focus-visible {
  border-color: var(--button-border-color-focus);
}

.ant-btn:not(.ant-btn-primary):hover {
  border-color: var(--button-border-color-hover);
}

.ant-btn:not(.ant-btn-primary):active,
.ant-btn:not(.ant-btn-primary):focus:active,
.ant-btn:not(.ant-btn-primary):focus-visible:active {
  color: var(--button-text-color-active);
}

.ant-btn-sm {
  height: var(--button-height-sm);
  line-height: var(--button-height-sm);
}

.ant-btn-lg {
  height: var(--button-height-lg);
  line-height: var(--button-height-lg);
}

.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: var(--primary-color-hover);
  border-color: var(--primary-color-hover);
}

.ant-btn-primary:active {
  background-color: var(--primary-color-active);
  border-color: var(--primary-color-active);
}

/* ng-zorro Input Component Overrides */
.ant-input {
  font-family: var(--font-family);
  border-radius: var(--border-radius-base);
  border-color: var(--border-color-base);
  color: var(--input-text-color);
  background-color: var(--input-bg-color);
  height: var(--input-height);
  line-height: var(--input-height);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-input-sm {
  height: var(--input-height-sm);
  line-height: var(--input-height-sm);
}

.ant-input-lg {
  height: var(--input-height-lg);
  line-height: var(--input-height-lg);
}

.ant-input::placeholder {
  color: var(--input-placeholder-color);
}

.ant-input:focus,
.ant-input-focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-outline);
  color: var(--input-text-color);
}

/* ng-zorro Table Component Overrides */
.ant-table {
  font-family: var(--font-family);
  border-radius: var(--border-radius-base);
}

.ant-table-thead tr th {
  font-weight: 600;
  background-color: var(--background-color-base);
  border-bottom: 2px solid var(--border-color-base);
}

.ant-table-tbody tr:hover>td {
  background-color: var(--background-color-base);
}

/* ng-zorro Modal Component Overrides */
.ant-modal {
  font-family: var(--font-family);
}

.ant-modal-content {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-base);
}

.ant-modal-header {
  border-bottom: 1px solid var(--border-color-split);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.ant-modal-title {
  font-weight: 600;
  color: var(--text-color);
}

/* ng-zorro Card Component Overrides */
.ant-card {
  font-family: var(--font-family);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-card);
  border: 1px solid var(--border-color-split);
}

.ant-card-head {
  border-bottom: 1px solid var(--border-color-split);
}

.ant-card-head-title {
  font-weight: 600;
  color: var(--text-color);
}

/* ng-zorro Form Component Overrides */
.ant-form {
  font-family: var(--font-family);
}

.ant-form-item-label>label {
  font-weight: 500;
  color: var(--text-color);
}

.ant-form-item-explain-error {
  color: var(--error-color);
}

/* ng-zorro Select Component Overrides */
.ant-select {
  font-family: var(--font-family);
}

.ant-select-selector {
  border-radius: var(--border-radius-base);
  border-color: var(--border-color-base);
}

.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-outline);
}

/* ng-zorro Menu Component Overrides */
.ant-menu {
  font-family: var(--font-family);
  background-color: transparent;
}

.ant-menu-item {
  border-radius: var(--border-radius-sm);
  margin: 4px 8px;
}

.ant-menu-item-selected {
  background-color: var(--primary-color-outline);
  color: var(--primary-color);
}

.ant-menu-item:hover {
  background-color: var(--menu-item-hover-bg-color);
  color: var(--menu-item-hover-text-color);
}

/* ng-zorro Tag Component Overrides */
.ant-tag {
  font-family: var(--font-family);
  border-radius: var(--border-radius-sm);
  font-weight: 500;
}

.ant-tag-success {
  background-color: var(--success-color-outline);
  border-color: var(--success-color);
  color: var(--success-color-active);
}

.ant-tag-warning {
  background-color: var(--warning-color-outline);
  border-color: var(--warning-color);
  color: var(--warning-color-active);
}

.ant-tag-error {
  background-color: var(--error-color-outline);
  border-color: var(--error-color);
  color: var(--error-color-active);
}

/* ng-zorro Alert Component Overrides */
.ant-alert {
  font-family: var(--font-family);
  border-radius: var(--border-radius-base);
  border: none;
}

.ant-alert-success {
  background-color: var(--success-color-outline);
  color: var(--success-color-active);
}

.ant-alert-warning {
  background-color: var(--warning-color-outline);
  color: var(--warning-color-active);
}

.ant-alert-error {
  background-color: var(--error-color-outline);
  color: var(--error-color-active);
}

.ant-alert-info {
  background-color: var(--info-color-outline);
  color: var(--info-color-active);
}

/* ng-zorro Breadcrumb Component Overrides */
.ant-breadcrumb {
  font-family: var(--font-family);
}

.ant-breadcrumb a {
  color: var(--text-color-secondary);
  transition: color 0.3s;
}

.ant-breadcrumb a:hover {
  color: var(--primary-color);
}

/* ng-zorro Avatar Component Overrides */
.ant-avatar {
  font-family: var(--font-family);
  font-weight: 500;
}

/* ng-zorro Tooltip Component Overrides */
.ant-tooltip {
  font-family: var(--font-family);
}

.ant-tooltip-inner {
  border-radius: var(--border-radius-sm);
  font-weight: 500;
}

/* ng-zorro Popconfirm Component Overrides */
.ant-popover {
  font-family: var(--font-family);
}

.ant-popover-inner {
  border-radius: var(--border-radius-base);
}

/* ng-zorro Switch Component Overrides */
.ant-switch {
  background-color: var(--border-color-base);
}

.ant-switch-checked {
  background-color: var(--primary-color);
}

/* ng-zorro DatePicker Component Overrides */
.ant-picker {
  font-family: var(--font-family);
  border-radius: var(--border-radius-base);
  border-color: var(--border-color-base);
}

.ant-picker-focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-outline);
}

/* ng-zorro Upload Component Overrides */
.ant-upload {
  font-family: var(--font-family);
}

.ant-upload-drag {
  border-radius: var(--border-radius-base);
  border-color: var(--border-color-base);
}

.ant-upload-drag:hover {
  border-color: var(--primary-color);
}

/* ng-zorro Divider Component Overrides */
.ant-divider {
  border-color: var(--border-color-split);
}

.ant-divider-vertical {
  border-left: 1px solid var(--border-color-split);
}

.custom-confirm-modal .ant-modal-confirm-content {
  background: var(--error-color-outline);
  padding: 10px;
  border-radius: var(--border-radius-base);
  color: var(--error-color-active);
  margin: 25px 0px;
  margin-left: unset !important;
}

.custom-confirm-modal .ant-modal-confirm-btns button,
.confirm-modal .ant-modal-confirm-btns button {
  border-radius: var(--border-radius-base);
  height: 40px;
}

.custom-login-modal-required .ant-modal-content,
.custom-login-modal-permissions .ant-modal-content {
  border-radius: var(--border-radius-base);
}

.custom-login-modal-required .ant-modal-content .ant-modal-confirm-btns button,
.custom-login-modal-permissions .ant-modal-content .ant-modal-confirm-btns button {
  width: 80px;
  height: 40px;
  border-radius: var(--border-radius-base);
}

nz-message-container .ant-message-notice {
  margin-top: 50px;
}

/* css spacing utilities */
.w-100 {
  width: 100% !important;
}

.mb-8 {
  margin-bottom: 8px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mt-24 {
  margin-top: 24px !important;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.mr-5 {
  margin-right: 5px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.ml-5 {
  margin-left: 5px !important;
}

.ml-5 {
  margin-left: 5px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.p-0 {
  padding: 0 !important;
}

.br-8 {
  border-radius: var(--border-radius-base) !important;
}

.br-12 {
  border-radius: var(--border-radius-lg) !important;
}

.h-4 {
  height: 40px !important;
}