import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-invitations-table',
  standalone: false,
  templateUrl: './invitations-table.component.html',
  styleUrl: './invitations-table.component.css'
})
export class InvitationsTableComponent {
  @Input() orgId: any[] = [];
  @Input() listDataInvitations: any[] = [];
  @Input() isLoadingTable = false;
  @Input() nameOrganization: string | null = null;
  @Output() resendInvite = new EventEmitter<any>();
  @Output() revokeInvite = new EventEmitter<any>();
  highlightedRowId: string | null = null;
  isLoadingResend: boolean[] = [];
  confirmModal?: NzModalRef;
  constructor(
    private modalService: NzModalService,
  ) { }

  ngOnInit() { }
  capitalize(value: string): string {
    if (!value) return value;
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  }
  handleResend(value: any) {
    this.resendInvite.emit(value);
  }
  handleDelete(value: any) {
    this.revokeInvite.emit(value);
  }
}
