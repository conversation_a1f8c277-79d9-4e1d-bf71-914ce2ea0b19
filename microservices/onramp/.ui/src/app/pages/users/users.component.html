<div class="users-container">
  <div class="gr-header">
    <div class="form-left">
      <div class="title-users">
        <h1 id="title-users">Users</h1>
      </div>
      <div class="form-breadcrumb">
        <nz-breadcrumb>
          <nz-breadcrumb-item>
            <a (click)="handleBackOrganization()">{{ nameOrganization }}</a>
          </nz-breadcrumb-item>
          <nz-breadcrumb-item>
            Admin
          </nz-breadcrumb-item>
          <nz-breadcrumb-item>
            Users
          </nz-breadcrumb-item>
        </nz-breadcrumb>
      </div>
    </div>
    <div class="form-right">
      <button id="btn-create-invite-user" nz-button nzType="primary" class="add-btn br-8 h-4" (click)="onCreateUser()">
        Invite User
      </button>
    </div>
  </div>
  <div class="page-description-users">
    <span>This page allows you to manage the users within the <b>{{ nameOrganization}}</b> organization</span>
  </div>
  <div class="form-table-user">
    <div class="title-user-table">
      <h1 id="title-main">Users In Organization</h1>
      <span>These Users are currently in the <b>{{ nameOrganization}}</b> Organization and have been assigned the Role
        that is shown
        below.</span>
    </div>
    <nz-table id="users-list" class="mt-10" #basicTable nzBordered [nzData]="listDataUsers" nzShowSizeChanger
      nzShowPagination [nzLoading]="isTableLoading">
      <thead>
        <tr>
          <th nzWidth="10%">First Name</th>
          <th nzWidth="10%">Last Name</th>
          <th nzWidth="10%">Username</th>
          <th nzWidth="15%">Email</th>
          <th nzWidth="10%">Auth Method</th>
          <th nzWidth="15%">Organization Role</th>
          <th nzWidth="15%">Last Login</th>
          <th nzWidth="8%">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index"
          [ngClass]="data.id === highlightedRowId ? 'highlight-row' : 'default-row'">
          <td>{{ data.firstName }}</td>
          <td>{{ data.lastName }}</td>
          <td>{{ data.userName }}</td>
          <td>{{ data.email }}</td>
          <td>{{ data.authMethod }}</td>
          <td>
            <ng-container *ngIf="!isArray(data.orgRole)">
              <div class="item-obj">
                <span class="dot mr-5"></span> {{ getOrgName(data.orgRole) }}
              </div>
            </ng-container>
            <ng-container *ngIf="isArray(data.orgRole)">
              <div class="item-arr" *ngFor="let org of data.orgRole">
                <span class="dot mr-5"></span> {{ getOrgName(org) }}
              </div>
            </ng-container>
          </td>
          <td>{{ data.lastLogin | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC' }}</td>
          <td>
            <div class="btn-action">
              <button id="btn-edit-user" *ngIf="hasSynapseManagerUser" nz-button nzType="primary"
                (click)="handleEdit(data)" nz-tooltip nzTooltipTitle="Edit" class="mr-10 br-8">
                <nz-icon nzType="edit" nzTheme="outline" />
              </button>
              <button id="btn-delete-user" *ngIf="hasSynapseDeleteUser" nz-button nzType="primary"
                (click)="handleDelete(data)" nzDanger nz-tooltip nzTooltipTitle="Delete" class="br-8">
                <nz-icon nzType="delete" nzTheme="outline" />
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <app-invitations-table [orgId]="orgIdUsers" [listDataInvitations]="listDataInvitations"
      [nameOrganization]="nameOrganization" [isLoadingTable]="isTableLoading"
      (resendInvite)="handleResendInvite($event)" (revokeInvite)="handleRevokeInvite($event)">
    </app-invitations-table>
  </div>
  <app-add-edit-user [isVisible]="isModalVisible" [isEditMode]="isEditMode" [data]="currentUser"
    [nameOrg]="nameOrganization" (close)="onCloseModal()" [listOrgRole]="orgRoleOptions"
    (confirm)="handleModalSave($event)"></app-add-edit-user>
</div>