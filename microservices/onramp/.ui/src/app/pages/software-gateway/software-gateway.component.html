<div class="software-container" id="software-container">
  <div class="gr-header">
    <div class="form-left">
      <div class="title-software">
        <h1 id="title-gateways">Gateways</h1>
      </div>
      <div class="form-breadcrumb">
        <nz-breadcrumb>
          <nz-breadcrumb-item>
            <a (click)="handleBackOverviewGateway()">
              {{nameOrganization ? nameOrganization : 'Organization'}}
            </a>
          </nz-breadcrumb-item>
          <nz-breadcrumb-item>
            Gateways
          </nz-breadcrumb-item>
        </nz-breadcrumb>
      </div>
    </div>
    <div class="form-right">
      <button id="btn-create-gateways" nz-button nzType="primary" class="add-btn br-8 h-4" (click)="showModal()">
        Add Gateway
      </button>
    </div>
  </div>
  <div class="page-description-software">
    <span>This page shows all Gateways that <b>Organization</b> has created.</span>
  </div>

  <nz-table id="gateways-list" #basicTable nzBordered nzShowSizeChanger [nzData]="listOfData"
    [nzLoading]="isTableLoading" style="margin-top: 16px;">
    <thead nzSingleSort>
      <tr>
        <th nzShowSort nzSortKey="name" (click)="onColumnClick('name')"
          [nzSortOrder]="currentSortKey === 'name' ? sortOrder : null">
          Name
        </th>
        <th nzShowSort nzSortKey="isenabled" (click)="onColumnClick('isenabled')"
          [nzSortOrder]="currentSortKey === 'isenabled' ? sortOrder : null">
          Is Enabled
        </th>
        <th nzShowSort nzSortKey="apikey" (click)="onColumnClick('apikey')"
          [nzSortOrder]="currentSortKey === 'apikey' ? sortOrder : null">
          API Key
        </th>
        <th nzShowSort nzSortKey="machinekey" (click)="onColumnClick('machinekey')"
          [nzSortOrder]="currentSortKey === 'machinekey' ? sortOrder : null">
          Machine Key
        </th>
        <th nzShowSort nzSortKey="createdat" (click)="onColumnClick('createdat')"
          [nzSortOrder]="currentSortKey === 'createdat' ? sortOrder : null">
          Created
        </th>
        <th nzShowSort nzSortKey="updatedat" (click)="onColumnClick('updatedat')"
          [nzSortOrder]="currentSortKey === 'updatedat' ? sortOrder : null">
          Updated
        </th>
        <th nzWidth="15%">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td>
          <span nz-tooltip [nzTooltipTitle]="data?.name">
            {{ data.name ? (data.name.length > 20 ? data.name.substring(0, 20) + '...' : data.name) : '' }}
          </span>
        </td>
        <td>
          <nz-tag [nzColor]="getTagColor(data.isenabled)">{{ getTagText(data.isenabled) }}</nz-tag>
        </td>
        <td [nzTooltipTitle]="data?.apikey" nz-tooltip>
          <span class="truncate-input">{{ data.apikey }}</span>
        </td>
        <td [nzTooltipTitle]="data?.machinekey" nz-tooltip>
          <span class="truncate-input">{{ data.machinekey }}</span>
        </td>
        <td nzAlign="center">{{ data.createdat | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC' }}</td>
        <td nzAlign="center">{{ data.updatedat | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC' }}</td>
        <td class="btn-action">
          <button nz-button nzType="default" (click)="handleConfig(data)"
            class="btn-soft br-8 mr-10">Configuration</button>
          <button nz-button nzType="primary" (click)="editGateway(i)" class="edit-gateway-btn br-8">Edit</button>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <app-gateway-edit-add [isVisible]="isVisible" [isEditMode]="isEditMode" [data]="currentGateway"
    [showBtnReset]="showBtnReset" [nameOrg]="nameOrganization" (close)="handleModalClose()"
    (confirm)="handleModalSave($event)" (delete)="handleModalDelete($event)" (generateAPIkey)="handleGenerate($event)"
    (loadingChange)="handleLoadingChange($event)"></app-gateway-edit-add>
</div>