import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ApsResetService } from '../../core/services/aps_reset.service';
import { AuthService } from '../../core/services/auth.service';
import { NzMessageService } from 'ng-zorro-antd/message';

interface Permission {
  organizationId: string;
  scope: string;
  permissions?: string[];
}

interface ApsResetResponse {
  data: string;
  message?: string;
}

@Component({
  selector: 'app-aps-reset',
  standalone: false,
  templateUrl: './aps-reset.component.html',
  styleUrl: './aps-reset.component.css'
})

export class ApsResetComponent {
  @ViewChild('input1') input1!: ElementRef<HTMLInputElement>;
  @ViewChild('input2') input2!: ElementRef<HTMLInputElement>;
  @ViewChild('input3') input3!: ElementRef<HTMLInputElement>;
  @ViewChild('input4') input4!: ElementRef<HTMLInputElement>;

  codes: string[] = ['', '', '', ''];
  responseCode: string | null = null;
  isConfirmModalVisible: boolean = false;
  hasApsResetPermission: boolean = false;
  orgId: string | null = null;
  private destroy$ = new Subject<void>();

  // Regex for valid characters
  private readonly validFirstChar = /^[0-9A-HJKLMNP-X]$/i;
  private readonly validOtherChar = /^[0-9A-F]$/i;

  constructor(
    private route: ActivatedRoute,
    private apsResetService: ApsResetService,
    private authService: AuthService,
    private message: NzMessageService
  ) { }

  ngOnInit(): void {
    const state = window.history.state;
    this.orgId = state.orgData.id;
    if (!this.orgId) {
      this.message.error('Organization ID is missing.');
      this.hasApsResetPermission = false;
      return;
    } else {
      this.hasApsResetPermission = true;
    }
    this.checkPermission();
  }

  private checkPermission(): void {
    this.authService.permissions$
      .pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        const permissions = response || [];
        this.hasApsResetPermission = permissions.some(
          (perm: Permission) => perm.organizationId === this.orgId && perm.scope === 'org_aps_factory_reset'
        );
        if (!this.hasApsResetPermission) {
          this.message.error('You do not have permission to access APS Reset.');
        }
      }, () => {
        this.hasApsResetPermission = false;
        this.message.error('Failed to load permissions. Please try again.');
      });
  }

  onInput(event: Event, inputIndex: number): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.toUpperCase();

    if (inputIndex === 1 && value.length > 0) {
      if (!this.validFirstChar.test(value[0])) {
        input.value = value.slice(1);
        value = input.value;
      }
      if (value.length > 1 && !this.validOtherChar.test(value[1])) {
        input.value = value[0];
        value = input.value;
      }
    } else if (inputIndex > 1 && value.length > 0) {
      value = value.split('').filter(char => this.validOtherChar.test(char)).join('');
      input.value = value;
    }

    this.codes[inputIndex - 1] = value;
    input.value = value;

    if (value.length === 2) {
      this.focusNextInput(inputIndex);
    }
  }

  onKeyDown(event: KeyboardEvent, inputIndex: number): void {
    const input = event.target as HTMLInputElement;
    // Move to previous cell when Backspace is pressed and current cell is empty
    if (event.key === 'Backspace' && this.codes[inputIndex - 1].length === 0 && inputIndex > 1) {
      this.focusPreviousInput(inputIndex);
    }
    // Automatically move to the next cell when 2 characters are entered (in case of paste)
    if (this.codes[inputIndex - 1].length === 2 && event.key !== 'Backspace') {
      this.focusNextInput(inputIndex);
    }
  }

  private focusNextInput(currentIndex: number): void {
    if (currentIndex < 4) {
      const inputRef = (this as any)[`input${currentIndex + 1}`] as ElementRef<HTMLInputElement>;
      if (inputRef && inputRef.nativeElement) {
        inputRef.nativeElement.focus();
        inputRef.nativeElement.select();
      }
    }
  }

  private focusPreviousInput(currentIndex: number): void {
    if (currentIndex > 1) {
      const prevInputRef = (this as any)[`input${currentIndex - 1}`] as ElementRef<HTMLInputElement>;
      if (prevInputRef && prevInputRef.nativeElement) {
        prevInputRef.nativeElement.focus();
        prevInputRef.nativeElement.select();
      }
    }
  }

  isSubmitEnabled(): boolean {
    return (
      this.codes.every(code => code.length === 2) &&
      this.validFirstChar.test(this.codes[0][0]) &&
      this.validOtherChar.test(this.codes[0][1]) &&
      this.codes.slice(1).every(code => code.split('').every(char => this.validOtherChar.test(char)))
    );
  }

  openConfirmModal(): void {
    const fullCode = this.codes.join('');
    if (this.isSubmitEnabled()) {
      this.isConfirmModalVisible = true;
    } else {
      this.message.warning('Please enter a valid 8-character challenge code.');
    }
  }

  formattedChallengeCodeForConfirm() {
    const code = this.codes;
    let formatted = '';
    for (let i = 0; i < code.length; i++) {
      formatted += code[i] + ' ';
    }
    return formatted.trim();
  }

  submitCode(): void {
    this.isConfirmModalVisible = false;
    const fullCode = this.codes.join('');
    if (!this.orgId) {
      this.message.error('Organization ID is missing.');
      return;
    }
    const formChallenge = {
      challengeCode: fullCode
    }
    this.apsResetService
      .resetAps(this.orgId, formChallenge)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: ApsResetResponse) => {
          this.responseCode = response.data;
          this.message.success(response.message || 'APS reset successful.');
        },
        error: (err: any) => {
          this.message.error(err.error?.message || 'Failed to reset APS. Please try again.');
        }
      });
  }

  handleCloseModal() {
    this.isConfirmModalVisible = false;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
