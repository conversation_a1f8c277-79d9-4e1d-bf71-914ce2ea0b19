import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-overviews',
  standalone: false,
  templateUrl: './overviews.component.html',
  styleUrl: './overviews.component.css'
})
export class OverviewsComponent {
  nameOverviewOrg: any;
  constructor(
    private route: ActivatedRoute,
  ) { }
  ngOnInit() {
    this.route.params.subscribe((params: any) => {
      console.log(history.state.orgData);
      this.nameOverviewOrg = history.state.orgData?.name || null;
    })
  }
}
