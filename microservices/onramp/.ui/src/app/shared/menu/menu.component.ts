import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { forkJoin, filter, debounceTime, takeUntil, Subject } from 'rxjs';
import { Organization, OrganizationsService } from '../../core/services/organization.service';
import { AuthService } from '../../core/services/auth.service';
import { ScrollService } from '../../core/services/Scroll.service';

interface ExtendedOrganization extends Organization {
  name: string;
  hasDeviceGroupScope: boolean;
  hasLocationGroupScope: boolean;
  hasOrgManageDevices: boolean;
  hasSynapseOrgType: boolean;
  hasSynapseViewAllOrganizations: boolean;
  hasApsFactoryReset: boolean;
}

@Component({
  selector: 'app-menu',
  standalone: false,
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.css'
})
export class MenuComponent implements OnInit {
  @ViewChild('sider') sider!: ElementRef;
  selectedKey: string | null = null;
  isCollapsed = false;
  openMap: { [name: string]: boolean } = {
    sub1: false,
    sub2: false,
    sub3: false,
    sub4: false
  };
  isFavorite = false;
  hasSynapseViewAllUsers = false;
  hasSynapseViewAllOrganizations = false;
  hasSynapseManagerAllOrganizations = false;
  hasSynapseDeleteOrganizations = false;
  synapseMenu: ExtendedOrganization[] = [];
  organizationsMenu: ExtendedOrganization[] = [];
  private currentOrgId: string | null = null;
  selectedIndex: number | null = null;
  private destroy$ = new Subject<void>();
  private isAddingOrg = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private organizationsService: OrganizationsService,
    private authService: AuthService,
    private message: NzMessageService,
    private scrollService: ScrollService,
    private cdr: ChangeDetectorRef
  ) {
    this.loadPermissions();
    this.organizationsService.selectedOrganization$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(org => {
      if (org) {
        this.isAddingOrg = true;
        this.addOrganizationToMenu(org);
      }
    });
  }

  ngOnInit() {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      debounceTime(50), // Small delay to ensure router state is settled
      takeUntil(this.destroy$)
    ).subscribe((event: NavigationEnd) => {
      this.handleCheckUrl(event.urlAfterRedirects)
      if (!this.isAddingOrg) {
        this.checkActiveMenu(); // Use event URL
      }
    });

    this.route.queryParams.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      this.currentOrgId = params['organizationId'] || null;
      const currentUrl = this.router.url;
      const orgRoutes = ['/overview', '/software-gateway', '/devices', '/aps-reset'];
      const nonOrgRoutes = ['/protected', '/all-users', '/organizations', '/permissions', '/users', '/authentication', '/invitations-pending'];
      if (nonOrgRoutes.some(route => currentUrl.includes(route)) && !orgRoutes.some(route => currentUrl.includes(route))) {
        this.organizationsMenu = [];
        this.currentOrgId = null;
        this.selectedIndex = null;
      }
      if (!this.isAddingOrg) {
        this.checkActiveMenu();
      }
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  handleCheckUrl(value?: any) {
    this.authService.permissions$.pipe(
      takeUntil(this.destroy$)
    ).subscribe((permissionsResponse: any) => {
      const currentUrl = value ? value : this.router.url;
      const orgPermissions = permissionsResponse?.data?.permissions
        ?.filter((perm: any) => perm.scope === 'org')
        .flatMap((perm: any) => perm.permissions) || [];
      if (currentUrl.includes('/organizations')) {
        const orgRelevantPermissions = orgPermissions.filter((perm: string) =>
          ['synapse_view_organizations', 'synapse_manage_organizations', 'synapse_delete_organizations'].includes(perm)
        );
        localStorage.setItem('organizationPermissionsSynapse', JSON.stringify(orgRelevantPermissions));
      }
      if (currentUrl.includes('/software-gateway')) {
        const orgRelevantPermissions = orgPermissions.filter((perm: string) =>
          ['synapse_manage_organizations'].includes(perm)
        );
        localStorage.setItem('gatewayPermissions', JSON.stringify(orgRelevantPermissions));
      }
      if (currentUrl.includes('/users')) {
        const userRelevantPermissions = orgPermissions.filter((perm: string) =>
          ['synapse_manage_synapse_users', 'synapse_delete_synapse_users', 'synapse_view_synapse_users'].includes(perm)
        );
        localStorage.setItem('userPermissionsSynapse', JSON.stringify(userRelevantPermissions));
      }
    });

  }
  private loadPermissions() {
    this.authService.permissions$.subscribe({
      next: (response: any) => {
        if (!response || !Array.isArray(response.data.permissions)) {
          this.hasSynapseViewAllUsers = false;
          this.hasSynapseViewAllOrganizations = false;
          this.hasSynapseManagerAllOrganizations = false;
          this.hasSynapseDeleteOrganizations = false;
          this.synapseMenu = [];
          this.organizationsMenu = [];
          return;
        }
        const permissions = response.data.permissions;

        // Get organizationId from scope: "org"
        const orgIds = [...new Set(
          permissions
            .filter((perm: any) => perm.scope === 'org')
            .map((perm: any) => perm.organizationId)
        )];

        const orgPermissions = permissions.reduce((acc: { [key: string]: any[] }, perm: any) => {
          if (!acc[perm.organizationId]) {
            acc[perm.organizationId] = [];
          }
          acc[perm.organizationId].push(perm);
          return acc;
        }, {});

        // Check permissions in scope 'org'
        this.hasSynapseViewAllOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.permissions.includes('synapse_view_organizations')
          )
        );
        this.hasSynapseManagerAllOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.permissions.includes('synapse_manage_organizations')
          )
        );
        this.hasSynapseDeleteOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.permissions.includes('synapse_delete_organizations')
          )
        );
        this.hasSynapseViewAllUsers = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.permissions.includes('synapse_view_synapse_users')
          )
        );

        // Fetch organization details for all orgIds
        const orgRequests = orgIds.map((orgId: any) =>
          this.organizationsService.getOrganizationsId(orgId)
        );

        forkJoin(orgRequests).subscribe({
          next: (orgs: any) => {
            this.synapseMenu = [];
            this.organizationsMenu = [];

            orgs
              .filter((org: any): org is Organization => org != null && !!org.id && !!org.name)
              .forEach((org: any) => {
                const extendedOrg: ExtendedOrganization = {
                  ...org,
                  hasDeviceGroupScope: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.scope === 'device_group'
                  ),
                  hasLocationGroupScope: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.scope === 'location_group'
                  ),
                  hasOrgManageDevices: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.permissions.includes('org_manage_devices')
                  ),
                  hasSynapseOrgType: org.orgtypeidentifier === 'synapse',
                  hasSynapseViewAllOrganizations: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.permissions.includes('synapse_view_organizations')
                  ),
                  hasApsFactoryReset: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.permissions.includes('org_aps_factory_reset')
                  ),
                };

                // Classify organizations into synapseMenu or organizationsMenu
                if (extendedOrg.hasSynapseOrgType) {
                  this.synapseMenu.push(extendedOrg);
                } else if (org.orgtypeidentifier === 'municipality') {
                  this.organizationsMenu.push(extendedOrg);
                }
              });

            this.organizationsMenu.sort((a: any, b: any) => a.name.localeCompare(b.name));
          },
          error: (err) => {
            this.message.create('error', 'Failed to load organizationsMenu. Please try again.', { nzDuration: 5000 });
            this.synapseMenu = [];
            this.organizationsMenu = [];
          }
        });
      },
      error: (err) => {
        this.hasSynapseViewAllUsers = false;
        this.hasSynapseViewAllOrganizations = false;
        this.hasSynapseManagerAllOrganizations = false;
        this.hasSynapseDeleteOrganizations = false;
        this.synapseMenu = [];
        this.organizationsMenu = [];
      }
    });
  }
  private addOrganizationToMenu(org: Organization) {
    if (!org || !org.id || !org.name) {
      this.message.create('error', 'Invalid organization data.', { nzDuration: 5000 });
      this.isAddingOrg = false;
      return;
    }
    this.organizationsMenu = [];
    this.authService.permissions$.pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response: any) => {
        const orgPermissions = response?.data?.permissions?.filter((perm: any) => perm.organizationId === org.id) || [];
        const extendedOrg: ExtendedOrganization = {
          ...org,
          hasDeviceGroupScope: orgPermissions.some((perm: any) => perm.scope === 'device_group'),
          hasLocationGroupScope: orgPermissions.some((perm: any) => perm.scope === 'location_group'),
          hasOrgManageDevices: orgPermissions.some((perm: any) => perm.permissions.includes('org_manage_devices')),
          hasSynapseOrgType: org.orgtypeidentifier === 'synapse',
          hasSynapseViewAllOrganizations: orgPermissions.some((perm: any) => perm.permissions.includes('synapse_view_organizations')),
          hasApsFactoryReset: orgPermissions.some((perm: any) => perm.permissions.includes('org_aps_factory_reset')),
        };
        if (!extendedOrg.hasSynapseOrgType) {
          this.organizationsMenu.push(extendedOrg);
          this.organizationsService.setCurrentOrgId(org.id);
          this.organizationsService.setCurrentIndex(0);
          this.currentOrgId = org.id;
          this.selectedIndex = 0;
          this.cdr.detectChanges();
          this.checkActiveMenu();
        }
        this.isAddingOrg = false;
      },
      error: (err) => {
        this.message.create('error', 'Failed to load organization permissions.', { nzDuration: 5000 });
        console.error('Error loading permissions:', err);
        this.isAddingOrg = false;
      }
    });
  }
  onClickGateway(org: ExtendedOrganization, index: number, url?: string): void {
    this.organizationsService.setCurrentOrgId(org.id);
    this.organizationsService.setCurrentIndex(index);
    this.currentOrgId = org.id;
    this.selectedIndex = index;

    if (org.id) {
      this.isAddingOrg = true;
      const cleanUrl = url?.startsWith('/') ? url.slice(1) : url || '';
      this.router.navigate([`/organization/${org.id}/${cleanUrl}`], {
        state: { orgData: org }
      }).then(success => {
        this.isAddingOrg = false;
        this.checkActiveMenu();
        this.cdr.detectChanges();
      }).catch(error => {
        console.error('Navigation error:', error);
        this.isAddingOrg = false;
      });
    } else {
      this.message.create('error', 'Organization ID is missing.', { nzDuration: 5000 });
      this.isAddingOrg = false;
    }
  }

  onClickSubmenu(route: string, org?: ExtendedOrganization, index?: number): void {
    if (org && index !== undefined) {
      this.organizationsService.setCurrentOrgId(org.id);
      this.organizationsService.setCurrentIndex(index);
      this.currentOrgId = org.id;
      this.selectedIndex = index;

      if (org.id) {
        this.isAddingOrg = true;
        const cleanUrl = route.startsWith('/') ? route.slice(1) : route;
        this.router.navigate([`/${cleanUrl}`], {
          state: { orgData: org }
        }).then((success) => {
          this.isAddingOrg = false;
          this.checkActiveMenu();
          this.openMap[`sub${index + 2}`] = true;
          this.cdr.detectChanges();
        }).catch(error => {
          console.error('Navigation error:', error);
          this.isAddingOrg = false;
        });
      } else {
        this.message.create('error', 'Organization ID is missing.', { nzDuration: 5000 });
        this.isAddingOrg = false;
      }
    } else {
      this.router.navigate([route], { state: {} }).then(() => {
        this.checkActiveMenu();
        this.openMap['sub1'] = false;
        this.cdr.detectChanges();
      }).catch(error => {
        console.error('Navigation error:', error);
      });
    }
  }
  openHandler(key: string, open?: boolean): void {
    if (open !== undefined) {
      this.openMap[key] = open;
    } else {
      this.openMap[key] = true;
    }
  }

  private checkActiveMenu(): void {
    const activeRoutes = ['/permissions', '/users'];
    this.openMap['sub1'] = activeRoutes.some(route => this.isActive(route));
    this.organizationsMenu.forEach((org, index) => {
      const subKey = `sub${index + 2}`;
      const activeSubRoutes = ['/aps-reset', '/permissions', '/users'];
      this.openMap[subKey] = activeSubRoutes.some(route => this.isActive(route, index));
    });

    this.cdr.markForCheck();
  }

  isActive(route: string, index?: number): boolean {
    const fullUrl = this.router.url.split('?')[0] || '/';
    const segments = fullUrl.split('/').filter(segment => segment);
    const currentPath = segments[0] || '';
    const routeWithoutSlash = route.startsWith('/') ? route.slice(1) : route;
    if (index === undefined) {
      let isRouteActive: boolean;

      if (routeWithoutSlash === 'organizations') {
        isRouteActive = fullUrl === '/organizations' || fullUrl.startsWith('/organizations/');
      } else {
        isRouteActive = currentPath ? currentPath === routeWithoutSlash : false;
      }

      return isRouteActive;
    } else {
      const currentSubRoute = segments[2] || '';
      const isRouteActive = currentSubRoute === routeWithoutSlash;
      const currentUrlOrgId = segments[1] || '';
      const storedOrgId = this.organizationsService.getCurrentOrgId();
      const isOrgIdMatch = currentUrlOrgId === this.organizationsMenu[index]?.id || storedOrgId === this.organizationsMenu[index]?.id;
      const currentIndex = this.organizationsService.getCurrentIndex();
      const isActive = isRouteActive && currentIndex === index && isOrgIdMatch;
      return isActive;
    }
  }

  onSiderScroll(event?: Event): void {
    const element = this.sider.nativeElement;
    if (element.scrollTop + element.clientHeight >= element.scrollHeight - 10) {
      this.scrollService.scrollToTop();
    }
  }

  toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  handleFavorite() {
    this.isFavorite = !this.isFavorite;
  }
}