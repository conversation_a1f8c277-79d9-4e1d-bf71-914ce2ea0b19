import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, SimpleChanges, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../core/services/auth.service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent {
  loginForm!: FormGroup;
  showRegister = false;
  passwordVisible = false;
  credentials = { username: '', password: '' };
  errorMessage: string | null = null;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private message: NzMessageService,
    private auth: AuthService
  ) { }
  get username() { return this.loginForm.get('username'); }
  get password() { return this.loginForm.get('password'); }

  ngOnInit() {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }
  getUsernameErrorTip(): string {
    if (this.username?.errors?.['notExist']) return 'Username does not exist, please try again.';
    if (this.username?.errors?.['minlength']) return 'Username must be at least 3 characters.';
    if (this.username?.errors?.['required']) return 'Username is required.';
    return '';
  }
  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
  }
  onSubmit() {
    if (this.loginForm.valid) {
      const formLogin = this.loginForm.value;
      this.auth.loginApi(formLogin).subscribe({
        next: () => this.isLoading = false,
        error: () => this.isLoading = false
      });
    }
  }
  handleClear() {
    this.loginForm.get('password')?.setValue('');
  }

  isClearValue(): boolean {
    return this.loginForm.get('password')?.value !== '';
  }

  forgotPassword() {
    this.router.navigate(['/forgot-password']);
  }

  loginWithSSO() {
    this.auth.login();
  }

  createNewAccount() {
    this.router.navigate(['/account/register']);
  }
}
