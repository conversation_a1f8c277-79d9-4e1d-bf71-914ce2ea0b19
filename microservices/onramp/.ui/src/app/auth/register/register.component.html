<div class="register-container">
  <h2>Create Account</h2>
  <p class="register-subtitle">
    Create your account to access all features
  </p>

  <form nz-form class="form-register" [formGroup]="registerForm" (ngSubmit)="onSubmit()">
    <!-- First Row: First Name and Last Name -->
    <div class="form-row">
      <div class="form-field">
        <nz-form-item>
          <nz-form-control nzErrorTip="Please enter your first name">
            <span>First Name</span>
            <nz-input-group [nzSuffix]="firstSuffix" nzSize="large">
              <input id="firstname" class="no-radius" nz-input nzSize="large" formControlName="firstname"
                placeholder="Enter your first name" />
            </nz-input-group>
            <ng-template #firstSuffix>
              <span *ngIf="isControlValid('firstname')" nz-icon nzType="check-circle" nzTheme="fill"
                class="tick-success"></span>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div class="form-field">
        <nz-form-item>
          <nz-form-control nzErrorTip="Please enter your last name">
            <span>Last Name</span>
            <nz-input-group [nzSuffix]="lastSuffix" nzSize="large">
              <input id="lastname" class="no-radius" nz-input nzSize="large" formControlName="lastname"
                placeholder="Enter your last name" />
            </nz-input-group>
            <ng-template #lastSuffix>
              <span *ngIf="isControlValid('lastname')" nz-icon nzType="check-circle" nzTheme="fill"
                class="tick-success"></span>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- Second Row: Email and Username -->
    <div class="form-row">
      <div class="form-field">
        <nz-form-item>
          <nz-form-control [nzErrorTip]="emailErrorTpl">
            <span>Email</span>
            <nz-input-group [nzSuffix]="emailSuffix" nzSize="large">
              <input id="email" class="no-radius" formControlName="email" nz-input nzSize="large"
                placeholder="Enter your email address" />
            </nz-input-group>
            <ng-template #emailSuffix>
              <span *ngIf="isControlValid('email')" nz-icon nzType="check-circle" nzTheme="fill"
                class="tick-success"></span>
            </ng-template>
            <ng-template #emailErrorTpl let-control>
              <ng-container *ngIf="control.hasError('required')">
                Please enter your email
              </ng-container>
              <ng-container *ngIf="!control.hasError('required') && control.hasError('invalidEmail')">
                Please enter a valid email
              </ng-container>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div class="form-field">
        <nz-form-item>
          <nz-form-control nzErrorTip="Please enter a username">
            <span>Username</span>
            <nz-input-group [nzSuffix]="usernameSuffix" nzSize="large">
              <input id="username" class="no-radius" formControlName="username" nz-input nzSize="large"
                placeholder="Enter your username" />
            </nz-input-group>
            <ng-template #usernameSuffix>
              <span *ngIf="isControlValid('username')" nz-icon nzType="check-circle" nzTheme="fill"
                class="tick-success"></span>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- Third Row: Password and Confirm Password -->
    <div class="form-row">
      <div class="form-field">
        <nz-form-item>
          <nz-form-control [nzErrorTip]="passwordErrorTpl">
            <span>Password</span>
            <nz-input-group [nzSuffix]="suffixTemplate" nzSize="large">
              <input id="password" class="no-radius" formControlName="password" nz-input nzSize="large"
                placeholder="Enter your password" [type]="passwordVisible ? 'text' : 'password'"
                autocomplete="new-password" />
            </nz-input-group>
            <ng-template #suffixTemplate>
              <span *ngIf="isControlValid('password')" nz-icon nzType="check-circle" nzTheme="fill"
                class="tick-success tick-gap"></span>
              <span nz-icon [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" style="cursor: pointer;"
                (click)="togglePasswordVisibility()"></span>
            </ng-template>
            <ng-template #passwordErrorTpl let-control>
              <ng-container *ngIf="control.hasError('required')">
                Please enter a password
              </ng-container>
              <ng-container
                *ngIf="!control.hasError('required') && (control.hasError('minlength') || control.hasError('weakPassword'))">
                Password does not meet the required policy
              </ng-container>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div class="form-field">
        <nz-form-item>
          <nz-form-control [nzErrorTip]="confirmErrorTpl">
            <span>Confirm Password</span>
            <nz-input-group [nzSuffix]="confirmSuffixTemplate" nzSize="large">
              <input id="confirmPassword" class="no-radius" formControlName="confirmPassword" nz-input nzSize="large"
                placeholder="Confirm your password" [type]="confirmPasswordVisible ? 'text' : 'password'"
                autocomplete="new-password" />
            </nz-input-group>
            <ng-template #confirmSuffixTemplate>
              <span *ngIf="isControlValid('confirmPassword')" nz-icon nzType="check-circle" nzTheme="fill"
                class="tick-success tick-gap"></span>
              <span nz-icon [nzType]="confirmPasswordVisible ? 'eye-invisible' : 'eye'" style="cursor: pointer;"
                (click)="toggleConfirmPasswordVisibility()"></span>
            </ng-template>
            <ng-template #confirmErrorTpl let-control>
              <ng-container *ngIf="control.hasError('required')">
                Please confirm your password
              </ng-container>
              <ng-container *ngIf="!control.hasError('required') && control.hasError('passwordMismatch')">
                Passwords do not match
              </ng-container>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
    <!-- Password policy -->
    <div class="password-policy">
      <div class="password-policy-title">Reminder to set a strong, personal password to keep your data safe</div>
      <div class="password-policy-list">
        <div class="policy-item">
          <span nz-icon [nzType]="passwordLengthOk ? 'check-circle' : 'close-circle'"
            [nzTheme]="passwordLengthOk?'fill':'outline'"
            [ngClass]="passwordLengthOk ? 'policy-ok' : 'policy-bad'"></span>
          <span>8 or more characters</span>
        </div>
        <div class="policy-item">
          <span nz-icon [nzType]="passwordHasUpper ? 'check-circle' : 'close-circle'"
            [nzTheme]="passwordHasUpper?'fill':'outline'"
            [ngClass]="passwordHasUpper ? 'policy-ok' : 'policy-bad'"></span>
          <span>At least one uppercase letter (A-Z)</span>
        </div>
        <div class="policy-item">
          <span nz-icon [nzType]="passwordHasLower ? 'check-circle' : 'close-circle'"
            [nzTheme]="passwordHasLower?'fill':'outline'"
            [ngClass]="passwordHasLower ? 'policy-ok' : 'policy-bad'"></span>
          <span>At least one lowercase letter (a-z)</span>
        </div>
      </div>
      <div class="password-policy-list">
        <div class="policy-item">
          <span nz-icon [nzType]="passwordHasNumber ? 'check-circle' : 'close-circle'"
            [nzTheme]="passwordHasNumber?'fill':'outline'"
            [ngClass]="passwordHasNumber ? 'policy-ok' : 'policy-bad'"></span>
          <span>At least one number (0-9)</span>
        </div>
        <div class="policy-item">
          <span nz-icon [nzType]="passwordHasSpecial ? 'check-circle' : 'close-circle'"
            [nzTheme]="passwordHasSpecial?'fill':'outline'"
            [ngClass]="passwordHasSpecial ? 'policy-ok' : 'policy-bad'"></span>
          <span>At lest one special character (! &#64; # $ % ^ & *)</span>
        </div>
      </div>
    </div>
    <div class="form-btn">
      <button id="btn-submit" type="submit" class="btn-register" [disabled]="registerForm.invalid || isLoading"
        nz-button nzType="primary" nzBlock nzSize="large">
        Create Account
      </button>
    </div>
  </form>
</div>

<ng-template #successTpl>
  <div class="modal-title-center">
    <span nz-icon nzType="check-circle" nzTheme="fill"
      style="color:#52c41a;font-size:24px;display:block;margin:0 auto 8px;"></span>
    <div style="font-weight:600;">{{ successTitle }}</div>
    <div style="margin-top:4px;">{{ successMessage }}</div>
  </div>
</ng-template>

<!-- Full-screen loading overlay -->
<div class="global-loading-overlay" *ngIf="isLoading">
  <svg class="global-loading-spinner" xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56"
    fill="none" aria-hidden="true" focusable="false">
    <path
      d="M36.6385 5.60895C39.579 6.7434 42.2672 8.44592 44.5497 10.6193C46.8322 12.7927 48.6643 15.3943 49.9414 18.2757C51.2184 21.1571 51.9154 24.2619 51.9926 27.4126C52.0698 30.5634 51.5256 33.6985 50.3911 36.639C49.2567 39.5795 47.5542 42.2677 45.3808 44.5502C43.2074 46.8327 40.6058 48.6648 37.7244 49.9419C34.843 51.2189 31.7382 51.9159 28.5875 51.9931C25.4367 52.0703 22.3015 51.5261 19.3611 50.3916C16.4206 49.2572 13.7324 47.5547 11.4499 45.3813C9.16735 43.2079 7.33527 40.6063 6.05822 37.7249C4.78117 34.8435 4.08416 31.7387 4.00699 28.588C3.92982 25.4372 4.474 22.3021 5.60845 19.3616C6.7429 16.4211 8.44542 13.7329 10.6188 11.4504C12.7922 9.16786 15.3938 7.33578 18.2752 6.05873C21.1566 4.78168 24.2614 4.08467 27.4121 4.0075C30.5629 3.93032 33.698 4.4745 36.6385 5.60895L36.6385 5.60895Z"
      stroke="#D9D9D9" stroke-width="8" />
    <path
      d="M36.6385 5.60895C39.579 6.7434 42.2672 8.44592 44.5497 10.6193C46.8322 12.7927 48.6643 15.3943 49.9414 18.2757C51.2184 21.1571 51.9154 24.2619 51.9926 27.4126C52.0698 30.5634 51.5256 33.6985 50.3911 36.639C49.2567 39.5795 47.5542 42.2677 45.3808 44.5502C43.2074 46.8327 40.6058 48.6648 37.7244 49.9419C34.843 51.2189 31.7382 51.9159 28.5875 51.9931C25.4367 52.0703 22.3015 51.5261 19.3611 50.3916C16.4206 49.2572 13.7324 47.5547 11.4499 45.3813C9.16735 43.2079 7.33527 40.6063 6.05822 37.7249C4.78117 34.8435 4.08416 31.7387 4.00699 28.588C3.92982 25.4372 4.474 22.3021 5.60845 19.3616C6.7429 16.4211 8.44542 13.7329 10.6188 11.4504C12.7922 9.16786 15.3938 7.33578 18.2752 6.05873C21.1566 4.78168 24.2614 4.08467 27.4121 4.0075C30.5629 3.93032 33.698 4.4745 36.6385 5.60895L36.6385 5.60895Z"
      stroke="url(#paint0_linear_5_1830)" stroke-width="8" stroke-linecap="round" />
    <defs>
      <linearGradient id="paint0_linear_5_1830" x1="5.60845" y1="19.3616" x2="50.3911" y2="36.639"
        gradientUnits="userSpaceOnUse">
        <stop stop-color="white" />
        <stop offset="1" stop-color="#FF0066" />
      </linearGradient>
    </defs>
  </svg>
</div>

<ng-template #errorTpl>
  <div class="modal-title-center">
    <span nz-icon nzType="close-circle" nzTheme="fill"
      style="color:#ff4d4f;font-size:24px;display:block;margin:0 auto 8px;"></span>
    <div style="font-weight:600;">{{ errorTitle }}</div>
    <div style="margin-top:4px;">{{ errorMessage }}</div>
  </div>
</ng-template>