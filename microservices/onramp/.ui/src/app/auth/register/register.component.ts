import { Component, ElementRef, ViewChild, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-register',
  standalone: false,
  templateUrl: './register.component.html',
  styleUrl: './register.component.css'
})
export class RegisterComponent {
  registerForm!: FormGroup;
  passwordVisible = false;
  confirmPasswordVisible = false;
  isLoading = false;
  successTitle = 'Account has been created!';
  successMessage = 'Your account has been created successfully. You may now proceed to log in.';
  errorTitle = 'Registration failed';
  errorMessage = 'There was a problem creating your account. Please try again.';
  @ViewChild('successTpl', { static: true }) successTpl!: TemplateRef<any>;
  @ViewChild('errorTpl', { static: true }) errorTpl!: TemplateRef<any>;


  constructor(
    private fb: FormBuilder,
    private message: NzMessageService,
    private router: Router,
    private auth: AuthService,
    private modal: NzModalService
  ) { }

  ngOnInit() {
    this.registerForm = this.fb.group({
      firstname: ['', [Validators.required]],
      lastname: ['', [Validators.required]],
      email: ['', [Validators.required, this.emailValidator]],
      username: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(8), this.passwordPolicyValidator()]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    // Ensure confirmPassword revalidates when password changes
    const passwordControl = this.registerForm.get('password');
    const confirmControl = this.registerForm.get('confirmPassword');
    if (passwordControl && confirmControl) {
      passwordControl.valueChanges.subscribe(() => {
        confirmControl.updateValueAndValidity({ onlySelf: true });
      });
    }
  }

  emailValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Let required validator handle empty values
    }

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const isValid = emailRegex.test(control.value);

    return isValid ? null : { invalidEmail: true };
  }

  // Custom password policy validator
  private passwordPolicyValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value: string = control.value || '';
      if (!value) {
        return null; // required handles empties
      }
      const lengthOk = value.length >= 8;
      const hasUpper = /[A-Z]/.test(value);
      const hasLower = /[a-z]/.test(value);
      const hasNumber = /[0-9]/.test(value);
      const hasSpecial = /[!@#$%^&*]/.test(value);
      const valid = lengthOk && hasUpper && hasLower && hasNumber && hasSpecial;
      return valid ? null : { weakPassword: true };
    };
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    if (confirmPassword && confirmPassword.hasError('passwordMismatch')) {
      confirmPassword.setErrors(null);
    }

    return null;
  }

  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
  }

  toggleConfirmPasswordVisibility() {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }

  onSubmit() {
    if (this.registerForm.valid) {
      this.isLoading = true;
      const formRegister = this.registerForm.value;
      this.auth.registerUser(formRegister).subscribe({
        next: () => {
          const credentials = { username: formRegister.username, password: formRegister.password };
          this.isLoading = false;
          this.modal.success({
            nzContent: this.successTpl,
            nzOkText: 'Continue',
            nzOkType: 'default',
            nzCentered: true,
            nzMaskClosable: false,
            nzClosable: false,
            nzIconType: '' as any,
            nzClassName: 'register-modal',
            nzOnOk: () => new Promise<void>((resolve) => {
              this.isLoading = true;
              this.auth.loginApi(credentials).subscribe({
                next: () => {
                  this.auth.getProfile().subscribe({
                    next: () => {
                      this.auth.getUserPermissions().subscribe({
                        next: () => {
                          this.router.navigate(['/']);
                          this.isLoading = false;
                          resolve();
                        },
                        error: () => {
                          this.router.navigate(['/login']);
                          this.isLoading = false;
                          resolve();
                        }
                      });
                    },
                    error: () => {
                      this.router.navigate(['/login']);
                      this.isLoading = false;
                      resolve();
                    }
                  });
                },
                error: () => {
                  this.message.create('error', 'Login failed. Please check your credentials.', { nzDuration: 5000 });
                  this.isLoading = false;
                  resolve();
                }
              });
            })
          });
        },
        error: (err) => {
          this.isLoading = false;
          this.errorTitle = 'Issue with creating account';
          this.errorMessage = (err && err.error && (err.error.message || err.error.error)) ? (err.error.message || err.error.error) : 'There was a problem creating your account. Please try again.';
          this.modal.error({
            nzContent: this.errorTpl,
            nzOkText: 'Try Again',
            nzOkType: 'default',
            nzCentered: true,
            nzMaskClosable: false,
            nzClosable: false,
            nzIconType: '' as any,
            nzClassName: 'register-modal'
          });
        }
      });
    } else {
      Object.values(this.registerForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  handleLogin() {
    this.router.navigate(['/login']);
  }

  // Helper: show green tick when a control is valid and interacted with
  isControlValid(controlName: string): boolean {
    const control = this.registerForm.get(controlName);
    return !!control && control.valid && (control.dirty || control.touched);
  }

  get firstname() { return this.registerForm.get('firstname'); }
  get lastname() { return this.registerForm.get('lastname'); }
  get email() { return this.registerForm.get('email'); }
  get username() { return this.registerForm.get('username'); }
  get password() { return this.registerForm.get('password'); }
  get confirmPassword() { return this.registerForm.get('confirmPassword'); }

  // Computed password policy flags for dynamic UI hints
  get passwordValue(): string {
    const control = this.password;
    return (control && control.value) ? String(control.value) : '';
  }

  get passwordLengthOk(): boolean {
    return this.passwordValue.length >= 8;
  }

  get passwordHasUpper(): boolean {
    return /[A-Z]/.test(this.passwordValue);
  }

  get passwordHasLower(): boolean {
    return /[a-z]/.test(this.passwordValue);
  }

  get passwordHasNumber(): boolean {
    return /[0-9]/.test(this.passwordValue);
  }

  get passwordHasSpecial(): boolean {
    return /[!@#$%^&*]/.test(this.passwordValue);
  }
}
