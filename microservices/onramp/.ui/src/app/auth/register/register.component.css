::ng-deep body {
  background: linear-gradient(180deg, rgba(253, 39, 125, 0.25) 0%, rgba(255, 121, 30, 0.5) 100%), url('/images/wallpaper.jpg');
  background-size: cover;
  background-blend-mode: overlay;
}

.register-container {
  width: 1020px;
  margin: 0 auto;
  padding: 40px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #D9D9D9;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.register-container h2 {
  font-size: 24px;
  margin-bottom: 8px;
  font-weight: bold;
  text-align: center;
  color: #1f2937;
}

.register-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 32px;
  text-align: center;
}

.register-container .form-register nz-form-control ::ng-deep .ant-form-item-control-input-content {
  display: grid;
  gap: 8px;
}

.register-container .form-register button,
.register-container nz-form-control ::ng-deep .ant-form-item-control-input-content nz-input-group {
  /* height: 42px; */
  padding: 0 15px;
  border-radius: 4px;
}

nz-form-item {
  margin-bottom: 0;
}

nz-form-control {
  width: 100%;
}

.form-btn {
  margin-top: 24px;
  text-align: center;
}

.form-btn .btn-register {
  width: 460px;
  font-size: 16px;
  font-weight: 700;
}

/* Password policy (static helper text) */
.password-policy {
  margin-top: 8px;
  color: #6b7280;
}

.password-policy-title {
  font-size: 14px;
  margin-bottom: 8px;
}

.password-policy-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px 12px;
  margin: 0;
  padding: 0;
}

.password-policy-list .policy-item {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.password-policy-list .policy-item [nz-icon] {
  font-size: 14px;
}

.policy-ok {
  color: #52c41a !important;
}

.policy-bad {
  color: #ff4d4f !important;
}

/* Removed legacy nzHasFeedback-related blocks and unused commented styles */

[nz-icon] {
  color: rgba(0, 0, 0, 0.45);
}

/* (none) */

/* Suffix tick styles used in template */
.tick-success {
  color: #52c41a;
}

.tick-gap {
  margin-right: 6px;
}

.no-radius {
  border-radius: 0;
}

/* Form row layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .register-container {
    max-width: 100%;
    margin: 20px 16px;
    padding: 16px;
    position: relative;
    top: auto;
    left: auto;
    transform: none;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 16px;
  }

  .register-container h2 {
    font-size: 20px;
    text-align: center;
  }

  .register-subtitle {
    font-size: 12px;
    margin-bottom: 24px;
    text-align: center;
  }

  .register-container .form-register nz-form-control ::ng-deep .ant-form-item-control-input-content {
    gap: 6px;
  }

  .register-container .form-register button,
  .register-container nz-form-control ::ng-deep .ant-form-item-control-input-content input {
    height: 36px;
  }

  nz-form-item,
  .register-container .form-register hr,
  .register-container .form-btn .btn-register {
    margin-bottom: 16px;
  }

  nz-form-control .ant-form-item-explain-error {
    font-size: 11px;
  }

  nz-button {
    margin-top: 8px;
  }

  .form-btn .btn-register {
    width: 100%;
  }

  .password-policy-title {
    font-size: 11px;
  }

  .password-policy-list {
    gap: 10px 16px;
  }

  .register-container {
    width: calc(100vw - 40px)
  }
}

/* Center custom modal title (with icon inside template) */
.modal-title-center {
  text-align: center;
}

::ng-deep .ant-modal-confirm-body>.anticon+.ant-modal-confirm-title+.ant-modal-confirm-content {
  margin-left: 0;
}

/* Center the confirm modal buttons horizontally */
::ng-deep .register-modal .ant-modal-confirm-btns {
  display: flex;
  justify-content: center;
}

/* Register modal - make the confirm button full width */
::ng-deep .register-modal .ant-modal-confirm-btns {
  width: 100%;
  display: block;
  padding: 0;
  margin: 16px 0 0 0;
  text-align: unset;
}

::ng-deep .register-modal .ant-modal-confirm-btns button {
  width: 100%;
  display: block;
}

/* Desktop Adjustments (for larger screens) */
@media (min-width: 769px) {
  .register-container {
    min-height: 500px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

/* Full-screen loading overlay shown during submit */
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: rgba(0, 0, 0, 0.45); */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  /* above Ant Design modals */
  pointer-events: all;
  /* block interactions while loading */
}

/* Use provided SVG and rotate it */
.global-loading-spinner {
  display: block;
  width: 56px;
  height: 56px;
  animation: global-spinner-rotate 0.9s linear infinite;
}

@keyframes global-spinner-rotate {
  to {
    transform: rotate(1turn);
  }
}