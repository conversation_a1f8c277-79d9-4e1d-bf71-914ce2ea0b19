import { Component, ElementRef, ViewChild } from '@angular/core';
import { AuthService, User, UserProfile } from './core/services/auth.service';
import { catchError, of, Observable, filter, map } from 'rxjs';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ScrollService } from './core/services/Scroll.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  standalone: false,
})
export class AppComponent {
  @ViewChild('content') content!: ElementRef;
  user: UserProfile | null = null;

  constructor(
    private auth: AuthService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private scrollService: ScrollService
  ) {
  }
  ngAfterViewInit() {
    this.scrollService.onScrollToTop().subscribe(() => {
      this.content.nativeElement.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
  ngOnInit() {
    const storedPermissions = localStorage.getItem('permissions');
    if (!storedPermissions) {
      this.auth.getProfile().subscribe({
        next: profile => this.user = profile,
        error: () => this.user = null
      });
    }
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => {
        let route = this.activatedRoute;
        while (route.firstChild) {
          route = route.firstChild;
        }
        return route;
      }),
      map(route => route.snapshot.data['title'])
    ).subscribe(title => {
      this.titleService.setTitle(title || 'OnrampUi');
    });
  }
  isWhiteScreenRoute(): boolean {
    return this.router.url === '/login' || this.router.url === '/account/register' || this.router.url === '/forgot-password';
  }

}
