import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DevicesService {
  constructor(private http: HttpClient) { }
  getDevices(): Observable<any[]> {
    return this.http.get<any[]>('/api/devices');
  }
  getDevicesById(id: string): Observable<any> {
    return this.http.get<any>(`/api/organizations/${id}/device`);
  }

}