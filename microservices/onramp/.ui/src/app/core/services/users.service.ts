import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UsersService {
  constructor(private http: HttpClient) { }
  getRoleUsers(organizationId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${organizationId}/roles`);
  }

  getUsersList(organizationId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${organizationId}/users`);
  }

  updateUser(organizationId: any, data: any): Observable<any> {
    return this.http.patch<any>(`/api/organizations/${organizationId}/users/${data.userId}`, {
      roleIds: data.roleId
    });
  }

  deleteUser(organizationId: any, userId: any): Observable<any> {
    return this.http.delete<any>(`/api/organizations/${organizationId}/users/${userId}`);
  }

  getInvitations(userId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${userId}/invites`);
  }

  createInvaitions(organizationId: any, data: any): Observable<any[]> {
    return this.http.post<any[]>(`/api/organizations/${organizationId}/invites`, data);
  }

  deleteInvite(organizationId: any, data: any, actor: any) {
    return this.http.delete<any>(`/api/organizations/${organizationId}/invites/${data.id}`, {
      body: actor
    });
  }

  resendInvite(organizationId: any, data: any) {
    return this.http.post<any>(`/api/organizations/${organizationId}/invites/${data.id}/resend`, data);
  }
}
