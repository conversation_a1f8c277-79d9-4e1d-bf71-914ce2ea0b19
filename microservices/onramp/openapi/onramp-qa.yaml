components: {}
definitions:
    app.UserProfile:
        properties:
            email:
                type: string
            name:
                type: string
        type: object
    config.GatewayConfigTemplate:
        properties:
            description:
                type: string
            id:
                type: string
            isDeleted:
                type: boolean
            name:
                type: string
            organizationId:
                type: string
        type: object
    config.GatewayConfigTemplateBaseSetting:
        properties:
            defaultValue:
                type: string
            description:
                type: string
            format:
                description: JSONB stored as string
                type: string
            isDeleted:
                type: boolean
            name:
                type: string
            setting:
                type: string
        type: object
    config.GatewayConfigTemplateSetting:
        properties:
            gatewayConfigTemplateId:
                type: string
            setting:
                type: string
            value:
                type: string
        type: object
    config.GatewayConfigTemplateSettingOverride:
        properties:
            setting:
                type: string
            softwareGatewayId:
                type: string
            value:
                type: string
        type: object
    config.SoftwareGateway:
        properties:
            config:
                type: string
            description:
                type: string
            id:
                type: string
            name:
                type: string
            templateId:
                type: string
        type: object
    device.DeviceResponse:
        properties:
            createdAt:
                type: string
            description:
                type: string
            enableRealtime:
                type: boolean
            flushConnectionMs:
                type: integer
            id:
                type: string
            ipAddress:
                type: string
            isEnabled:
                type: boolean
            locationId:
                type: string
            name:
                type: string
            organizationId:
                type: string
            origId:
                type: integer
            port:
                type: integer
            serialNumber:
                type: string
            softwareGatewayId:
                type: string
            type:
                type: string
            updatedAt:
                type: string
        type: object
    device.DevicesResponse:
        properties:
            devices:
                items:
                    $ref: '#/definitions/device.DeviceResponse'
                type: array
        type: object
    devicegroups.DeviceGroups:
        properties:
            createdat:
                type: string
            id:
                type: string
            name:
                type: string
            organizationid:
                type: string
            updatedat:
                type: string
        type: object
    devicegroupusers.CreateDeviceGroupRoleAssignmentRequest:
        properties:
            roleIds:
                items:
                    type: string
                minItems: 1
                type: array
            userId:
                type: string
        required:
            - roleIds
            - userId
        type: object
    devicegroupusers.DeviceGroupRoleAssignment:
        properties:
            createdAt:
                type: string
            deviceGroupId:
                type: string
            membershipId:
                type: string
            roleId:
                type: string
            updatedAt:
                type: string
        type: object
    devicegroupusers.DeviceGroupRoleAssignmentResponse:
        properties:
            deviceGroupRoleAssignment:
                items:
                    $ref: '#/definitions/devicegroupusers.DeviceGroupRoleAssignment'
                type: array
        type: object
    devicegroupusers.DeviceGroupUser:
        properties:
            email:
                type: string
            firstName:
                type: string
            lastName:
                type: string
            roleIds:
                items:
                    type: string
                type: array
            userId:
                type: string
            userName:
                type: string
        type: object
    devicegroupusers.DeviceGroupUsersResponse:
        properties:
            users:
                items:
                    $ref: '#/definitions/devicegroupusers.DeviceGroupUser'
                type: array
        type: object
    devicegroupusers.UpdateDeviceGroupRoleAssignmentRequest:
        properties:
            roleIds:
                items:
                    type: string
                minItems: 1
                type: array
        required:
            - roleIds
        type: object
    emailverification.RequestVerificationRequest:
        properties:
            expiryMinutes:
                type: integer
        type: object
    emailverification.VerificationResponse:
        properties:
            authMethodId:
                type: string
            created:
                type: string
            expired:
                type: string
            id:
                type: string
            retried:
                type: string
            retryCount:
                type: integer
            sent:
                type: string
            status:
                type: string
            updated:
                type: string
        type: object
    invites.InviteResponse:
        properties:
            created:
                type: string
            customroleid:
                type: string
            email:
                type: string
            expired:
                type: string
            id:
                type: string
            inviterid:
                type: string
            message:
                type: string
            organizationidentifier:
                type: string
            requiresso:
                type: boolean
            retried:
                type: string
            retrycount:
                type: integer
            sent:
                type: string
            status:
                type: string
            updated:
                type: string
        type: object
    locationgrouproleassignments.LocationGroupRoleAssignment:
        properties:
            createdAt:
                type: string
            locationGroupId:
                type: string
            membershipId:
                type: string
            roleId:
                type: string
            updatedAt:
                type: string
        type: object
    locationgrouproleassignments.LocationGroupRoleAssignmentResponse:
        properties:
            locationGroupRoleAssignment:
                items:
                    $ref: '#/definitions/locationgrouproleassignments.LocationGroupRoleAssignment'
                type: array
        type: object
    locationgrouproleassignments.LocationGroupUser:
        properties:
            email:
                type: string
            firstName:
                type: string
            lastName:
                type: string
            roleIds:
                items:
                    type: string
                type: array
            userId:
                type: string
            userName:
                type: string
        type: object
    locationgrouproleassignments.LocationGroupUsersResponse:
        properties:
            users:
                items:
                    $ref: '#/definitions/locationgrouproleassignments.LocationGroupUser'
                type: array
        type: object
    locationgroups.LocationGroupResponse:
        properties:
            createdat:
                type: string
            id:
                type: string
            name:
                type: string
            organizationid:
                type: string
            updatedat:
                type: string
        type: object
    locationgroups.LocationGroupsResponse:
        properties:
            locationgroups:
                items:
                    $ref: '#/definitions/locationgroups.LocationGroupResponse'
                type: array
        type: object
    locations.Location:
        properties:
            createdat:
                type: string
            description:
                type: string
            id:
                type: string
            latitude:
                type: number
            longitude:
                type: number
            name:
                type: string
            organizationid:
                type: string
            updatedat:
                type: string
        type: object
    organization.OrganizationResponse:
        properties:
            createdat:
                type: string
            description:
                type: string
            id:
                type: string
            name:
                type: string
            orgtypeidentifier:
                type: string
            updatedat:
                type: string
        type: object
    permissions.PermissionGroupResponse:
        properties:
            description:
                type: string
            name:
                type: string
            roles:
                items:
                    $ref: '#/definitions/permissions.RolePermissions'
                type: array
            scope:
                type: string
            weight:
                type: number
        type: object
    permissions.PermissionValue:
        properties:
            default_value:
                type: boolean
            inherited:
                type: boolean
            machine_name:
                type: string
            name:
                type: string
            value:
                type: boolean
            weight:
                type: number
        type: object
    permissions.PermissionsResponse:
        properties:
            permissions:
                items:
                    $ref: '#/definitions/permissions.PermissionGroupResponse'
                type: array
        type: object
    permissions.RolePermissions:
        properties:
            description:
                type: string
            name:
                type: string
            permissions:
                items:
                    $ref: '#/definitions/permissions.PermissionValue'
                type: array
        type: object
    profile.updateProfileRequest:
        properties:
            description:
                example: Updated description
                type: string
            firstName:
                example: John
                type: string
            lastName:
                example: Doe
                type: string
            mobile:
                example: "+1234567890"
                type: string
            notificationSmsEnabled:
                example: true
                type: boolean
        type: object
    response.BadRequestResponse:
        description: Standard bad request response structure matching the actual implementation
        properties:
            code:
                example: 400
                type: integer
            data: {}
            message:
                example: Bad Request
                type: string
            status:
                example: error
                type: string
        type: object
    response.ForbiddenResponse:
        description: Standard forbidden response structure matching the actual implementation
        properties:
            code:
                example: 403
                type: integer
            data: {}
            message:
                example: Forbidden
                type: string
            status:
                example: forbidden
                type: string
        type: object
    response.InternalErrorResponse:
        description: Standard internal server error response structure matching the actual implementation
        properties:
            code:
                example: 500
                type: integer
            data: {}
            message:
                example: Internal Server Error
                type: string
            status:
                example: error
                type: string
        type: object
    response.MethodNotAllowedResponse:
        description: Standard method not allowed response structure matching the actual implementation
        properties:
            code:
                example: 405
                type: integer
            data: {}
            message:
                example: Method Not Allowed
                type: string
            status:
                example: error
                type: string
        type: object
    response.NotFoundResponse:
        description: Standard not found response structure matching the actual implementation
        properties:
            code:
                example: 404
                type: integer
            data: {}
            message:
                example: Not Found
                type: string
            status:
                example: error
                type: string
        type: object
    response.SuccessResponse:
        description: Standard success response structure matching the actual implementation
        properties:
            code:
                example: 200
                type: integer
            data: {}
            message:
                example: Request Succeeded
                type: string
            status:
                example: success
                type: string
        type: object
    response.UnauthorizedResponse:
        description: Standard unauthorized response structure matching the actual implementation
        properties:
            code:
                example: 401
                type: integer
            data: {}
            message:
                example: Unauthorized
                type: string
            status:
                example: error
                type: string
        type: object
    roles.CustomRole:
        properties:
            createdAt:
                type: string
            description:
                type: string
            id:
                type: string
            isDeletable:
                type: boolean
            name:
                type: string
            orgTypeIdentifier:
                type: string
            organizationId:
                type: string
            templateRoleIdentifier:
                type: string
            updatedAt:
                type: string
        type: object
    softwaregateway.SoftwareGatewayResponse:
        properties:
            apikey:
                type: string
            config:
                type: string
            createdat:
                type: string
            datelastcheckedinutc:
                type: string
            description:
                type: string
            gatewayversion:
                type: string
            id:
                type: string
            isenabled:
                type: boolean
            machinekey:
                type: string
            name:
                type: string
            organizationid:
                type: string
            pushconfigonnextcheck:
                type: boolean
            templateid:
                type: string
            token:
                type: string
            updatedat:
                type: string
        type: object
    user.User:
        properties:
            createdAt:
                type: string
            description:
                type: string
            firstName:
                type: string
            id:
                type: string
            isDeleted:
                type: boolean
            lastLogin:
                type: string
            lastName:
                type: string
            mobile:
                type: string
            notificationSmsEnabled:
                type: boolean
            updatedAt:
                type: string
        type: object
info:
    contact:
        email: <EMAIL>
        name: API Support
        url: http://www.synapse-its.com/support
    description: Comprehensive API documentation for the Onramp microservice
    title: Onramp Service API
    version: 1.0.0
openapi: ""
paths:
    /api/invites/validate/{token}:
        get:
            description: Validate an invite token
            parameters:
                - description: Invite token
                  in: path
                  name: token
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Invite validated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/invites.InviteResponse'
                              type: object
                "400":
                    description: Bad request - invalid token
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Invite not found or expired
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Validate invite
            tags:
                - invites
    /api/organization/{organizationId}/softwaregateway:
        get:
            description: Get all software gateways for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Gateways retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/softwaregateway.SoftwareGatewayResponse'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: List software gateways
            tags:
                - softwaregateway
        post:
            consumes:
                - application/json
            description: Create a new software gateway for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Gateway data
                  in: body
                  name: gateway
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Gateway created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/softwaregateway.SoftwareGatewayResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or gateway data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create software gateway
            tags:
                - softwaregateway
    /api/organization/{organizationId}/softwaregateway/{identifier}:
        delete:
            description: Delete a software gateway
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Gateway identifier
                  in: path
                  name: identifier
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Gateway deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID or identifier
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Gateway not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete software gateway
            tags:
                - softwaregateway
        get:
            description: Get a software gateway by its identifier
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Gateway identifier
                  in: path
                  name: identifier
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Gateway retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/softwaregateway.SoftwareGatewayResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or identifier
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Gateway not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get software gateway by identifier
            tags:
                - softwaregateway
        patch:
            consumes:
                - application/json
            description: Update an existing software gateway
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Gateway identifier
                  in: path
                  name: identifier
                  required: true
                  type: string
                - description: Updated gateway data
                  in: body
                  name: gateway
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Gateway updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/softwaregateway.SoftwareGatewayResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID, identifier, or gateway data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Gateway not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update software gateway
            tags:
                - softwaregateway
    /api/organization/{organizationId}/softwaregateway/{identifier}/config:
        get:
            description: Get software gateway configuration by its identifier
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Software gateway identifier
                  in: path
                  name: identifier
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Software gateway retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/config.SoftwareGateway'
                              type: object
                "400":
                    description: Bad request - invalid identifier
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get software gateway by identifier
            tags:
                - softwaregatewayconfig
        patch:
            consumes:
                - application/json
            description: Upsert multiple setting overrides for a software gateway (legacy endpoint)
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Software gateway identifier
                  in: path
                  name: identifier
                  required: true
                  type: string
                - description: Map of setting names to values
                  in: body
                  name: overrides
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Setting overrides upserted successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/config.GatewayConfigTemplateSettingOverride'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid software gateway ID or overrides data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Bulk upsert setting overrides (legacy)
            tags:
                - softwaregatewayconfig
    /api/organization/{organizationId}/softwaregateway/{identifier}/config/overrides:
        get:
            description: Get all setting overrides for a software gateway
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Software gateway ID
                  in: path
                  name: identifier
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Setting overrides retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/config.GatewayConfigTemplateSettingOverride'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID or software gateway ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get setting overrides
            tags:
                - softwaregatewayconfig
        post:
            consumes:
                - application/json
            description: Create or update a setting override for a software gateway
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Software gateway ID
                  in: path
                  name: identifier
                  required: true
                  type: string
                - description: Setting override data
                  in: body
                  name: override
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Setting override created/updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/config.GatewayConfigTemplateSettingOverride'
                              type: object
                "400":
                    description: Bad request - invalid organization ID, software gateway ID, or override data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create or update setting override
            tags:
                - softwaregatewayconfig
    /api/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/{setting}:
        delete:
            description: Delete a setting override for a software gateway
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Software gateway ID
                  in: path
                  name: identifier
                  required: true
                  type: string
                - description: Setting name
                  in: path
                  name: setting
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Setting override deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID, software gateway ID, or setting name
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete setting override
            tags:
                - softwaregatewayconfig
    /api/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/bulk:
        post:
            consumes:
                - application/json
            description: Upsert multiple setting overrides for a software gateway
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Software gateway identifier
                  in: path
                  name: identifier
                  required: true
                  type: string
                - description: Map of setting names to values
                  in: body
                  name: overrides
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Setting overrides upserted successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/config.GatewayConfigTemplateSettingOverride'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID, software gateway ID, or overrides data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Bulk upsert setting overrides
            tags:
                - softwaregatewayconfig
        put:
            consumes:
                - application/json
            description: Replace all setting overrides for a software gateway
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Software gateway ID
                  in: path
                  name: identifier
                  required: true
                  type: string
                - description: Array of setting overrides
                  in: body
                  name: overrides
                  required: true
                  schema:
                    items:
                        $ref: '#/definitions/config.GatewayConfigTemplateSettingOverride'
                    type: array
            produces:
                - application/json
            responses:
                "200":
                    description: Setting overrides replaced successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID, software gateway ID, or overrides data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Bulk replace setting overrides
            tags:
                - softwaregatewayconfig
    /api/organization/{organizationId}/softwaregatewayconfig/templates:
        get:
            description: Get all gateway configuration templates for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Gateway config templates retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/config.GatewayConfigTemplate'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: List gateway config templates
            tags:
                - softwaregatewayconfig
        post:
            consumes:
                - application/json
            description: Create a new gateway configuration template
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Gateway config template data
                  in: body
                  name: template
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Gateway config template created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/config.GatewayConfigTemplate'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or template data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Organization not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create gateway config template
            tags:
                - softwaregatewayconfig
    /api/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}:
        delete:
            description: Delete a gateway configuration template
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Template ID
                  in: path
                  name: templateId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Gateway config template deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID or template ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Template not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete gateway config template
            tags:
                - softwaregatewayconfig
        get:
            description: Get a specific gateway configuration template
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Template ID
                  in: path
                  name: templateId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Gateway config template retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/config.GatewayConfigTemplate'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or template ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Template not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get gateway config template
            tags:
                - softwaregatewayconfig
        patch:
            consumes:
                - application/json
            description: Update an existing gateway configuration template
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Template ID
                  in: path
                  name: templateId
                  required: true
                  type: string
                - description: Updated gateway config template data
                  in: body
                  name: template
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Gateway config template updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/config.GatewayConfigTemplate'
                              type: object
                "400":
                    description: Bad request - invalid organization ID, template ID, or template data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Template not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update gateway config template
            tags:
                - softwaregatewayconfig
    /api/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings:
        get:
            description: Get all settings for a gateway config template
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Template ID
                  in: path
                  name: templateId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Template settings retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/config.GatewayConfigTemplateSetting'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID or template ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get template settings
            tags:
                - softwaregatewayconfig
        post:
            consumes:
                - application/json
            description: Create or update a setting for a gateway config template
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Template ID
                  in: path
                  name: templateId
                  required: true
                  type: string
                - description: Template setting data
                  in: body
                  name: setting
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Template setting created/updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/config.GatewayConfigTemplateSetting'
                              type: object
                "400":
                    description: Bad request - invalid organization ID, template ID, or setting data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create or update template setting
            tags:
                - softwaregatewayconfig
    /api/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings/{setting}:
        delete:
            description: Delete a setting from a gateway config template
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Template ID
                  in: path
                  name: templateId
                  required: true
                  type: string
                - description: Setting name
                  in: path
                  name: setting
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Template setting deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID, template ID, or setting name
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete template setting
            tags:
                - softwaregatewayconfig
    /api/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings/bulk:
        put:
            consumes:
                - application/json
            description: Replace all settings for a gateway config template
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Template ID
                  in: path
                  name: templateId
                  required: true
                  type: string
                - description: Array of template settings
                  in: body
                  name: settings
                  required: true
                  schema:
                    items:
                        $ref: '#/definitions/config.GatewayConfigTemplateSetting'
                    type: array
            produces:
                - application/json
            responses:
                "200":
                    description: Template settings replaced successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID, template ID, or settings data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Bulk replace template settings
            tags:
                - softwaregatewayconfig
    /api/organizations:
        get:
            description: Get all organizations that the user has access to. If user has 'synapse_view_organizations' permission, returns all organizations. Otherwise, returns only organizations the user belongs to.
            produces:
                - application/json
            responses:
                "200":
                    description: Organizations retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/organization.OrganizationResponse'
                                    type: array
                              type: object
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: List organizations
            tags:
                - organizations
        post:
            consumes:
                - application/json
            description: Create a new organization
            parameters:
                - description: Organization data
                  in: body
                  name: organization
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Organization created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/organization.OrganizationResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create organization
            tags:
                - organizations
    /api/organizations/{organizationId}:
        delete:
            description: Delete an organization
            parameters:
                - description: Organization identifier
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Organization deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organizationId
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Organization not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete organization
            tags:
                - organizations
        get:
            description: Get organization by its organizationId
            parameters:
                - description: Organization identifier
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Organization retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/organization.OrganizationResponse'
                              type: object
                "400":
                    description: Bad request - invalid organizationId
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Organization not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get organization by organizationId
            tags:
                - organizations
        patch:
            consumes:
                - application/json
            description: Update an existing organization
            parameters:
                - description: Organization identifier
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Updated organization data
                  in: body
                  name: organization
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Organization updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/organization.OrganizationResponse'
                              type: object
                "400":
                    description: Bad request - invalid organizationId or data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Organization not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update organization
            tags:
                - organizations
    /api/organizations/{organizationId}/aps-factory-reset:
        post:
            consumes:
                - application/json
            description: Generate APS factory reset code for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: 8-character challenge code
                  in: body
                  name: challengeCode
                  required: true
                  schema:
                    properties:
                        challengeCode:
                            type: string
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: APS factory reset code generated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    type: string
                              type: object
                "400":
                    description: Bad request - invalid organization ID or challenge code
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "403":
                    description: Forbidden - insufficient permissions
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
            summary: APS factory reset
            tags:
                - organizations
    /api/organizations/{organizationId}/device:
        get:
            description: Get all devices for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Devices retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/device.DevicesResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: List organization devices
            tags:
                - devices
        post:
            consumes:
                - application/json
            description: Create a new device for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device data
                  in: body
                  name: device
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Device created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/device.DeviceResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or device data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create device
            tags:
                - devices
    /api/organizations/{organizationId}/device/{deviceId}:
        delete:
            description: Delete a device
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device ID
                  in: path
                  name: deviceId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Device deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID or device ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Device not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete device
            tags:
                - devices
        patch:
            consumes:
                - application/json
            description: Update an existing device
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device ID
                  in: path
                  name: deviceId
                  required: true
                  type: string
                - description: Updated device data
                  in: body
                  name: device
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Device updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/device.DeviceResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID, device ID, or device data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Device not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update device
            tags:
                - devices
    /api/organizations/{organizationId}/devicegroups:
        get:
            description: Get all device groups for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Device groups retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/devicegroups.DeviceGroups'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get device groups
            tags:
                - devicegroups
        post:
            consumes:
                - application/json
            description: Create a new device group for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device group data
                  in: body
                  name: devicegroup
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Device group created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/devicegroups.DeviceGroups'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or device group data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Organization not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create device group
            tags:
                - devicegroups
    /api/organizations/{organizationId}/devicegroups/{deviceGroupId}/users:
        get:
            description: Retrieve all users and their roles within a specific device group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device Group ID
                  in: path
                  name: deviceGroupId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Users retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/devicegroupusers.DeviceGroupUsersResponse'
                              type: object
                "400":
                    description: Bad request - invalid parameters
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get all users in device group
            tags:
                - devicegroupusers
        post:
            consumes:
                - application/json
            description: Assign a role to a user within a device group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device Group ID
                  in: path
                  name: deviceGroupId
                  required: true
                  type: string
                - description: Role assignment data
                  in: body
                  name: request
                  required: true
                  schema:
                    $ref: '#/definitions/devicegroupusers.CreateDeviceGroupRoleAssignmentRequest'
            produces:
                - application/json
            responses:
                "200":
                    description: Role assignment created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/devicegroupusers.DeviceGroupRoleAssignmentResponse'
                              type: object
                "400":
                    description: Bad request - invalid parameters
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create device group role assignment
            tags:
                - devicegroupusers
    /api/organizations/{organizationId}/devicegroups/{deviceGroupId}/users/{userId}:
        delete:
            description: Remove a user's role assignment from a device group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device Group ID
                  in: path
                  name: deviceGroupId
                  required: true
                  type: string
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Role assignment deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid parameters
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete device group role assignment
            tags:
                - devicegroupusers
        patch:
            consumes:
                - application/json
            description: Update the role assignment for a user within a device group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device Group ID
                  in: path
                  name: deviceGroupId
                  required: true
                  type: string
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Updated role assignment data
                  in: body
                  name: request
                  required: true
                  schema:
                    $ref: '#/definitions/devicegroupusers.UpdateDeviceGroupRoleAssignmentRequest'
            produces:
                - application/json
            responses:
                "200":
                    description: Role assignment updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/devicegroupusers.DeviceGroupRoleAssignmentResponse'
                              type: object
                "400":
                    description: Bad request - invalid parameters
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update device group role assignment
            tags:
                - devicegroupusers
    /api/organizations/{organizationId}/devicegroups/{devicegroupId}:
        delete:
            description: Delete a device group from an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device group ID
                  in: path
                  name: devicegroupId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Device group deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid IDs or device group has devices
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Device group not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete device group
            tags:
                - devicegroups
        patch:
            consumes:
                - application/json
            description: Update an existing device group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Device group ID
                  in: path
                  name: devicegroupId
                  required: true
                  type: string
                - description: Updated device group data
                  in: body
                  name: devicegroup
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Device group updated successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID, device group ID, or device group data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Device group not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update device group
            tags:
                - devicegroups
    /api/organizations/{organizationId}/invites:
        get:
            description: Get all invites for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Invites retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/invites.InviteResponse'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: List organization invites
            tags:
                - organizations
        post:
            consumes:
                - application/json
            description: Create a new invite for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Invite data
                  in: body
                  name: invite
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Invite created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/invites.InviteResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or invite data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create organization invite
            tags:
                - organizations
    /api/organizations/{organizationId}/invites/{inviteId}/resend:
        post:
            description: Resend an existing invite
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Invite ID
                  in: path
                  name: inviteId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Invite resent successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID or invite ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Invite not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Resend invite
            tags:
                - invites
    /api/organizations/{organizationId}/invites/{inviteId}/revoke:
        post:
            description: Revoke an existing invite
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Invite ID
                  in: path
                  name: inviteId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Invite revoked successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID or invite ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Invite not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Revoke invite
            tags:
                - invites
    /api/organizations/{organizationId}/locationgroups:
        get:
            description: Get all location groups for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Location groups retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/locationgroups.LocationGroupsResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get location groups
            tags:
                - locationgroups
        post:
            consumes:
                - application/json
            description: Create a new location group for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location group data
                  in: body
                  name: locationgroup
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Location group created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/locationgroups.LocationGroupResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or location group data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Organization not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create location group
            tags:
                - locationgroups
    /api/organizations/{organizationId}/locationgroups/{locationGroupId}/users:
        get:
            description: Get all users and their role assignments for a location group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location group ID
                  in: path
                  name: locationGroupId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Users retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/locationgrouproleassignments.LocationGroupUsersResponse'
                              type: object
                "400":
                    description: Bad request - invalid IDs
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get all users in location group
            tags:
                - locationgrouproleassignments
    /api/organizations/{organizationId}/locationgroups/{locationgroupId}:
        delete:
            description: Delete a location group from an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location group ID
                  in: path
                  name: locationgroupId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Location group deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID or location group ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Location group not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete location group
            tags:
                - locationgroups
        patch:
            consumes:
                - application/json
            description: Update an existing location group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location group ID
                  in: path
                  name: locationgroupId
                  required: true
                  type: string
                - description: Updated location group data
                  in: body
                  name: locationgroup
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Location group updated successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID, location group ID, or location group data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Location group not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update location group
            tags:
                - locationgroups
    /api/organizations/{organizationId}/locationgroups/{locationgroupId}/users:
        post:
            consumes:
                - application/json
            description: Assign roles to a user for a location group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location group ID
                  in: path
                  name: locationGroupId
                  required: true
                  type: string
                - description: Role assignment data
                  in: body
                  name: assignment
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Role assignment created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/locationgrouproleassignments.LocationGroupRoleAssignmentResponse'
                              type: object
                "400":
                    description: Bad request - invalid IDs or assignment data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create location group role assignment
            tags:
                - locationgrouproleassignments
    /api/organizations/{organizationId}/locationgroups/{locationgroupId}/users/{userId}:
        delete:
            description: Remove a role assignment for a user in a location group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location group ID
                  in: path
                  name: locationGroupId
                  required: true
                  type: string
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Role assignment deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid IDs
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete location group role assignment
            tags:
                - locationgrouproleassignments
        patch:
            consumes:
                - application/json
            description: Update an existing role assignment for a user in a location group
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location group ID
                  in: path
                  name: locationGroupId
                  required: true
                  type: string
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Updated role assignment data
                  in: body
                  name: assignment
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Role assignment updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/locationgrouproleassignments.LocationGroupRoleAssignmentResponse'
                              type: object
                "400":
                    description: Bad request - invalid IDs or assignment data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update location group role assignment
            tags:
                - locationgrouproleassignments
    /api/organizations/{organizationId}/locations:
        get:
            description: Get all locations for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Locations retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/locations.Location'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get locations
            tags:
                - locations
        post:
            consumes:
                - application/json
            description: Create a new location for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location data
                  in: body
                  name: location
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Location created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/locations.Location'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or location data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Organization not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create location
            tags:
                - locations
    /api/organizations/{organizationId}/locations/{locationId}:
        delete:
            description: Delete a location from an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location ID
                  in: path
                  name: locationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Location deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID or location ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Location not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete location
            tags:
                - locations
        patch:
            consumes:
                - application/json
            description: Update an existing location
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Location ID
                  in: path
                  name: locationId
                  required: true
                  type: string
                - description: Updated location data
                  in: body
                  name: location
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Location updated successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid organization ID, location ID, or location data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Location not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update location
            tags:
                - locations
    /api/organizations/{organizationId}/permissions:
        get:
            description: Get the complete permissions matrix for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Permissions matrix retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/permissions.PermissionsResponse'
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get permissions matrix
            tags:
                - permissions
    /api/organizations/{organizationId}/permissions/{permissionId}/roles/{roleId}:
        patch:
            consumes:
                - application/json
            description: Update a permission value for a role
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Permission ID
                  in: path
                  name: permissionId
                  required: true
                  type: string
                - description: Role ID
                  in: path
                  name: roleId
                  required: true
                  type: string
                - description: Permission update data
                  in: body
                  name: permission
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Permission updated successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid IDs or request body
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Permission-role combination not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update permission
            tags:
                - permissions
    /api/organizations/{organizationId}/role-templates:
        get:
            description: Get available role templates for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Role templates retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/roles.CustomRole'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get role templates
            tags:
                - organizations
    /api/organizations/{organizationId}/roles:
        get:
            description: Get all roles for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Roles retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/roles.CustomRole'
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get organization roles
            tags:
                - organizations
        post:
            consumes:
                - application/json
            description: Create a new role for an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Role data
                  in: body
                  name: role
                  required: true
                  schema:
                    type: object
            produces:
                - application/json
            responses:
                "201":
                    description: Role created successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/roles.CustomRole'
                              type: object
                "400":
                    description: Bad request - invalid organization ID or role data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create organization role
            tags:
                - organizations
    /api/organizations/{organizationId}/roles/{roleId}:
        delete:
            description: Delete a role from an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: Role ID
                  in: path
                  name: roleId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Role deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid IDs
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Role not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete organization role
            tags:
                - organizations
    /api/organizations/{organizationId}/users:
        get:
            description: Get all users belonging to a specific organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Organization users retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        additionalProperties: true
                                        type: object
                                    type: array
                              type: object
                "400":
                    description: Bad request - invalid organization ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get organization users
            tags:
                - organizations
    /api/organizations/{organizationId}/users/{userId}:
        delete:
            description: Remove a user from an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: User removed from organization successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid IDs
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Remove user from organization
            tags:
                - organizations
        patch:
            consumes:
                - application/json
            description: Update the role of a user within an organization
            parameters:
                - description: Organization ID
                  in: path
                  name: organizationId
                  required: true
                  type: string
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: New role ID
                  in: body
                  name: roleId
                  required: true
                  schema:
                    properties:
                        roleId:
                            type: string
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: User role updated successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid IDs or request body
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: User not found in organization
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update user role in organization
            tags:
                - organizations
    /api/softwaregatewayconfig/basesettings:
        get:
            description: Get all base settings for gateway config templates
            produces:
                - application/json
            responses:
                "200":
                    description: Base settings retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/config.GatewayConfigTemplateBaseSetting'
                                    type: array
                              type: object
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: List base settings
            tags:
                - softwaregatewayconfig
        post:
            consumes:
                - application/json
            description: Create a new base setting for gateway config templates
            parameters:
                - description: Base setting data
                  in: body
                  name: setting
                  required: true
                  schema:
                    $ref: '#/definitions/config.GatewayConfigTemplateBaseSetting'
            produces:
                - application/json
            responses:
                "201":
                    description: Base setting created successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid base setting data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Create base setting
            tags:
                - softwaregatewayconfig
    /api/softwaregatewayconfig/basesettings/{setting}:
        delete:
            description: Delete a base setting for gateway config templates
            parameters:
                - description: Setting name
                  in: path
                  name: setting
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Base setting deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid setting name
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete base setting
            tags:
                - softwaregatewayconfig
        get:
            description: Get a specific base setting for gateway config templates
            parameters:
                - description: Setting name
                  in: path
                  name: setting
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Base setting retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/config.GatewayConfigTemplateBaseSetting'
                              type: object
                "400":
                    description: Bad request - invalid setting name
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Base setting not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get base setting
            tags:
                - softwaregatewayconfig
        patch:
            consumes:
                - application/json
            description: Update an existing base setting for gateway config templates
            parameters:
                - description: Setting name
                  in: path
                  name: setting
                  required: true
                  type: string
                - description: Updated base setting data
                  in: body
                  name: baseSetting
                  required: true
                  schema:
                    $ref: '#/definitions/config.GatewayConfigTemplateBaseSetting'
            produces:
                - application/json
            responses:
                "200":
                    description: Base setting updated successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid setting name or base setting data
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Base setting not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update base setting
            tags:
                - softwaregatewayconfig
    /api/user/{userId}/auth-methods:
        get:
            description: Get all authentication methods for a specific user
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Authentication methods retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        additionalProperties: true
                                        type: object
                                    type: array
                              type: object
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "403":
                    description: Forbidden - user not authorized
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get user authentication methods
            tags:
                - user
    /api/user/{userId}/auth-methods/{authMethodId}:
        delete:
            description: Delete a specific authentication method for a user
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Authentication method ID
                  in: path
                  name: authMethodId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Authentication method deleted successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "403":
                    description: Forbidden - user not authorized
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Authentication method not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Delete user authentication method
            tags:
                - user
    /api/user/{userId}/auth-methods/{authMethodId}/password:
        patch:
            consumes:
                - application/json
            description: Update password for a specific authentication method
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Authentication method ID
                  in: path
                  name: authMethodId
                  required: true
                  type: string
                - description: New password
                  in: body
                  name: password
                  required: true
                  schema:
                    additionalProperties:
                        type: string
                    type: object
            produces:
                - application/json
            responses:
                "200":
                    description: Password updated successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid password
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "403":
                    description: Forbidden - user not authorized
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "404":
                    description: Authentication method not found
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Update user password
            tags:
                - user
    /api/user/{userId}/email-verification:
        post:
            consumes:
                - application/json
            description: Send a verification email to the user's email address
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Email verification request data
                  in: body
                  name: request
                  required: true
                  schema:
                    $ref: '#/definitions/emailverification.RequestVerificationRequest'
            produces:
                - application/json
            responses:
                "200":
                    description: Verification email sent successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request - invalid parameters
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Request email verification
            tags:
                - emailverification
    /api/user/{userId}/email-verification/verify:
        post:
            description: Verify an email address using the verification token
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Email verification token
                  in: query
                  name: token
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Email verified successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/emailverification.VerificationResponse'
                              type: object
                "400":
                    description: Bad request - invalid parameters or token
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "401":
                    description: Unauthorized - invalid or expired token
                    schema:
                        $ref: '#/definitions/response.UnauthorizedResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Verify email address
            tags:
                - emailverification
    /api/user/{userId}/invites:
        get:
            description: Get all invites for a specific user
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: User invites retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    items:
                                        $ref: '#/definitions/invites.InviteResponse'
                                    type: array
                              type: object
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.UnauthorizedResponse'
                "403":
                    description: Forbidden - user not authorized
                    schema:
                        $ref: '#/definitions/response.ForbiddenResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: List user invites
            tags:
                - user
    /api/user/{userId}/invites/{inviteId}:
        delete:
            description: Reject a specific invite for a user
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Invite ID
                  in: path
                  name: inviteId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Invite rejected successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.UnauthorizedResponse'
                "403":
                    description: Forbidden - user not authorized
                    schema:
                        $ref: '#/definitions/response.ForbiddenResponse'
                "404":
                    description: Invite not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Reject user invite
            tags:
                - user
    /api/user/{userId}/invites/{inviteId}/redeem:
        post:
            description: Redeem a specific invite for a user
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Invite ID
                  in: path
                  name: inviteId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Invite redeemed successfully
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.UnauthorizedResponse'
                "403":
                    description: Forbidden - user not authorized
                    schema:
                        $ref: '#/definitions/response.ForbiddenResponse'
                "404":
                    description: Invite not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Redeem user invite
            tags:
                - user
    /api/user/{userId}/profile:
        get:
            description: Get profile information for a specific user
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: User profile retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/user.User'
                              type: object
                "400":
                    description: Bad request - invalid user ID
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.UnauthorizedResponse'
                "403":
                    description: Forbidden - user not authorized
                    schema:
                        $ref: '#/definitions/response.ForbiddenResponse'
                "404":
                    description: User not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            security:
                - SessionAuth: []
            summary: Get user profile
            tags:
                - user
        patch:
            consumes:
                - application/json
            description: Update profile information for a specific user
            parameters:
                - description: User ID
                  in: path
                  name: userId
                  required: true
                  type: string
                - description: Profile update data
                  in: body
                  name: profile
                  required: true
                  schema:
                    $ref: '#/definitions/profile.updateProfileRequest'
            produces:
                - application/json
            responses:
                "200":
                    description: Profile updated successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/user.User'
                              type: object
                "400":
                    description: Bad request - invalid user ID or request body
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "401":
                    description: Unauthorized - invalid or missing session
                    schema:
                        $ref: '#/definitions/response.UnauthorizedResponse'
                "403":
                    description: Forbidden - user not authorized
                    schema:
                        $ref: '#/definitions/response.ForbiddenResponse'
                "404":
                    description: User not found
                    schema:
                        $ref: '#/definitions/response.NotFoundResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            security:
                - SessionAuth: []
            summary: Update user profile
            tags:
                - user
    /callback:
        get:
            description: Handles OAuth2 callback from provider with authorization code
            parameters:
                - description: OAuth2 state parameter
                  in: query
                  name: state
                  required: true
                  type: string
                - description: OAuth2 authorization code
                  in: query
                  name: code
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "302":
                    description: Redirect to application root
                    schema:
                        type: string
                "400":
                    description: Bad request - invalid state or missing code
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: OAuth2 callback
            tags:
                - authentication
    /login:
        get:
            description: Redirects user to OAuth2 provider for authentication
            produces:
                - application/json
            responses:
                "302":
                    description: Redirect to OAuth2 provider
                    schema:
                        type: string
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: OAuth2 login redirect
            tags:
                - authentication
        post:
            consumes:
                - application/x-www-form-urlencoded
            description: Authenticate user with username and password
            parameters:
                - description: Username
                  in: formData
                  name: username
                  required: true
                  type: string
                - description: Password
                  in: formData
                  name: password
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Login successful
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/response.UnauthorizedResponse'
                "405":
                    description: Method not allowed
                    schema:
                        $ref: '#/definitions/response.MethodNotAllowedResponse'
            summary: User login
            tags:
                - authentication
    /logout:
        get:
            description: Logs out the current user and clears session
            produces:
                - application/json
            responses:
                "302":
                    description: Redirect to application root
                    schema:
                        type: string
            summary: User logout
            tags:
                - authentication
    /register:
        post:
            consumes:
                - application/x-www-form-urlencoded
            description: Register a new user account
            parameters:
                - description: First name
                  in: formData
                  name: firstname
                  required: true
                  type: string
                - description: Last name
                  in: formData
                  name: lastname
                  required: true
                  type: string
                - description: Username
                  in: formData
                  name: username
                  required: true
                  type: string
                - description: Password
                  in: formData
                  name: password
                  required: true
                  type: string
                - description: Email address
                  in: formData
                  name: email
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Registration successful
                    schema:
                        $ref: '#/definitions/response.SuccessResponse'
                "400":
                    description: Bad request
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "405":
                    description: Method not allowed
                    schema:
                        $ref: '#/definitions/response.MethodNotAllowedResponse'
                "500":
                    description: Internal server error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: User registration
            tags:
                - authentication
schemes:
    - http
    - https
swagger: "2.0"
