================================================================================
                          LICENSE VALIDATION REPORT
================================================================================
Config File: /scripts/license-config.yaml

ALLOWED LICENSES:
  - MIT
  - Apache-2.0
  - BSD-2-Clause
  - BSD-3-Clause
  - ISC
  - CC0-1.0
  - BlueOak-1.0.0

EXCLUSIONS (Packages to skip):
  - synapse-its.com/*
  - bitbucket.org/synapse-its/*
  - github.com/mailgun/mailgun-go/*
  - onramp-ui@*
  - e2e@*

================================================================================
                              PACKAGE ANALYSIS
================================================================================


GO MODULES:
----------

Module: broker
--------------------------------------------------
ACCEPTED (75 packages):
  - cloud.google.com/go (Apache-2.0)
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/KyleBanks/depth (MIT)
  - github.com/Masterminds/semver/v3 (MIT)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/davecgh/go-spew/spew (ISC)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/go-openapi/jsonpointer (Apache-2.0)
  - github.com/go-openapi/jsonreference (Apache-2.0)
  - github.com/go-openapi/spec (Apache-2.0)
  - github.com/go-openapi/swag (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/golang-jwt/jwt/v4 (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/gorilla/mux (BSD-3-Clause)
  - github.com/josharian/intern (MIT)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/mailru/easyjson (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/pmezard/go-difflib/difflib (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/stretchr/objx (MIT)
  - github.com/stretchr/testify (MIT)
  - github.com/swaggo/files/v2 (MIT)
  - github.com/swaggo/http-swagger/v2 (MIT)
  - github.com/swaggo/swag (MIT)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/mod (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/tools (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)
  - gopkg.in/yaml.v2 (Apache-2.0)
  - gopkg.in/yaml.v3 (MIT)

EXCLUDED - SKIPPED (61 packages):
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/audit_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_comms (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_config (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_stats (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_faults (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers (Unknown)
  - synapse-its.com/broker (Unknown)
  - synapse-its.com/broker/api/brokerShared (Unknown)
  - synapse-its.com/broker/api/handlers/v3/data/device (Unknown)
  - synapse-its.com/broker/api/handlers/v3/data/fault (Unknown)
  - synapse-its.com/broker/api/handlers/v3/gateway/authenticate (Unknown)
  - synapse-its.com/broker/api/handlers/v3/gateway/ingest (Unknown)
  - synapse-its.com/broker/api/handlers/v3/gateway/update (Unknown)
  - synapse-its.com/broker/api/handlers/v3/shared (Unknown)
  - synapse-its.com/broker/api/handlers/v3/user/account/close (Unknown)
  - synapse-its.com/broker/api/handlers/v3/user/account/notifications (Unknown)
  - synapse-its.com/broker/api/handlers/v3/user/authenticate (Unknown)
  - synapse-its.com/broker/api/handlers/v3/user/instruction (Unknown)
  - synapse-its.com/broker/api/handlers/v3/user/merge-user (Unknown)
  - synapse-its.com/broker/api/handlers/v3/user/profile (Unknown)
  - synapse-its.com/broker/api/handlers/v4/data/device (Unknown)
  - synapse-its.com/broker/api/routes (Unknown)
  - synapse-its.com/broker/api/synapse (Unknown)
  - synapse-its.com/broker/docs (Unknown)
  - synapse-its.com/shared/api (Unknown)
  - synapse-its.com/shared/api/authorizer (Unknown)
  - synapse-its.com/shared/api/handlers/defaultapi (Unknown)
  - synapse-its.com/shared/api/helper (Unknown)
  - synapse-its.com/shared/api/jwttokens (Unknown)
  - synapse-its.com/shared/api/middleware (Unknown)
  - synapse-its.com/shared/api/response (Unknown)
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/api/softwaregateway (Unknown)
  - synapse-its.com/shared/bqbatch (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/devices (Unknown)
  - synapse-its.com/shared/devices/edi/edicmu2212 (Unknown)
  - synapse-its.com/shared/devices/edi/ediecl2010 (Unknown)
  - synapse-its.com/shared/devices/edi/ediecl2018 (Unknown)
  - synapse-its.com/shared/devices/edi/edikcl2018 (Unknown)
  - synapse-its.com/shared/devices/edi/edimmu16le (Unknown)
  - synapse-its.com/shared/devices/edi/helper (Unknown)
  - synapse-its.com/shared/devices/helper (Unknown)
  - synapse-its.com/shared/healthz (Unknown)
  - synapse-its.com/shared/httplogger (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/mocks/bqexecutor (Unknown)
  - synapse-its.com/shared/mocks/dbexecutor (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/rest/onramp/helper (Unknown)
  - synapse-its.com/shared/rest/onramp/softwaregateway/config (Unknown)
  - synapse-its.com/shared/rest/onramp/user/merge-user (Unknown)
  - synapse-its.com/shared/schemas (Unknown)


Module: coordinator
--------------------------------------------------
ACCEPTED (54 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/Masterminds/semver (MIT)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)

EXCLUDED - SKIPPED (9 packages):
  - synapse-its.com/coordinator (Unknown)
  - synapse-its.com/coordinator/internal/setup (Unknown)
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/healthz (Unknown)
  - synapse-its.com/shared/httplogger (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/schema_mgmt (Unknown)


Module: etl
--------------------------------------------------
ACCEPTED (70 packages):
  - cloud.google.com/go (Apache-2.0)
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/davecgh/go-spew/spew (ISC)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/golang-jwt/jwt/v4 (MIT)
  - github.com/golang-jwt/jwt/v5 (MIT)
  - github.com/golang/mock/gomock (Apache-2.0)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/json-iterator/go (MIT)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/mailgun/errors (Apache-2.0)
  - github.com/modern-go/concurrent (Apache-2.0)
  - github.com/modern-go/reflect2 (Apache-2.0)
  - github.com/nyaruka/phonenumbers (MIT)
  - github.com/oapi-codegen/runtime/types (Apache-2.0)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/pkg/errors (BSD-2-Clause)
  - github.com/pmezard/go-difflib/difflib (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/stretchr/objx (MIT)
  - github.com/stretchr/testify (MIT)
  - github.com/twilio/twilio-go (MIT)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)
  - gopkg.in/yaml.v3 (MIT)

EXCLUDED - SKIPPED (58 packages):
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/audit_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_comms (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_config (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_stats (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_faults (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers (Unknown)
  - github.com/mailgun/mailgun-go/v5 (Unknown)
  - github.com/mailgun/mailgun-go/v5/events (Unknown)
  - github.com/mailgun/mailgun-go/v5/internal/types/inboxready (Unknown)
  - github.com/mailgun/mailgun-go/v5/mtypes (Unknown)
  - synapse-its.com/etl (Unknown)
  - synapse-its.com/etl/processors/handlers/dlq/batch (Unknown)
  - synapse-its.com/etl/processors/handlers/dlq/messages (Unknown)
  - synapse-its.com/etl/processors/handlers/etlShared (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/faultLogs (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/faultNotification (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/gatewayLog (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/helper (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/macAddress (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/monitorName (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/perfStats (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/rmsData (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/rmsEngine (Unknown)
  - synapse-its.com/etl/processors/handlers/gateway/wrapperResponse (Unknown)
  - synapse-its.com/etl/processors/handlers/notifications (Unknown)
  - synapse-its.com/etl/processors/handlers/notifications/mailgun (Unknown)
  - synapse-its.com/etl/processors/handlers/notifications/twilio (Unknown)
  - synapse-its.com/etl/processors/handlers/raw (Unknown)
  - synapse-its.com/etl/processors/handlers/synapse/purgeExpired (Unknown)
  - synapse-its.com/etl/processors/subscriptions (Unknown)
  - synapse-its.com/shared/api (Unknown)
  - synapse-its.com/shared/api/authorizer (Unknown)
  - synapse-its.com/shared/api/helper (Unknown)
  - synapse-its.com/shared/api/jwttokens (Unknown)
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/bqbatch (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/devices (Unknown)
  - synapse-its.com/shared/devices/edi/edicmu2212 (Unknown)
  - synapse-its.com/shared/devices/edi/ediecl2010 (Unknown)
  - synapse-its.com/shared/devices/edi/edimmu16le (Unknown)
  - synapse-its.com/shared/devices/edi/helper (Unknown)
  - synapse-its.com/shared/devices/helper (Unknown)
  - synapse-its.com/shared/healthz (Unknown)
  - synapse-its.com/shared/httplogger (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/mocks/bqexecutor (Unknown)
  - synapse-its.com/shared/mocks/dbexecutor (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/schemas (Unknown)


Module: onramp
--------------------------------------------------
ACCEPTED (88 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/KyleBanks/depth (MIT)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/coreos/go-oidc (Apache-2.0)
  - github.com/davecgh/go-spew/spew (ISC)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/go-openapi/jsonpointer (Apache-2.0)
  - github.com/go-openapi/jsonreference (Apache-2.0)
  - github.com/go-openapi/spec (Apache-2.0)
  - github.com/go-openapi/swag (Apache-2.0)
  - github.com/go-openapi/swag/cmdutils (Apache-2.0)
  - github.com/go-openapi/swag/conv (Apache-2.0)
  - github.com/go-openapi/swag/fileutils (Apache-2.0)
  - github.com/go-openapi/swag/jsonname (Apache-2.0)
  - github.com/go-openapi/swag/jsonutils (Apache-2.0)
  - github.com/go-openapi/swag/loading (Apache-2.0)
  - github.com/go-openapi/swag/mangling (Apache-2.0)
  - github.com/go-openapi/swag/netutils (Apache-2.0)
  - github.com/go-openapi/swag/stringutils (Apache-2.0)
  - github.com/go-openapi/swag/typeutils (Apache-2.0)
  - github.com/go-openapi/swag/yamlutils (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/golang-jwt/jwt/v4 (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/gorilla/mux (BSD-3-Clause)
  - github.com/josharian/intern (MIT)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/mailru/easyjson (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/pmezard/go-difflib/difflib (BSD-3-Clause)
  - github.com/pquerna/cachecontrol (Apache-2.0)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/stretchr/objx (MIT)
  - github.com/stretchr/testify (MIT)
  - github.com/swaggo/files (MIT)
  - github.com/swaggo/http-swagger (MIT)
  - github.com/swaggo/swag (MIT)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.starlark.net (BSD-3-Clause)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/mod (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/tools (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)
  - gopkg.in/go-jose/go-jose.v2 (Apache-2.0)
  - gopkg.in/go-jose/go-jose.v2/json (BSD-3-Clause)
  - gopkg.in/yaml.v3 (MIT)

EXCLUDED - SKIPPED (58 packages):
  - synapse-its.com/onramp (Unknown)
  - synapse-its.com/onramp/app (Unknown)
  - synapse-its.com/onramp/data (Unknown)
  - synapse-its.com/onramp/docs (Unknown)
  - synapse-its.com/onramp/domain (Unknown)
  - synapse-its.com/onramp/handlers (Unknown)
  - synapse-its.com/onramp/middlewares (Unknown)
  - synapse-its.com/onramp/mock (Unknown)
  - synapse-its.com/onramp/modules/auth (Unknown)
  - synapse-its.com/onramp/modules/auth/session (Unknown)
  - synapse-its.com/onramp/modules/device (Unknown)
  - synapse-its.com/onramp/modules/organization (Unknown)
  - synapse-its.com/onramp/modules/softwaregateway (Unknown)
  - synapse-its.com/onramp/modules/user (Unknown)
  - synapse-its.com/onramp/modules/user/permissions (Unknown)
  - synapse-its.com/onramp/pkg (Unknown)
  - synapse-its.com/shared/api/authorizer (Unknown)
  - synapse-its.com/shared/api/authtypes (Unknown)
  - synapse-its.com/shared/api/helper (Unknown)
  - synapse-its.com/shared/api/jwttokens (Unknown)
  - synapse-its.com/shared/api/middleware (Unknown)
  - synapse-its.com/shared/api/password (Unknown)
  - synapse-its.com/shared/api/response (Unknown)
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/bqbatch (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/healthz (Unknown)
  - synapse-its.com/shared/httplogger (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/mocks/bqexecutor (Unknown)
  - synapse-its.com/shared/mocks/dbexecutor (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/rest/biz (Unknown)
  - synapse-its.com/shared/rest/domain/aps_reset (Unknown)
  - synapse-its.com/shared/rest/domain/organization (Unknown)
  - synapse-its.com/shared/rest/domain/user (Unknown)
  - synapse-its.com/shared/rest/external/bigquery (Unknown)
  - synapse-its.com/shared/rest/external/postgres (Unknown)
  - synapse-its.com/shared/rest/external/starlark (Unknown)
  - synapse-its.com/shared/rest/onramp/devicegroups (Unknown)
  - synapse-its.com/shared/rest/onramp/devicegroupusers (Unknown)
  - synapse-its.com/shared/rest/onramp/helper (Unknown)
  - synapse-its.com/shared/rest/onramp/invites (Unknown)
  - synapse-its.com/shared/rest/onramp/locationgrouproleassignments (Unknown)
  - synapse-its.com/shared/rest/onramp/locations (Unknown)
  - synapse-its.com/shared/rest/onramp/organization (Unknown)
  - synapse-its.com/shared/rest/onramp/organization/device (Unknown)
  - synapse-its.com/shared/rest/onramp/organization/locationgroups (Unknown)
  - synapse-its.com/shared/rest/onramp/permissions (Unknown)
  - synapse-its.com/shared/rest/onramp/roles (Unknown)
  - synapse-its.com/shared/rest/onramp/softwaregateway (Unknown)
  - synapse-its.com/shared/rest/onramp/softwaregateway/config (Unknown)
  - synapse-its.com/shared/rest/onramp/user/auth-methods (Unknown)
  - synapse-its.com/shared/rest/onramp/user/emailverification (Unknown)
  - synapse-its.com/shared/rest/onramp/user/merge-user (Unknown)
  - synapse-its.com/shared/rest/onramp/user/profile (Unknown)
  - synapse-its.com/shared/schemas (Unknown)
  - synapse-its.com/shared/util (Unknown)


Module: rushhour
--------------------------------------------------
ACCEPTED (77 packages):
  - cloud.google.com/go (Apache-2.0)
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/andybalholm/brotli (MIT)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/davecgh/go-spew/spew (ISC)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/golang-jwt/jwt/v4 (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/gookit/color (MIT)
  - github.com/gorilla/mux (BSD-3-Clause)
  - github.com/gorilla/websocket (BSD-2-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/pmezard/go-difflib/difflib (BSD-3-Clause)
  - github.com/quic-go/qpack (MIT)
  - github.com/quic-go/quic-go (MIT)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/stretchr/objx (MIT)
  - github.com/stretchr/testify (MIT)
  - github.com/vmihailenco/msgpack/v5 (BSD-2-Clause)
  - github.com/vmihailenco/tagparser/v2 (BSD-2-Clause)
  - github.com/xo/terminfo (MIT)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - github.com/zishang520/engine.io-go-parser (MIT)
  - github.com/zishang520/engine.io/v2 (MIT)
  - github.com/zishang520/engine.io/v2/events (MIT)
  - github.com/zishang520/engine.io/v2/webtransport (BSD-3-Clause)
  - github.com/zishang520/socket.io-go-parser/v2/parser (MIT)
  - github.com/zishang520/socket.io-go-redis (MIT)
  - github.com/zishang520/socket.io/v2 (MIT)
  - github.com/zishang520/webtransport-go (MIT)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)
  - gopkg.in/yaml.v3 (MIT)

EXCLUDED - SKIPPED (36 packages):
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_comms (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_config (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_stats (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_faults (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/rushhour/v1 (Unknown)
  - synapse-its.com/rushhour (Unknown)
  - synapse-its.com/rushhour/app (Unknown)
  - synapse-its.com/rushhour/auth (Unknown)
  - synapse-its.com/rushhour/domain (Unknown)
  - synapse-its.com/rushhour/modules/socketio (Unknown)
  - synapse-its.com/rushhour/permissions (Unknown)
  - synapse-its.com/rushhour/tracking (Unknown)
  - synapse-its.com/shared/api/authorizer (Unknown)
  - synapse-its.com/shared/api/helper (Unknown)
  - synapse-its.com/shared/api/jwttokens (Unknown)
  - synapse-its.com/shared/api/middleware (Unknown)
  - synapse-its.com/shared/api/response (Unknown)
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/bqbatch (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/healthz (Unknown)
  - synapse-its.com/shared/httplogger (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/mocks/bqexecutor (Unknown)
  - synapse-its.com/shared/mocks/dbexecutor (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/schemas (Unknown)


Module: shared-api
--------------------------------------------------
ACCEPTED (59 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/davecgh/go-spew/spew (ISC)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/golang-jwt/jwt/v4 (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/pmezard/go-difflib/difflib (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/stretchr/objx (MIT)
  - github.com/stretchr/testify (MIT)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)
  - gopkg.in/yaml.v3 (MIT)

EXCLUDED - SKIPPED (18 packages):
  - synapse-its.com/shared/api (Unknown)
  - synapse-its.com/shared/api/authorizer (Unknown)
  - synapse-its.com/shared/api/authtypes (Unknown)
  - synapse-its.com/shared/api/handlers/defaultapi (Unknown)
  - synapse-its.com/shared/api/helper (Unknown)
  - synapse-its.com/shared/api/jwttokens (Unknown)
  - synapse-its.com/shared/api/middleware (Unknown)
  - synapse-its.com/shared/api/password (Unknown)
  - synapse-its.com/shared/api/response (Unknown)
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/api/softwaregateway (Unknown)
  - synapse-its.com/shared/bqbatch (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/mocks/bqexecutor (Unknown)
  - synapse-its.com/shared/mocks/dbexecutor (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/schemas (Unknown)


Module: shared-bqbatch
--------------------------------------------------
ACCEPTED (58 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/davecgh/go-spew/spew (ISC)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/pmezard/go-difflib/difflib (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/stretchr/objx (MIT)
  - github.com/stretchr/testify (MIT)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)
  - gopkg.in/yaml.v3 (MIT)

EXCLUDED - SKIPPED (8 packages):
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/bqbatch (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/mocks/bqexecutor (Unknown)
  - synapse-its.com/shared/mocks/dbexecutor (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/schemas (Unknown)


Module: shared-connect
--------------------------------------------------
ACCEPTED (53 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)

EXCLUDED - SKIPPED (3 packages):
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/logger (Unknown)


Module: shared-devices
--------------------------------------------------
ACCEPTED (48 packages):
  - cloud.google.com/go (Apache-2.0)
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type/expr (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)

EXCLUDED - SKIPPED (23 packages):
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_comms (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_config (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_stats (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_faults (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers (Unknown)
  - synapse-its.com/shared/devices (Unknown)
  - synapse-its.com/shared/devices/edi/edicmu2212 (Unknown)
  - synapse-its.com/shared/devices/edi/ediecl2010 (Unknown)
  - synapse-its.com/shared/devices/edi/ediecl2018 (Unknown)
  - synapse-its.com/shared/devices/edi/edimmu16le (Unknown)
  - synapse-its.com/shared/devices/edi/helper (Unknown)
  - synapse-its.com/shared/devices/helper (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/schemas (Unknown)


Module: shared-healthz
--------------------------------------------------
ACCEPTED (1 packages):
  - go.uber.org/zap (MIT)

EXCLUDED - SKIPPED (3 packages):
  - synapse-its.com/shared/healthz (Unknown)
  - synapse-its.com/shared/httplogger (Unknown)
  - synapse-its.com/shared/logger (Unknown)


Module: shared-httplogger
--------------------------------------------------
ACCEPTED (1 packages):
  - go.uber.org/zap (MIT)

EXCLUDED - SKIPPED (2 packages):
  - synapse-its.com/shared/httplogger (Unknown)
  - synapse-its.com/shared/logger (Unknown)


Module: shared-logger
--------------------------------------------------
ACCEPTED (1 packages):
  - go.uber.org/zap (MIT)

EXCLUDED - SKIPPED (1 packages):
  - synapse-its.com/shared/logger (Unknown)


Module: shared-mocks
--------------------------------------------------
ACCEPTED (63 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/alicebob/miniredis/v2 (MIT)
  - github.com/alicebob/miniredis/v2/geohash (MIT)
  - github.com/alicebob/miniredis/v2/hyperloglog (MIT)
  - github.com/alicebob/miniredis/v2/metro (MIT)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/davecgh/go-spew/spew (ISC)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/pmezard/go-difflib/difflib (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/stretchr/objx (MIT)
  - github.com/stretchr/testify (MIT)
  - github.com/yuin/gopher-lua (MIT)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)
  - gopkg.in/yaml.v3 (MIT)

EXCLUDED - SKIPPED (16 packages):
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/bqbatch (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/mocks (Unknown)
  - synapse-its.com/shared/mocks/bqbatcher (Unknown)
  - synapse-its.com/shared/mocks/bqexecutor (Unknown)
  - synapse-its.com/shared/mocks/dbexecutor (Unknown)
  - synapse-its.com/shared/mocks/firestore (Unknown)
  - synapse-its.com/shared/mocks/healthz (Unknown)
  - synapse-its.com/shared/mocks/notifications (Unknown)
  - synapse-its.com/shared/mocks/pubsub (Unknown)
  - synapse-its.com/shared/mocks/schemaexecutor (Unknown)
  - synapse-its.com/shared/mocks/unexported (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/schemas (Unknown)

NOT ALLOWED (2 packages):
  - github.com/alicebob/miniredis/v2/fpconv (BSL-1.0)
  - github.com/alicebob/miniredis/v2/gopher-json (Unlicense)


Module: shared-protobuf-schemas
--------------------------------------------------
ACCEPTED (1 packages):
  - google.golang.org/protobuf (BSD-3-Clause)

EXCLUDED - SKIPPED (16 packages):
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/audit_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_comms (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_config (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_stats (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/dfu (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/discovery (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_faults (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/realtime (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers (Unknown)
  - bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/rushhour/v1 (Unknown)


Module: shared-pubsubdata
--------------------------------------------------
ACCEPTED (33 packages):
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/internal (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type/expr (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)

EXCLUDED - SKIPPED (1 packages):
  - synapse-its.com/shared/pubsubdata (Unknown)


Module: shared-rest
--------------------------------------------------
ACCEPTED (61 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/davecgh/go-spew/spew (ISC)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/golang-jwt/jwt/v4 (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/gorilla/mux (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/pmezard/go-difflib/difflib (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/stretchr/objx (MIT)
  - github.com/stretchr/testify (MIT)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.starlark.net (BSD-3-Clause)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)
  - gopkg.in/yaml.v3 (MIT)

EXCLUDED - SKIPPED (41 packages):
  - synapse-its.com/onramp/domain (Unknown)
  - synapse-its.com/onramp/middlewares (Unknown)
  - synapse-its.com/shared/api/authorizer (Unknown)
  - synapse-its.com/shared/api/authtypes (Unknown)
  - synapse-its.com/shared/api/helper (Unknown)
  - synapse-its.com/shared/api/jwttokens (Unknown)
  - synapse-its.com/shared/api/password (Unknown)
  - synapse-its.com/shared/api/response (Unknown)
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/bqbatch (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/mocks/bqexecutor (Unknown)
  - synapse-its.com/shared/mocks/dbexecutor (Unknown)
  - synapse-its.com/shared/pubsubdata (Unknown)
  - synapse-its.com/shared/rest/biz (Unknown)
  - synapse-its.com/shared/rest/domain/aps_reset (Unknown)
  - synapse-its.com/shared/rest/domain/mocks (Unknown)
  - synapse-its.com/shared/rest/domain/organization (Unknown)
  - synapse-its.com/shared/rest/domain/user (Unknown)
  - synapse-its.com/shared/rest/external/bigquery (Unknown)
  - synapse-its.com/shared/rest/external/postgres (Unknown)
  - synapse-its.com/shared/rest/external/starlark (Unknown)
  - synapse-its.com/shared/rest/onramp/devicegroups (Unknown)
  - synapse-its.com/shared/rest/onramp/devicegroupusers (Unknown)
  - synapse-its.com/shared/rest/onramp/helper (Unknown)
  - synapse-its.com/shared/rest/onramp/invites (Unknown)
  - synapse-its.com/shared/rest/onramp/locationgrouproleassignments (Unknown)
  - synapse-its.com/shared/rest/onramp/locations (Unknown)
  - synapse-its.com/shared/rest/onramp/organization (Unknown)
  - synapse-its.com/shared/rest/onramp/organization/device (Unknown)
  - synapse-its.com/shared/rest/onramp/organization/locationgroups (Unknown)
  - synapse-its.com/shared/rest/onramp/permissions (Unknown)
  - synapse-its.com/shared/rest/onramp/roles (Unknown)
  - synapse-its.com/shared/rest/onramp/softwaregateway (Unknown)
  - synapse-its.com/shared/rest/onramp/softwaregateway/config (Unknown)
  - synapse-its.com/shared/rest/onramp/user/auth-methods (Unknown)
  - synapse-its.com/shared/rest/onramp/user/emailverification (Unknown)
  - synapse-its.com/shared/rest/onramp/user/merge-user (Unknown)
  - synapse-its.com/shared/rest/onramp/user/profile (Unknown)
  - synapse-its.com/shared/schemas (Unknown)


Module: shared-schema_mgmt
--------------------------------------------------
ACCEPTED (54 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/Masterminds/semver (MIT)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)

EXCLUDED - SKIPPED (4 packages):
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/schema_mgmt (Unknown)


Module: shared-schemas
--------------------------------------------------
ACCEPTED (43 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync/semaphore (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type/expr (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)

EXCLUDED - SKIPPED (1 packages):
  - synapse-its.com/shared/schemas (Unknown)


Module: shared-util
--------------------------------------------------
EXCLUDED - SKIPPED (1 packages):
  - synapse-its.com/shared/util/test (Unknown)


Module: testing
--------------------------------------------------
ACCEPTED (54 packages):
  - cloud.google.com/go/auth (Apache-2.0)
  - cloud.google.com/go/auth/oauth2adapt (Apache-2.0)
  - cloud.google.com/go/bigquery (Apache-2.0)
  - cloud.google.com/go/compute/metadata (Apache-2.0)
  - cloud.google.com/go/firestore (Apache-2.0)
  - cloud.google.com/go/iam (Apache-2.0)
  - cloud.google.com/go/longrunning/autogen/longrunningpb (Apache-2.0)
  - cloud.google.com/go/pubsub (Apache-2.0)
  - github.com/Masterminds/semver (MIT)
  - github.com/apache/arrow/go/v15 (Apache-2.0)
  - github.com/cespare/xxhash/v2 (MIT)
  - github.com/dgryski/go-rendezvous (MIT)
  - github.com/felixge/httpsnoop (MIT)
  - github.com/go-logr/logr (Apache-2.0)
  - github.com/go-logr/stdr (Apache-2.0)
  - github.com/goccy/go-json (MIT)
  - github.com/google/flatbuffers/go (Apache-2.0)
  - github.com/google/s2a-go (Apache-2.0)
  - github.com/google/uuid (BSD-3-Clause)
  - github.com/googleapis/enterprise-certificate-proxy/client (Apache-2.0)
  - github.com/googleapis/gax-go/v2 (BSD-3-Clause)
  - github.com/klauspost/compress (Apache-2.0)
  - github.com/klauspost/compress/internal/snapref (BSD-3-Clause)
  - github.com/klauspost/compress/zstd/internal/xxhash (MIT)
  - github.com/klauspost/cpuid/v2 (MIT)
  - github.com/lib/pq (MIT)
  - github.com/pierrec/lz4/v4 (BSD-3-Clause)
  - github.com/redis/go-redis/v9 (BSD-2-Clause)
  - github.com/zeebo/xxh3 (BSD-2-Clause)
  - go.opencensus.io (Apache-2.0)
  - go.opentelemetry.io/auto/sdk (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc (Apache-2.0)
  - go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp (Apache-2.0)
  - go.opentelemetry.io/otel (Apache-2.0)
  - go.opentelemetry.io/otel/metric (Apache-2.0)
  - go.opentelemetry.io/otel/trace (Apache-2.0)
  - go.uber.org/multierr (MIT)
  - go.uber.org/zap (MIT)
  - golang.org/x/crypto (BSD-3-Clause)
  - golang.org/x/exp/constraints (BSD-3-Clause)
  - golang.org/x/net (BSD-3-Clause)
  - golang.org/x/oauth2 (BSD-3-Clause)
  - golang.org/x/sync (BSD-3-Clause)
  - golang.org/x/sys (BSD-3-Clause)
  - golang.org/x/text (BSD-3-Clause)
  - golang.org/x/time/rate (BSD-3-Clause)
  - golang.org/x/xerrors (BSD-3-Clause)
  - google.golang.org/api (BSD-3-Clause)
  - google.golang.org/api/internal/third_party/uritemplates (BSD-3-Clause)
  - google.golang.org/genproto/googleapis/api (Apache-2.0)
  - google.golang.org/genproto/googleapis/rpc (Apache-2.0)
  - google.golang.org/genproto/googleapis/type (Apache-2.0)
  - google.golang.org/grpc (Apache-2.0)
  - google.golang.org/protobuf (BSD-3-Clause)

EXCLUDED - SKIPPED (5 packages):
  - synapse-its.com/shared/api/security (Unknown)
  - synapse-its.com/shared/connect (Unknown)
  - synapse-its.com/shared/logger (Unknown)
  - synapse-its.com/shared/schema_mgmt (Unknown)
  - synapse-its.com/testing/utils (Unknown)


JAVASCRIPT/NODE.JS PACKAGES:
----------------------------
ACCEPTED (924 packages):
  - @ampproject/remapping@2.3.0 (Apache-2.0)
  - @angular-devkit/architect@0.1902.13 (MIT)
  - @angular-devkit/build-angular@19.2.13 (MIT)
  - @angular-devkit/build-webpack@0.1902.13 (MIT)
  - @angular-devkit/core@19.2.13 (MIT)
  - @angular-devkit/schematics@19.2.13 (MIT)
  - @angular/animations@19.2.13 (MIT)
  - @angular/build@19.2.13 (MIT)
  - @angular/cdk@19.2.17 (MIT)
  - @angular/cli@19.2.13 (MIT)
  - @angular/common@19.2.13 (MIT)
  - @angular/compiler-cli@19.2.13 (MIT)
  - @angular/compiler@19.2.13 (MIT)
  - @angular/core@19.2.13 (MIT)
  - @angular/forms@19.2.13 (MIT)
  - @angular/platform-browser-dynamic@19.2.13 (MIT)
  - @angular/platform-browser@19.2.13 (MIT)
  - @angular/router@19.2.13 (MIT)
  - @ant-design/colors@7.2.1 (MIT)
  - @ant-design/fast-color@2.0.6 (MIT)
  - @ant-design/icons-angular@19.0.0 (MIT)
  - @babel/code-frame@7.27.1 (MIT)
  - @babel/compat-data@7.27.2 (MIT)
  - @babel/core@7.26.10 (MIT)
  - @babel/core@7.26.9 (MIT)
  - @babel/generator@7.26.10 (MIT)
  - @babel/generator@7.27.1 (MIT)
  - @babel/helper-annotate-as-pure@7.25.9 (MIT)
  - @babel/helper-annotate-as-pure@7.27.1 (MIT)
  - @babel/helper-compilation-targets@7.27.2 (MIT)
  - @babel/helper-create-class-features-plugin@7.27.1 (MIT)
  - @babel/helper-create-regexp-features-plugin@7.27.1 (MIT)
  - @babel/helper-define-polyfill-provider@0.6.4 (MIT)
  - @babel/helper-member-expression-to-functions@7.27.1 (MIT)
  - @babel/helper-module-imports@7.27.1 (MIT)
  - @babel/helper-module-transforms@7.27.1 (MIT)
  - @babel/helper-optimise-call-expression@7.27.1 (MIT)
  - @babel/helper-plugin-utils@7.27.1 (MIT)
  - @babel/helper-remap-async-to-generator@7.27.1 (MIT)
  - @babel/helper-replace-supers@7.27.1 (MIT)
  - @babel/helper-skip-transparent-expression-wrappers@7.27.1 (MIT)
  - @babel/helper-split-export-declaration@7.24.7 (MIT)
  - @babel/helper-string-parser@7.27.1 (MIT)
  - @babel/helper-validator-identifier@7.27.1 (MIT)
  - @babel/helper-validator-option@7.27.1 (MIT)
  - @babel/helper-wrap-function@7.27.1 (MIT)
  - @babel/helpers@7.27.1 (MIT)
  - @babel/parser@7.27.2 (MIT)
  - @babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1 (MIT)
  - @babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1 (MIT)
  - @babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1 (MIT)
  - @babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1 (MIT)
  - @babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1 (MIT)
  - @babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2 (MIT)
  - @babel/plugin-syntax-import-assertions@7.27.1 (MIT)
  - @babel/plugin-syntax-import-attributes@7.26.0 (MIT)
  - @babel/plugin-syntax-unicode-sets-regex@7.18.6 (MIT)
  - @babel/plugin-transform-arrow-functions@7.27.1 (MIT)
  - @babel/plugin-transform-async-generator-functions@7.26.8 (MIT)
  - @babel/plugin-transform-async-to-generator@7.25.9 (MIT)
  - @babel/plugin-transform-block-scoped-functions@7.27.1 (MIT)
  - @babel/plugin-transform-block-scoping@7.27.1 (MIT)
  - @babel/plugin-transform-class-properties@7.27.1 (MIT)
  - @babel/plugin-transform-class-static-block@7.27.1 (MIT)
  - @babel/plugin-transform-classes@7.27.1 (MIT)
  - @babel/plugin-transform-computed-properties@7.27.1 (MIT)
  - @babel/plugin-transform-destructuring@7.27.1 (MIT)
  - @babel/plugin-transform-dotall-regex@7.27.1 (MIT)
  - @babel/plugin-transform-duplicate-keys@7.27.1 (MIT)
  - @babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1 (MIT)
  - @babel/plugin-transform-dynamic-import@7.27.1 (MIT)
  - @babel/plugin-transform-exponentiation-operator@7.27.1 (MIT)
  - @babel/plugin-transform-export-namespace-from@7.27.1 (MIT)
  - @babel/plugin-transform-for-of@7.27.1 (MIT)
  - @babel/plugin-transform-function-name@7.27.1 (MIT)
  - @babel/plugin-transform-json-strings@7.27.1 (MIT)
  - @babel/plugin-transform-literals@7.27.1 (MIT)
  - @babel/plugin-transform-logical-assignment-operators@7.27.1 (MIT)
  - @babel/plugin-transform-member-expression-literals@7.27.1 (MIT)
  - @babel/plugin-transform-modules-amd@7.27.1 (MIT)
  - @babel/plugin-transform-modules-commonjs@7.27.1 (MIT)
  - @babel/plugin-transform-modules-systemjs@7.27.1 (MIT)
  - @babel/plugin-transform-modules-umd@7.27.1 (MIT)
  - @babel/plugin-transform-named-capturing-groups-regex@7.27.1 (MIT)
  - @babel/plugin-transform-new-target@7.27.1 (MIT)
  - @babel/plugin-transform-nullish-coalescing-operator@7.27.1 (MIT)
  - @babel/plugin-transform-numeric-separator@7.27.1 (MIT)
  - @babel/plugin-transform-object-rest-spread@7.27.2 (MIT)
  - @babel/plugin-transform-object-super@7.27.1 (MIT)
  - @babel/plugin-transform-optional-catch-binding@7.27.1 (MIT)
  - @babel/plugin-transform-optional-chaining@7.27.1 (MIT)
  - @babel/plugin-transform-parameters@7.27.1 (MIT)
  - @babel/plugin-transform-private-methods@7.27.1 (MIT)
  - @babel/plugin-transform-private-property-in-object@7.27.1 (MIT)
  - @babel/plugin-transform-property-literals@7.27.1 (MIT)
  - @babel/plugin-transform-regenerator@7.27.1 (MIT)
  - @babel/plugin-transform-regexp-modifiers@7.27.1 (MIT)
  - @babel/plugin-transform-reserved-words@7.27.1 (MIT)
  - @babel/plugin-transform-runtime@7.26.10 (MIT)
  - @babel/plugin-transform-shorthand-properties@7.27.1 (MIT)
  - @babel/plugin-transform-spread@7.27.1 (MIT)
  - @babel/plugin-transform-sticky-regex@7.27.1 (MIT)
  - @babel/plugin-transform-template-literals@7.27.1 (MIT)
  - @babel/plugin-transform-typeof-symbol@7.27.1 (MIT)
  - @babel/plugin-transform-unicode-escapes@7.27.1 (MIT)
  - @babel/plugin-transform-unicode-property-regex@7.27.1 (MIT)
  - @babel/plugin-transform-unicode-regex@7.27.1 (MIT)
  - @babel/plugin-transform-unicode-sets-regex@7.27.1 (MIT)
  - @babel/preset-env@7.26.9 (MIT)
  - @babel/preset-modules@0.1.6-no-external-plugins (MIT)
  - @babel/runtime@7.26.10 (MIT)
  - @babel/template@7.27.2 (MIT)
  - @babel/traverse@7.27.1 (MIT)
  - @babel/types@7.27.1 (MIT)
  - @colors/colors@1.5.0 (MIT)
  - @ctrl/tinycolor@3.6.1 (MIT)
  - @discoveryjs/json-ext@0.6.3 (MIT)
  - @esbuild/linux-x64@0.25.4 (MIT)
  - @inquirer/checkbox@4.1.8 (MIT)
  - @inquirer/confirm@5.1.6 (MIT)
  - @inquirer/core@10.1.13 (MIT)
  - @inquirer/editor@4.2.13 (MIT)
  - @inquirer/expand@4.0.15 (MIT)
  - @inquirer/figures@1.0.12 (MIT)
  - @inquirer/input@4.1.12 (MIT)
  - @inquirer/number@3.0.15 (MIT)
  - @inquirer/password@4.0.15 (MIT)
  - @inquirer/prompts@7.3.2 (MIT)
  - @inquirer/rawlist@4.1.3 (MIT)
  - @inquirer/search@3.0.15 (MIT)
  - @inquirer/select@4.2.3 (MIT)
  - @inquirer/type@1.5.5 (MIT)
  - @inquirer/type@3.0.7 (MIT)
  - @isaacs/cliui@8.0.2 (ISC)
  - @isaacs/fs-minipass@4.0.1 (ISC)
  - @istanbuljs/schema@0.1.3 (MIT)
  - @jridgewell/gen-mapping@0.3.8 (MIT)
  - @jridgewell/resolve-uri@3.1.2 (MIT)
  - @jridgewell/set-array@1.2.1 (MIT)
  - @jridgewell/source-map@0.3.6 (MIT)
  - @jridgewell/sourcemap-codec@1.5.0 (MIT)
  - @jridgewell/trace-mapping@0.3.25 (MIT)
  - @jsonjoy.com/base64@1.1.2 (Apache-2.0)
  - @jsonjoy.com/json-pack@1.2.0 (Apache-2.0)
  - @jsonjoy.com/util@1.6.0 (Apache-2.0)
  - @leichtgewicht/ip-codec@2.0.5 (MIT)
  - @listr2/prompt-adapter-inquirer@2.0.18 (MIT)
  - @lmdb/lmdb-linux-x64@3.2.6 (MIT)
  - @msgpackr-extract/msgpackr-extract-linux-x64@3.0.3 (MIT)
  - @napi-rs/nice-linux-x64-gnu@1.0.1 (MIT)
  - @napi-rs/nice-linux-x64-musl@1.0.1 (MIT)
  - @napi-rs/nice@1.0.1 (MIT)
  - @ngtools/webpack@19.2.13 (MIT)
  - @nodelib/fs.scandir@2.1.5 (MIT)
  - @nodelib/fs.stat@2.0.5 (MIT)
  - @nodelib/fs.walk@1.2.8 (MIT)
  - @npmcli/agent@3.0.0 (ISC)
  - @npmcli/fs@4.0.0 (ISC)
  - @npmcli/git@6.0.3 (ISC)
  - @npmcli/installed-package-contents@3.0.0 (ISC)
  - @npmcli/node-gyp@4.0.0 (ISC)
  - @npmcli/package-json@6.2.0 (ISC)
  - @npmcli/promise-spawn@8.0.2 (ISC)
  - @npmcli/redact@3.2.2 (ISC)
  - @npmcli/run-script@9.1.0 (ISC)
  - @parcel/watcher-linux-x64-glibc@2.5.1 (MIT)
  - @parcel/watcher-linux-x64-musl@2.5.1 (MIT)
  - @parcel/watcher@2.5.1 (MIT)
  - @pkgjs/parseargs@0.11.0 (MIT)
  - @rollup/rollup-linux-x64-gnu@4.34.8 (MIT)
  - @rollup/rollup-linux-x64-musl@4.34.8 (MIT)
  - @schematics/angular@19.2.13 (MIT)
  - @sigstore/bundle@3.1.0 (Apache-2.0)
  - @sigstore/core@2.0.0 (Apache-2.0)
  - @sigstore/protobuf-specs@0.4.2 (Apache-2.0)
  - @sigstore/sign@3.1.0 (Apache-2.0)
  - @sigstore/tuf@3.1.1 (Apache-2.0)
  - @sigstore/verify@2.1.1 (Apache-2.0)
  - @sindresorhus/merge-streams@2.3.0 (MIT)
  - @socket.io/component-emitter@3.1.2 (MIT)
  - @tufjs/canonical-json@2.0.0 (MIT)
  - @tufjs/models@3.0.1 (MIT)
  - @types/body-parser@1.19.5 (MIT)
  - @types/bonjour@3.5.13 (MIT)
  - @types/connect-history-api-fallback@1.5.4 (MIT)
  - @types/connect@3.4.38 (MIT)
  - @types/cors@2.8.18 (MIT)
  - @types/eslint-scope@3.7.7 (MIT)
  - @types/eslint@9.6.1 (MIT)
  - @types/estree@1.0.6 (MIT)
  - @types/estree@1.0.7 (MIT)
  - @types/express-serve-static-core@4.19.6 (MIT)
  - @types/express-serve-static-core@5.0.6 (MIT)
  - @types/express@4.17.22 (MIT)
  - @types/http-errors@2.0.4 (MIT)
  - @types/http-proxy@1.17.16 (MIT)
  - @types/jasmine@5.1.8 (MIT)
  - @types/json-schema@7.0.15 (MIT)
  - @types/mime@1.3.5 (MIT)
  - @types/node-forge@1.3.11 (MIT)
  - @types/node@22.15.21 (MIT)
  - @types/papaparse@5.3.16 (MIT)
  - @types/qs@6.14.0 (MIT)
  - @types/range-parser@1.2.7 (MIT)
  - @types/retry@0.12.2 (MIT)
  - @types/send@0.17.4 (MIT)
  - @types/serve-index@1.9.4 (MIT)
  - @types/serve-static@1.15.7 (MIT)
  - @types/sockjs@0.3.36 (MIT)
  - @types/ws@8.18.1 (MIT)
  - @vitejs/plugin-basic-ssl@1.2.0 (MIT)
  - @webassemblyjs/ast@1.14.1 (MIT)
  - @webassemblyjs/floating-point-hex-parser@1.13.2 (MIT)
  - @webassemblyjs/helper-api-error@1.13.2 (MIT)
  - @webassemblyjs/helper-buffer@1.14.1 (MIT)
  - @webassemblyjs/helper-numbers@1.13.2 (MIT)
  - @webassemblyjs/helper-wasm-bytecode@1.13.2 (MIT)
  - @webassemblyjs/helper-wasm-section@1.14.1 (MIT)
  - @webassemblyjs/ieee754@1.13.2 (MIT)
  - @webassemblyjs/leb128@1.13.2 (Apache-2.0)
  - @webassemblyjs/utf8@1.13.2 (MIT)
  - @webassemblyjs/wasm-edit@1.14.1 (MIT)
  - @webassemblyjs/wasm-gen@1.14.1 (MIT)
  - @webassemblyjs/wasm-opt@1.14.1 (MIT)
  - @webassemblyjs/wasm-parser@1.14.1 (MIT)
  - @webassemblyjs/wast-printer@1.14.1 (MIT)
  - @xtuc/ieee754@1.2.0 (BSD-3-Clause)
  - @xtuc/long@4.2.2 (Apache-2.0)
  - @yarnpkg/lockfile@1.1.0 (BSD-2-Clause)
  - abbrev@3.0.1 (ISC)
  - accepts@1.3.8 (MIT)
  - acorn@8.14.1 (MIT)
  - adjust-sourcemap-loader@4.0.0 (MIT)
  - agent-base@7.1.3 (MIT)
  - ajv-formats@2.1.1 (MIT)
  - ajv-formats@3.0.1 (MIT)
  - ajv-keywords@5.1.0 (MIT)
  - ajv@8.17.1 (MIT)
  - ansi-colors@4.1.3 (MIT)
  - ansi-escapes@4.3.2 (MIT)
  - ansi-escapes@7.0.0 (MIT)
  - ansi-html-community@0.0.8 (Apache-2.0)
  - ansi-regex@5.0.1 (MIT)
  - ansi-regex@6.1.0 (MIT)
  - ansi-styles@4.3.0 (MIT)
  - ansi-styles@6.2.1 (MIT)
  - anymatch@3.1.3 (ISC)
  - array-flatten@1.1.1 (MIT)
  - autoprefixer@10.4.20 (MIT)
  - babel-loader@9.2.1 (MIT)
  - babel-plugin-polyfill-corejs2@0.4.13 (MIT)
  - babel-plugin-polyfill-corejs3@0.11.1 (MIT)
  - babel-plugin-polyfill-regenerator@0.6.4 (MIT)
  - balanced-match@1.0.2 (MIT)
  - base64-js@1.5.1 (MIT)
  - base64id@2.0.0 (MIT)
  - batch@0.6.1 (MIT)
  - beasties@0.3.2 (Apache-2.0)
  - big.js@5.2.2 (MIT)
  - binary-extensions@2.3.0 (MIT)
  - bl@4.1.0 (MIT)
  - body-parser@1.20.3 (MIT)
  - bonjour-service@1.3.0 (MIT)
  - boolbase@1.0.0 (ISC)
  - brace-expansion@1.1.12 (MIT)
  - brace-expansion@2.0.2 (MIT)
  - braces@3.0.3 (MIT)
  - browserslist@4.24.5 (MIT)
  - buffer-from@1.1.2 (MIT)
  - buffer@5.7.1 (MIT)
  - bundle-name@4.1.0 (MIT)
  - bytes@3.1.2 (MIT)
  - cacache@19.0.1 (ISC)
  - call-bind-apply-helpers@1.0.2 (MIT)
  - call-bound@1.0.4 (MIT)
  - callsites@3.1.0 (MIT)
  - chalk@4.1.2 (MIT)
  - chardet@0.7.0 (MIT)
  - chokidar@3.6.0 (MIT)
  - chokidar@4.0.3 (MIT)
  - chownr@2.0.0 (ISC)
  - chownr@3.0.0 (BlueOak-1.0.0)
  - chrome-trace-event@1.0.4 (MIT)
  - cli-cursor@3.1.0 (MIT)
  - cli-cursor@5.0.0 (MIT)
  - cli-spinners@2.9.2 (MIT)
  - cli-truncate@4.0.0 (MIT)
  - cli-width@4.1.0 (ISC)
  - cliui@7.0.4 (ISC)
  - cliui@8.0.1 (ISC)
  - clone-deep@4.0.1 (MIT)
  - clone@1.0.4 (MIT)
  - color-convert@2.0.1 (MIT)
  - color-name@1.1.4 (MIT)
  - colorette@2.0.20 (MIT)
  - commander@2.20.3 (MIT)
  - common-path-prefix@3.0.0 (ISC)
  - compressible@2.0.18 (MIT)
  - compression@1.8.0 (MIT)
  - concat-map@0.0.1 (MIT)
  - connect-history-api-fallback@2.0.0 (MIT)
  - connect@3.7.0 (MIT)
  - content-disposition@0.5.4 (MIT)
  - content-type@1.0.5 (MIT)
  - convert-source-map@1.9.0 (MIT)
  - convert-source-map@2.0.0 (MIT)
  - cookie-signature@1.0.6 (MIT)
  - cookie@0.7.1 (MIT)
  - cookie@0.7.2 (MIT)
  - copy-anything@2.0.6 (MIT)
  - copy-webpack-plugin@12.0.2 (MIT)
  - core-js-compat@3.42.0 (MIT)
  - core-util-is@1.0.3 (MIT)
  - cors@2.8.5 (MIT)
  - cosmiconfig@9.0.0 (MIT)
  - cross-spawn@7.0.6 (MIT)
  - css-loader@7.1.2 (MIT)
  - css-select@5.1.0 (BSD-2-Clause)
  - css-what@6.1.0 (BSD-2-Clause)
  - cssesc@3.0.0 (MIT)
  - custom-event@1.0.1 (MIT)
  - date-fns@2.30.0 (MIT)
  - date-format@4.0.14 (MIT)
  - debug@2.6.9 (MIT)
  - debug@4.3.7 (MIT)
  - debug@4.4.1 (MIT)
  - default-browser-id@5.0.0 (MIT)
  - default-browser@5.2.1 (MIT)
  - defaults@1.0.4 (MIT)
  - define-lazy-prop@3.0.0 (MIT)
  - depd@1.1.2 (MIT)
  - depd@2.0.0 (MIT)
  - destroy@1.2.0 (MIT)
  - detect-libc@1.0.3 (Apache-2.0)
  - detect-libc@2.0.4 (Apache-2.0)
  - detect-node@2.1.0 (MIT)
  - di@0.0.1 (MIT)
  - dns-packet@5.6.1 (MIT)
  - dom-serialize@2.2.1 (MIT)
  - dom-serializer@2.0.0 (MIT)
  - domelementtype@2.3.0 (BSD-2-Clause)
  - domhandler@5.0.3 (BSD-2-Clause)
  - domutils@3.2.2 (BSD-2-Clause)
  - dunder-proto@1.0.1 (MIT)
  - eastasianwidth@0.2.0 (MIT)
  - ee-first@1.1.1 (MIT)
  - electron-to-chromium@1.5.157 (ISC)
  - emoji-regex@10.4.0 (MIT)
  - emoji-regex@8.0.0 (MIT)
  - emoji-regex@9.2.2 (MIT)
  - emojis-list@3.0.0 (MIT)
  - encodeurl@1.0.2 (MIT)
  - encodeurl@2.0.0 (MIT)
  - encoding@0.1.13 (MIT)
  - engine.io-parser@5.2.3 (MIT)
  - engine.io@6.6.4 (MIT)
  - enhanced-resolve@5.18.1 (MIT)
  - ent@2.2.2 (MIT)
  - entities@4.5.0 (BSD-2-Clause)
  - entities@6.0.0 (BSD-2-Clause)
  - env-paths@2.2.1 (MIT)
  - environment@1.1.0 (MIT)
  - err-code@2.0.3 (MIT)
  - errno@0.1.8 (MIT)
  - error-ex@1.3.2 (MIT)
  - es-define-property@1.0.1 (MIT)
  - es-errors@1.3.0 (MIT)
  - es-module-lexer@1.7.0 (MIT)
  - es-object-atoms@1.1.1 (MIT)
  - esbuild-wasm@0.25.4 (MIT)
  - esbuild@0.25.4 (MIT)
  - escalade@3.2.0 (MIT)
  - escape-html@1.0.3 (MIT)
  - eslint-scope@5.1.1 (BSD-2-Clause)
  - esrecurse@4.3.0 (BSD-2-Clause)
  - estraverse@4.3.0 (BSD-2-Clause)
  - estraverse@5.3.0 (BSD-2-Clause)
  - esutils@2.0.3 (BSD-2-Clause)
  - etag@1.8.1 (MIT)
  - eventemitter3@4.0.7 (MIT)
  - eventemitter3@5.0.1 (MIT)
  - events@3.3.0 (MIT)
  - exponential-backoff@3.1.2 (Apache-2.0)
  - express@4.21.2 (MIT)
  - extend@3.0.2 (MIT)
  - external-editor@3.1.0 (MIT)
  - fast-deep-equal@3.1.3 (MIT)
  - fast-glob@3.3.3 (MIT)
  - fast-uri@3.0.6 (BSD-3-Clause)
  - fastq@1.19.1 (ISC)
  - faye-websocket@0.11.4 (Apache-2.0)
  - fdir@6.4.4 (MIT)
  - fill-range@7.1.1 (MIT)
  - finalhandler@1.1.2 (MIT)
  - finalhandler@1.3.1 (MIT)
  - find-cache-dir@4.0.0 (MIT)
  - find-up@6.3.0 (MIT)
  - flat@5.0.2 (BSD-3-Clause)
  - flatted@3.3.3 (ISC)
  - follow-redirects@1.15.9 (MIT)
  - foreground-child@3.3.1 (ISC)
  - forwarded@0.2.0 (MIT)
  - fraction.js@4.3.7 (MIT)
  - fresh@0.5.2 (MIT)
  - fs-extra@8.1.0 (MIT)
  - fs-minipass@2.1.0 (ISC)
  - fs-minipass@3.0.3 (ISC)
  - fs.realpath@1.0.0 (ISC)
  - function-bind@1.1.2 (MIT)
  - gensync@1.0.0-beta.2 (MIT)
  - get-caller-file@2.0.5 (ISC)
  - get-east-asian-width@1.3.0 (MIT)
  - get-intrinsic@1.3.0 (MIT)
  - get-proto@1.0.1 (MIT)
  - glob-parent@5.1.2 (ISC)
  - glob-parent@6.0.2 (ISC)
  - glob-to-regexp@0.4.1 (BSD-2-Clause)
  - glob@10.4.5 (ISC)
  - glob@7.2.3 (ISC)
  - globals@11.12.0 (MIT)
  - globby@14.1.0 (MIT)
  - gopd@1.2.0 (MIT)
  - graceful-fs@4.2.11 (ISC)
  - handle-thing@2.0.1 (MIT)
  - has-flag@4.0.0 (MIT)
  - has-symbols@1.1.0 (MIT)
  - has-tostringtag@1.0.2 (MIT)
  - hasown@2.0.2 (MIT)
  - hosted-git-info@8.1.0 (ISC)
  - hpack.js@2.1.6 (MIT)
  - html-escaper@2.0.2 (MIT)
  - htmlparser2@10.0.0 (MIT)
  - http-cache-semantics@4.2.0 (BSD-2-Clause)
  - http-deceiver@1.2.7 (MIT)
  - http-errors@1.6.3 (MIT)
  - http-errors@2.0.0 (MIT)
  - http-parser-js@0.5.10 (MIT)
  - http-proxy-agent@7.0.2 (MIT)
  - http-proxy-middleware@2.0.9 (MIT)
  - http-proxy-middleware@3.0.5 (MIT)
  - http-proxy@1.18.1 (MIT)
  - https-proxy-agent@7.0.6 (MIT)
  - hyperdyperid@1.2.0 (MIT)
  - iconv-lite@0.4.24 (MIT)
  - iconv-lite@0.6.3 (MIT)
  - icss-utils@5.1.0 (ISC)
  - ieee754@1.2.1 (BSD-3-Clause)
  - ignore-walk@7.0.0 (ISC)
  - ignore@7.0.4 (MIT)
  - image-size@0.5.5 (MIT)
  - immutable@5.1.2 (MIT)
  - import-fresh@3.3.1 (MIT)
  - imurmurhash@0.1.4 (MIT)
  - inflight@1.0.6 (ISC)
  - inherits@2.0.3 (ISC)
  - inherits@2.0.4 (ISC)
  - ini@5.0.0 (ISC)
  - ip-address@9.0.5 (MIT)
  - ipaddr.js@1.9.1 (MIT)
  - ipaddr.js@2.2.0 (MIT)
  - is-arrayish@0.2.1 (MIT)
  - is-binary-path@2.1.0 (MIT)
  - is-core-module@2.16.1 (MIT)
  - is-docker@3.0.0 (MIT)
  - is-extglob@2.1.1 (MIT)
  - is-fullwidth-code-point@3.0.0 (MIT)
  - is-fullwidth-code-point@4.0.0 (MIT)
  - is-fullwidth-code-point@5.0.0 (MIT)
  - is-glob@4.0.3 (MIT)
  - is-inside-container@1.0.0 (MIT)
  - is-interactive@1.0.0 (MIT)
  - is-network-error@1.1.0 (MIT)
  - is-number@7.0.0 (MIT)
  - is-plain-obj@3.0.0 (MIT)
  - is-plain-object@2.0.4 (MIT)
  - is-plain-object@5.0.0 (MIT)
  - is-regex@1.2.1 (MIT)
  - is-unicode-supported@0.1.0 (MIT)
  - is-what@3.14.1 (MIT)
  - is-wsl@3.1.0 (MIT)
  - isarray@1.0.0 (MIT)
  - isbinaryfile@4.0.10 (MIT)
  - isexe@2.0.0 (ISC)
  - isexe@3.1.1 (ISC)
  - isobject@3.0.1 (MIT)
  - istanbul-lib-coverage@3.2.2 (BSD-3-Clause)
  - istanbul-lib-instrument@5.2.1 (BSD-3-Clause)
  - istanbul-lib-instrument@6.0.3 (BSD-3-Clause)
  - istanbul-lib-report@3.0.1 (BSD-3-Clause)
  - istanbul-lib-source-maps@4.0.1 (BSD-3-Clause)
  - istanbul-reports@3.1.7 (BSD-3-Clause)
  - jackspeak@3.4.3 (BlueOak-1.0.0)
  - jasmine-core@4.6.1 (MIT)
  - jasmine-core@5.6.0 (MIT)
  - jest-worker@27.5.1 (MIT)
  - jiti@1.21.7 (MIT)
  - js-tokens@4.0.0 (MIT)
  - js-yaml@4.1.0 (MIT)
  - jsbn@1.1.0 (MIT)
  - jsesc@3.0.2 (MIT)
  - jsesc@3.1.0 (MIT)
  - json-parse-even-better-errors@2.3.1 (MIT)
  - json-parse-even-better-errors@4.0.0 (MIT)
  - json-schema-traverse@1.0.0 (MIT)
  - json5@2.2.3 (MIT)
  - jsonc-parser@3.3.1 (MIT)
  - jsonfile@4.0.0 (MIT)
  - jsonparse@1.3.1 (MIT)
  - karma-chrome-launcher@3.2.0 (MIT)
  - karma-coverage@2.2.1 (MIT)
  - karma-jasmine-html-reporter@2.1.0 (MIT)
  - karma-jasmine@5.1.0 (MIT)
  - karma-source-map-support@1.4.0 (MIT)
  - karma@6.4.4 (MIT)
  - kind-of@6.0.3 (MIT)
  - launch-editor@2.10.0 (MIT)
  - less-loader@12.2.0 (MIT)
  - less@4.2.2 (Apache-2.0)
  - license-webpack-plugin@4.0.2 (ISC)
  - lines-and-columns@1.2.4 (MIT)
  - listr2@8.2.5 (MIT)
  - lmdb@3.2.6 (MIT)
  - loader-runner@4.3.0 (MIT)
  - loader-utils@2.0.4 (MIT)
  - loader-utils@3.3.1 (MIT)
  - locate-path@7.2.0 (MIT)
  - lodash.debounce@4.0.8 (MIT)
  - lodash@4.17.21 (MIT)
  - log-symbols@4.1.0 (MIT)
  - log-update@6.1.0 (MIT)
  - log4js@6.9.1 (Apache-2.0)
  - lru-cache@10.4.3 (ISC)
  - lru-cache@5.1.1 (ISC)
  - magic-string@0.30.17 (MIT)
  - make-dir@2.1.0 (MIT)
  - make-dir@4.0.0 (MIT)
  - make-fetch-happen@14.0.3 (ISC)
  - math-intrinsics@1.1.0 (MIT)
  - media-typer@0.3.0 (MIT)
  - memfs@4.17.2 (Apache-2.0)
  - merge-descriptors@1.0.3 (MIT)
  - merge-stream@2.0.0 (MIT)
  - merge2@1.4.1 (MIT)
  - methods@1.1.2 (MIT)
  - micromatch@4.0.8 (MIT)
  - mime-db@1.52.0 (MIT)
  - mime-types@2.1.35 (MIT)
  - mime@1.6.0 (MIT)
  - mime@2.6.0 (MIT)
  - mimic-fn@2.1.0 (MIT)
  - mimic-function@5.0.1 (MIT)
  - mini-css-extract-plugin@2.9.2 (MIT)
  - minimalistic-assert@1.0.1 (ISC)
  - minimatch@3.1.2 (ISC)
  - minimatch@9.0.5 (ISC)
  - minimist@1.2.8 (MIT)
  - minipass-collect@2.0.1 (ISC)
  - minipass-fetch@4.0.1 (MIT)
  - minipass-flush@1.0.5 (ISC)
  - minipass-pipeline@1.2.4 (ISC)
  - minipass-sized@1.0.3 (ISC)
  - minipass@3.3.6 (ISC)
  - minipass@5.0.0 (ISC)
  - minipass@7.1.2 (ISC)
  - minizlib@2.1.2 (MIT)
  - minizlib@3.0.2 (MIT)
  - mkdirp@0.5.6 (MIT)
  - mkdirp@1.0.4 (MIT)
  - mkdirp@3.0.1 (MIT)
  - mrmime@2.0.1 (MIT)
  - ms@2.0.0 (MIT)
  - ms@2.1.3 (MIT)
  - msgpackr-extract@3.0.3 (MIT)
  - msgpackr@1.11.4 (MIT)
  - multicast-dns@7.2.5 (MIT)
  - mute-stream@1.0.0 (ISC)
  - mute-stream@2.0.0 (ISC)
  - nanoid@3.3.11 (MIT)
  - needle@3.3.1 (MIT)
  - negotiator@0.6.3 (MIT)
  - negotiator@0.6.4 (MIT)
  - negotiator@1.0.0 (MIT)
  - neo-async@2.6.2 (MIT)
  - ng-zorro-antd@19.3.0 (MIT)
  - node-addon-api@6.1.0 (MIT)
  - node-addon-api@7.1.1 (MIT)
  - node-gyp-build-optional-packages@5.2.2 (MIT)
  - node-gyp@11.2.0 (MIT)
  - node-releases@2.0.19 (MIT)
  - nopt@8.1.0 (ISC)
  - normalize-path@3.0.0 (MIT)
  - normalize-range@0.1.2 (MIT)
  - npm-bundled@4.0.0 (ISC)
  - npm-install-checks@7.1.1 (BSD-2-Clause)
  - npm-normalize-package-bin@4.0.0 (ISC)
  - npm-package-arg@12.0.2 (ISC)
  - npm-packlist@9.0.0 (ISC)
  - npm-pick-manifest@10.0.0 (ISC)
  - npm-registry-fetch@18.0.2 (ISC)
  - nth-check@2.1.1 (BSD-2-Clause)
  - object-assign@4.1.1 (MIT)
  - object-inspect@1.13.4 (MIT)
  - obuf@1.1.2 (MIT)
  - on-finished@2.3.0 (MIT)
  - on-finished@2.4.1 (MIT)
  - on-headers@1.0.2 (MIT)
  - once@1.4.0 (ISC)
  - onetime@5.1.2 (MIT)
  - onetime@7.0.0 (MIT)
  - open@10.1.0 (MIT)
  - ora@5.4.1 (MIT)
  - ordered-binary@1.5.3 (MIT)
  - os-tmpdir@1.0.2 (MIT)
  - p-limit@4.0.0 (MIT)
  - p-locate@6.0.0 (MIT)
  - p-map@7.0.3 (MIT)
  - p-retry@6.2.1 (MIT)
  - package-json-from-dist@1.0.1 (BlueOak-1.0.0)
  - pacote@20.0.0 (ISC)
  - papaparse@5.5.3 (MIT)
  - parent-module@1.0.1 (MIT)
  - parse-json@5.2.0 (MIT)
  - parse-node-version@1.0.1 (MIT)
  - parse5-html-rewriting-stream@7.0.0 (MIT)
  - parse5-sax-parser@7.0.0 (MIT)
  - parse5@7.3.0 (MIT)
  - parseurl@1.3.3 (MIT)
  - path-exists@5.0.0 (MIT)
  - path-is-absolute@1.0.1 (MIT)
  - path-key@3.1.1 (MIT)
  - path-parse@1.0.7 (MIT)
  - path-scurry@1.11.1 (BlueOak-1.0.0)
  - path-to-regexp@0.1.12 (MIT)
  - path-type@6.0.0 (MIT)
  - picocolors@1.1.1 (ISC)
  - picomatch@2.3.1 (MIT)
  - picomatch@4.0.2 (MIT)
  - pify@4.0.1 (MIT)
  - piscina@4.8.0 (MIT)
  - pkg-dir@7.0.0 (MIT)
  - postcss-loader@8.1.1 (MIT)
  - postcss-media-query-parser@0.2.3 (MIT)
  - postcss-modules-extract-imports@3.1.0 (ISC)
  - postcss-modules-local-by-default@4.2.0 (MIT)
  - postcss-modules-scope@3.2.1 (ISC)
  - postcss-modules-values@4.0.0 (ISC)
  - postcss-selector-parser@7.1.0 (MIT)
  - postcss-value-parser@4.2.0 (MIT)
  - postcss@8.5.2 (MIT)
  - postcss@8.5.3 (MIT)
  - proc-log@5.0.0 (ISC)
  - process-nextick-args@2.0.1 (MIT)
  - promise-retry@2.0.1 (MIT)
  - proxy-addr@2.0.7 (MIT)
  - prr@1.0.1 (MIT)
  - punycode@1.4.1 (MIT)
  - qjobs@1.2.0 (MIT)
  - qs@6.13.0 (BSD-3-Clause)
  - queue-microtask@1.2.3 (MIT)
  - randombytes@2.1.0 (MIT)
  - range-parser@1.2.1 (MIT)
  - raw-body@2.5.2 (MIT)
  - readable-stream@2.3.8 (MIT)
  - readable-stream@3.6.2 (MIT)
  - readdirp@3.6.0 (MIT)
  - readdirp@4.1.2 (MIT)
  - reflect-metadata@0.2.2 (Apache-2.0)
  - regenerate-unicode-properties@10.2.0 (MIT)
  - regenerate@1.4.2 (MIT)
  - regenerator-runtime@0.14.1 (MIT)
  - regex-parser@2.3.1 (MIT)
  - regexpu-core@6.2.0 (MIT)
  - regjsgen@0.8.0 (MIT)
  - regjsparser@0.12.0 (BSD-2-Clause)
  - require-directory@2.1.1 (MIT)
  - require-from-string@2.0.2 (MIT)
  - requires-port@1.0.0 (MIT)
  - resolve-from@4.0.0 (MIT)
  - resolve-url-loader@5.0.0 (MIT)
  - resolve@1.22.10 (MIT)
  - restore-cursor@3.1.0 (MIT)
  - restore-cursor@5.1.0 (MIT)
  - retry@0.12.0 (MIT)
  - retry@0.13.1 (MIT)
  - reusify@1.1.0 (MIT)
  - rfdc@1.4.1 (MIT)
  - rimraf@3.0.2 (ISC)
  - rollup@4.34.8 (MIT)
  - rollup@4.41.1 (MIT)
  - run-applescript@7.0.0 (MIT)
  - run-parallel@1.2.0 (MIT)
  - rxjs@7.8.1 (Apache-2.0)
  - rxjs@7.8.2 (Apache-2.0)
  - safe-buffer@5.1.2 (MIT)
  - safe-buffer@5.2.1 (MIT)
  - safe-regex-test@1.1.0 (MIT)
  - safer-buffer@2.1.2 (MIT)
  - sass-loader@16.0.5 (MIT)
  - sass@1.85.0 (MIT)
  - sax@1.4.1 (ISC)
  - schema-utils@4.3.2 (MIT)
  - select-hose@2.0.0 (MIT)
  - selfsigned@2.4.1 (MIT)
  - semver@5.7.2 (ISC)
  - semver@6.3.1 (ISC)
  - semver@7.7.1 (ISC)
  - send@0.19.0 (MIT)
  - serialize-javascript@6.0.2 (BSD-3-Clause)
  - serve-index@1.9.1 (MIT)
  - serve-static@1.16.2 (MIT)
  - setprototypeof@1.1.0 (ISC)
  - setprototypeof@1.2.0 (ISC)
  - shallow-clone@3.0.1 (MIT)
  - shebang-command@2.0.0 (MIT)
  - shebang-regex@3.0.0 (MIT)
  - shell-quote@1.8.2 (MIT)
  - side-channel-list@1.0.0 (MIT)
  - side-channel-map@1.0.1 (MIT)
  - side-channel-weakmap@1.0.2 (MIT)
  - side-channel@1.1.0 (MIT)
  - signal-exit@3.0.7 (ISC)
  - signal-exit@4.1.0 (ISC)
  - sigstore@3.1.0 (Apache-2.0)
  - slash@5.1.0 (MIT)
  - slice-ansi@5.0.0 (MIT)
  - slice-ansi@7.1.0 (MIT)
  - smart-buffer@4.2.0 (MIT)
  - socket.io-adapter@2.5.5 (MIT)
  - socket.io-parser@4.2.4 (MIT)
  - socket.io@4.8.1 (MIT)
  - sockjs@0.3.24 (MIT)
  - socks-proxy-agent@8.0.5 (MIT)
  - socks@2.8.4 (MIT)
  - source-map-js@1.2.1 (BSD-3-Clause)
  - source-map-loader@5.0.0 (MIT)
  - source-map-support@0.5.21 (MIT)
  - source-map@0.6.1 (BSD-3-Clause)
  - source-map@0.7.4 (BSD-3-Clause)
  - spdx-correct@3.2.0 (Apache-2.0)
  - spdx-expression-parse@3.0.1 (MIT)
  - spdx-license-ids@3.0.21 (CC0-1.0)
  - spdy-transport@3.0.0 (MIT)
  - spdy@4.0.2 (MIT)
  - sprintf-js@1.1.3 (BSD-3-Clause)
  - ssri@12.0.0 (ISC)
  - statuses@1.5.0 (MIT)
  - statuses@2.0.1 (MIT)
  - streamroller@3.1.5 (MIT)
  - string-width@4.2.3 (MIT)
  - string-width@5.1.2 (MIT)
  - string-width@7.2.0 (MIT)
  - string_decoder@1.1.1 (MIT)
  - string_decoder@1.3.0 (MIT)
  - strip-ansi@6.0.1 (MIT)
  - strip-ansi@7.1.0 (MIT)
  - supports-color@7.2.0 (MIT)
  - supports-color@8.1.1 (MIT)
  - supports-preserve-symlinks-flag@1.0.0 (MIT)
  - symbol-observable@4.0.0 (MIT)
  - tapable@2.2.2 (MIT)
  - tar@6.2.1 (ISC)
  - tar@7.4.3 (ISC)
  - terser-webpack-plugin@5.3.14 (MIT)
  - terser@5.39.0 (BSD-2-Clause)
  - thunky@1.1.0 (MIT)
  - tinyglobby@0.2.14 (MIT)
  - tmp@0.0.33 (MIT)
  - tmp@0.2.3 (MIT)
  - to-regex-range@5.0.1 (MIT)
  - toidentifier@1.0.1 (MIT)
  - tree-dump@1.0.3 (Apache-2.0)
  - tree-kill@1.2.2 (MIT)
  - tuf-js@3.0.1 (MIT)
  - type-is@1.6.18 (MIT)
  - typed-assert@1.0.9 (MIT)
  - typescript@5.7.3 (Apache-2.0)
  - ua-parser-js@0.7.40 (MIT)
  - undici-types@6.21.0 (MIT)
  - unicode-canonical-property-names-ecmascript@2.0.1 (MIT)
  - unicode-match-property-ecmascript@2.0.0 (MIT)
  - unicode-match-property-value-ecmascript@2.2.0 (MIT)
  - unicode-property-aliases-ecmascript@2.1.0 (MIT)
  - unicorn-magic@0.3.0 (MIT)
  - unique-filename@4.0.0 (ISC)
  - unique-slug@5.0.0 (ISC)
  - universalify@0.1.2 (MIT)
  - unpipe@1.0.0 (MIT)
  - update-browserslist-db@1.1.3 (MIT)
  - util-deprecate@1.0.2 (MIT)
  - utils-merge@1.0.1 (MIT)
  - uuid@8.3.2 (MIT)
  - validate-npm-package-license@3.0.4 (Apache-2.0)
  - validate-npm-package-name@6.0.0 (ISC)
  - vary@1.1.2 (MIT)
  - vite@6.2.7 (MIT)
  - vite@6.3.5 (MIT)
  - void-elements@2.0.1 (MIT)
  - watchpack@2.4.2 (MIT)
  - wbuf@1.7.3 (MIT)
  - wcwidth@1.0.1 (MIT)
  - weak-lru-cache@1.2.2 (MIT)
  - webpack-dev-middleware@7.4.2 (MIT)
  - webpack-dev-server@5.2.0 (MIT)
  - webpack-merge@6.0.1 (MIT)
  - webpack-sources@3.3.0 (MIT)
  - webpack-subresource-integrity@5.1.0 (MIT)
  - webpack@5.98.0 (MIT)
  - websocket-driver@0.7.4 (Apache-2.0)
  - websocket-extensions@0.1.4 (Apache-2.0)
  - which@1.3.1 (ISC)
  - which@2.0.2 (ISC)
  - which@5.0.0 (ISC)
  - wildcard@2.0.1 (MIT)
  - wrap-ansi@6.2.0 (MIT)
  - wrap-ansi@7.0.0 (MIT)
  - wrap-ansi@8.1.0 (MIT)
  - wrap-ansi@9.0.0 (MIT)
  - wrappy@1.0.2 (ISC)
  - ws@8.17.1 (MIT)
  - ws@8.18.2 (MIT)
  - y18n@5.0.8 (ISC)
  - yallist@3.1.1 (ISC)
  - yallist@4.0.0 (ISC)
  - yallist@5.0.0 (BlueOak-1.0.0)
  - yargs-parser@20.2.9 (ISC)
  - yargs-parser@21.1.1 (ISC)
  - yargs@16.2.0 (MIT)
  - yargs@17.7.2 (MIT)
  - yocto-queue@1.2.1 (MIT)
  - yoctocolors-cjs@2.1.2 (MIT)
  - zone.js@0.15.1 (MIT)
  - @bazel/runfiles@6.3.1 (Apache-2.0)
  - @cucumber/ci-environment@10.0.1 (MIT)
  - @cucumber/cucumber-expressions@18.0.1 (MIT)
  - @cucumber/cucumber@11.3.0 (MIT)
  - @cucumber/gherkin-streams@5.0.1 (MIT)
  - @cucumber/gherkin-utils@9.2.0 (MIT)
  - @cucumber/gherkin@30.0.4 (MIT)
  - @cucumber/gherkin@31.0.0 (MIT)
  - @cucumber/html-formatter@21.10.1 (MIT)
  - @cucumber/junit-xml-formatter@0.7.1 (MIT)
  - @cucumber/message-streams@4.0.1 (MIT)
  - @cucumber/messages@26.0.1 (MIT)
  - @cucumber/messages@27.2.0 (MIT)
  - @cucumber/query@13.2.0 (MIT)
  - @cucumber/tag-expressions@6.1.2 (MIT)
  - @hapi/hoek@9.3.0 (BSD-3-Clause)
  - @hapi/topo@5.1.0 (BSD-3-Clause)
  - @sideway/address@4.1.5 (BSD-3-Clause)
  - @sideway/formula@3.0.1 (BSD-3-Clause)
  - @sideway/pinpoint@2.0.0 (BSD-3-Clause)
  - @teppeis/multimaps@3.0.0 (MIT)
  - @types/normalize-package-data@2.4.4 (MIT)
  - @types/uuid@10.0.0 (MIT)
  - ansi-regex@4.1.1 (MIT)
  - any-promise@1.3.0 (MIT)
  - assertion-error-formatter@3.0.0 (MIT)
  - asynckit@0.4.0 (MIT)
  - axios@1.9.0 (MIT)
  - capital-case@1.0.4 (MIT)
  - class-transformer@0.5.1 (MIT)
  - cli-table3@0.6.5 (MIT)
  - combined-stream@1.0.8 (MIT)
  - commander@10.0.1 (MIT)
  - commander@13.1.0 (MIT)
  - commander@9.1.0 (MIT)
  - delayed-stream@1.0.0 (MIT)
  - diff@4.0.2 (BSD-3-Clause)
  - error-stack-parser@2.1.4 (MIT)
  - es-set-tostringtag@2.1.0 (MIT)
  - escape-string-regexp@1.0.5 (MIT)
  - figures@3.2.0 (MIT)
  - find-up-simple@1.0.1 (MIT)
  - form-data@4.0.2 (MIT)
  - global-dirs@3.0.1 (MIT)
  - has-ansi@4.0.1 (MIT)
  - hosted-git-info@7.0.2 (ISC)
  - immediate@3.0.6 (MIT)
  - indent-string@4.0.0 (MIT)
  - index-to-position@1.1.0 (MIT)
  - ini@2.0.0 (ISC)
  - is-installed-globally@0.4.0 (MIT)
  - is-path-inside@3.0.3 (MIT)
  - is-stream@2.0.1 (MIT)
  - joi@17.13.3 (BSD-3-Clause)
  - knuth-shuffle-seeded@1.0.6 (Apache-2.0)
  - lie@3.3.0 (MIT)
  - lodash.merge@4.6.2 (MIT)
  - lodash.mergewith@4.6.2 (MIT)
  - lower-case@2.0.2 (MIT)
  - luxon@3.6.1 (MIT)
  - mime@3.0.0 (MIT)
  - mkdirp@2.1.6 (MIT)
  - mz@2.7.0 (MIT)
  - no-case@3.0.4 (MIT)
  - normalize-package-data@6.0.2 (BSD-2-Clause)
  - pad-right@0.2.2 (MIT)
  - parse-json@8.3.0 (MIT)
  - progress@2.0.3 (MIT)
  - property-expr@2.0.6 (MIT)
  - proxy-from-env@1.1.0 (MIT)
  - read-package-up@11.0.0 (MIT)
  - read-pkg@9.0.1 (MIT)
  - regexp-match-indices@1.0.2 (Apache-2.0)
  - regexp-tree@0.1.27 (MIT)
  - repeat-string@1.6.1 (MIT)
  - seed-random@2.2.0 (MIT)
  - selenium-webdriver@4.32.0 (Apache-2.0)
  - setimmediate@1.0.5 (MIT)
  - stackframe@1.3.4 (MIT)
  - string-argv@0.3.1 (MIT)
  - thenify-all@1.6.0 (MIT)
  - thenify@3.3.1 (MIT)
  - tiny-case@1.0.3 (MIT)
  - toposort@2.0.2 (MIT)
  - unicorn-magic@0.1.0 (MIT)
  - upper-case-first@2.0.2 (MIT)
  - util-arity@1.1.0 (MIT)
  - uuid@10.0.0 (MIT)
  - uuid@11.0.5 (MIT)
  - wait-on@8.0.3 (MIT)
  - xmlbuilder@15.1.1 (MIT)
  - yaml@2.8.0 (ISC)
  - yup@1.6.1 (MIT)

EXCLUDED - SKIPPED (2 packages):
  - onramp-ui@0.0.0 (UNLICENSED)
  - e2e@0.1.0 (UNKNOWN)

NOT ALLOWED (11 packages):
  - argparse@2.0.1 (Python-2.0)
  - caniuse-lite@1.0.30001718 (CC-BY-4.0)
  - node-forge@1.3.1 ((BSD-3-Clause OR GPL-2.0))
  - spdx-exceptions@2.5.0 (CC-BY-3.0)
  - thingies@1.21.0 (Unlicense)
  - tslib@2.8.1 (0BSD)
  - type-fest@0.21.3 ((MIT OR CC0-1.0))
  - jszip@3.10.1 ((MIT OR GPL-3.0-or-later))
  - pako@1.0.11 ((MIT AND Zlib))
  - type-fest@2.19.0 ((MIT OR CC0-1.0))
  - type-fest@4.41.0 ((MIT OR CC0-1.0))


================================================================================
                                 SUMMARY
================================================================================

PACKAGE STATISTICS:
  Total Packages Analyzed: 1831

  Go Modules:
    Accepted: 894
    Rejected: 2

  JavaScript/Node.js:
    Accepted: 924
    Rejected: 11

  OVERALL:
    Total Accepted: 1818
    Total Rejected: 13

STATUS: ATTENTION REQUIRED - 13 PACKAGES WITH NON-APPROVED LICENSES


NEXT STEPS:
1. Review packages marked as NOT ALLOWED above
2. Consider updating to packages with approved licenses
3. Seek legal approval for packages with restricted licenses
4. Update license-config.yaml if licenses are approved
================================================================================
Report saved to: //microservices/license-checker/license-reports/license-report.txt
================================================================================
