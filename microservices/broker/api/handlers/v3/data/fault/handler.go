package fault

import (
	// "encoding/json"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"synapse-its.com/broker/api/brokerShared"
	authorizer "synapse-its.com/shared/api/authorizer"
	response "synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	logger "synapse-its.com/shared/logger"
	"synapse-its.com/shared/schemas"
)

// TODO: Inject depenencies instead of saving functions on the package level, to allow for t.Parallel
var (
	userPermissionsFromContext = authorizer.UserPermissionsFromContext
	deviceProcessRmsData       = devices.ProcessRmsData
	jsonMarshalIndentFn        = json.MarshalIndent
	jsonCleanFn                = ReplaceNullsInJSON
	validateDeviceAccess       = brokerShared.ValidateDeviceAccess
)

// Hand<PERSON> retrieves fault logs and diagnostic data for authorized devices.
//
// @Summary      Get device fault logs and diagnostics
// @Description  Retrieves comprehensive fault information including monitor reset logs, previous failures, configuration changes, AC line events, and fault signal sequences. The endpoint processes fault data and returns device-specific diagnostic information based on the monitor model (ECL2010, CMU2212, MMU16LE). Users must specify a device ID and have appropriate access permissions.
// @Tags         env:dev, env:qa, env:sandbox, data
// @Produce      json
// @Security     JWTAuth
// @Param        deviceid  query     string  true   "Device ID to retrieve fault data for (integer ID or UUID format)"
// @Success      200       {object}  json.RawMessage                      "Device fault data retrieved successfully"
// @Failure      400       {object}  shared.BadRequestResponse            "Bad Request"
// @Failure      401       {object}  shared.UnauthorizedResponse          "Unauthorized"
// @Failure      500       {object}  shared.InternalServerErrorResponse   "Internal Server Error"
// @Router       /v3/data/fault [get]
func Handler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	// Get user info from jwt authorizer
	userPermissions, ok := userPermissionsFromContext(ctx)
	if !ok {
		logger.Error("Unable to retrieve user info from request context")
		response.CreateInternalErrorResponse(w)
		return
	}

	deviceOrigID, deviceUUID, err := parseRequest(r)
	if err != nil {
		logger.Infof("Unable to parse API request: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	connections, err := connect.GetConnections(ctx)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Validate user has access to device
	deviceUUID, err = validateDeviceAccess(connections.Postgres, userPermissions, deviceOrigID, deviceUUID)
	if err != nil {
		logger.Errorf("%v", err)
		// Check if this is an unauthorized access error or a database error
		if strings.Contains(err.Error(), "user does not have permission") {
			response.CreateUnauthorizedResponse(w)
		} else {
			response.CreateInternalErrorResponse(w)
		}
		return
	}

	pgDeviceDetail, err := getPGDeviceDetail(connections.Postgres, deviceUUID)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	allLogs, err := getBQLogs(connections.Bigquery, pgDeviceDetail.DeviceID, pgDeviceDetail.SoftWareGateWayID, pgDeviceDetail.OrganizationID)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	schema := getSchema(allLogs.FaultLogs.Model)
	var unified any
	switch schema {
	case "fault_mmu2_16leip.proto":
		deviceDetail := Logs_16leip_DeviceDetail{
			Model:                    allLogs.FaultLogs.DeviceModel,
			MonitorID:                pgDeviceDetail.MonitorID,
			MonitorName:              pgDeviceDetail.MonitorName,
			FirmwareVersion:          allLogs.FaultLogs.FirmwareReVision,
			FirmwareType:             allLogs.FaultLogs.FirmwareVersion,
			MonitorCommVersion:       allLogs.FaultLogs.MonitorCommVersion,
			RMSEngineFirmwareType:    pgDeviceDetail.RMSEngineFirmwareType,
			RMSEngineFirmwareVersion: pgDeviceDetail.RMSEngineFirmwareVersion,
			Logic24FirmwareType:      "",
			Logic24FirmwareVersion:   "",
			Manufacturer:             "EDI",
		}
		unified = ConvertSchemasTo16leip(
			allLogs.FaultLogs.PubsubTimestamp,
			schema,
			pgDeviceDetail.DeviceID,
			deviceDetail,
			allLogs.LogMonitorReset,
			allLogs.LogPreviousFail,
			allLogs.LogConfiguration,
			allLogs.LogACLineEvent,
			allLogs.LogFaultSignalSequence,
		)
	case "fault_ecl2010_fplus.proto":
		deviceDetail := &Logs_ECL2010_FPlus_LogData_DeviceDetail{
			Model:                    allLogs.FaultLogs.DeviceModel,
			MonitorId:                pgDeviceDetail.MonitorID,
			MonitorName:              pgDeviceDetail.MonitorName,
			FirmwareVersion:          allLogs.FaultLogs.FirmwareReVision,
			FirmwareType:             allLogs.FaultLogs.FirmwareVersion,
			MonitorCommVersion:       allLogs.FaultLogs.MonitorCommVersion,
			RmsEngineFirmwareType:    pgDeviceDetail.RMSEngineFirmwareType,
			RmsEngineFirmwareVersion: pgDeviceDetail.RMSEngineFirmwareVersion,
			Logic24FirmwareType:      "",
			Logic24FirmwareVersion:   "",
			Manufacturer:             "EDI",
		}

		unified = ConvertSchemasToECL2010FPlus(
			allLogs.FaultLogs.PubsubTimestamp,
			schema,
			pgDeviceDetail.DeviceID,
			deviceDetail,
			allLogs.LogMonitorReset,
			allLogs.LogPreviousFail,
			allLogs.LogConfiguration,
			allLogs.LogACLineEvent,
			allLogs.LogFaultSignalSequence,
		)
	case "fault_ecl2018_base.proto":
		deviceDetail := &Logs_ECL2018_Base_LogData_DeviceDetail{
			Model:                    allLogs.FaultLogs.DeviceModel,
			MonitorId:                pgDeviceDetail.MonitorID,
			MonitorName:              pgDeviceDetail.MonitorName,
			FirmwareVersion:          allLogs.FaultLogs.FirmwareReVision,
			FirmwareType:             allLogs.FaultLogs.FirmwareVersion,
			MonitorCommVersion:       allLogs.FaultLogs.MonitorCommVersion,
			RmsEngineFirmwareType:    pgDeviceDetail.RMSEngineFirmwareType,
			RmsEngineFirmwareVersion: pgDeviceDetail.RMSEngineFirmwareVersion,
			Logic24FirmwareType:      "",
			Logic24FirmwareVersion:   "",
			Manufacturer:             "EDI",
		}

		unified = ConvertSchemasToECL2018Base(
			allLogs.FaultLogs.PubsubTimestamp,
			schema,
			pgDeviceDetail.DeviceID,
			deviceDetail,
			allLogs.LogMonitorReset,
			allLogs.LogPreviousFail,
			allLogs.LogConfiguration,
			allLogs.LogACLineEvent,
			allLogs.LogFaultSignalSequence,
		)
	case "fault_kcl2018_base.proto":
		deviceDetail := &Logs_KCL2018_Base_LogData_DeviceDetail{
			Model:                    allLogs.FaultLogs.DeviceModel,
			MonitorId:                pgDeviceDetail.MonitorID,
			MonitorName:              pgDeviceDetail.MonitorName,
			FirmwareVersion:          allLogs.FaultLogs.FirmwareReVision,
			FirmwareType:             allLogs.FaultLogs.FirmwareVersion,
			MonitorCommVersion:       allLogs.FaultLogs.MonitorCommVersion,
			RmsEngineFirmwareType:    pgDeviceDetail.RMSEngineFirmwareType,
			RmsEngineFirmwareVersion: pgDeviceDetail.RMSEngineFirmwareVersion,
			Logic24FirmwareType:      "",
			Logic24FirmwareVersion:   "",
			Manufacturer:             "EDI",
		}

		unified = ConvertSchemasToKCL2018Base(
			allLogs.FaultLogs.PubsubTimestamp,
			schema,
			pgDeviceDetail.DeviceID,
			deviceDetail,
			allLogs.LogMonitorReset,
			allLogs.LogPreviousFail,
			allLogs.LogConfiguration,
			allLogs.LogACLineEvent,
			allLogs.LogFaultSignalSequence,
		)
	case "fault_cmu2212_base.proto":
		deviceDetail := &Logs_CMU2212_Base_LogData_DeviceDetail{
			Model:                    allLogs.FaultLogs.DeviceModel,
			MonitorId:                pgDeviceDetail.MonitorID,
			MonitorName:              pgDeviceDetail.MonitorName,
			FirmwareVersion:          allLogs.FaultLogs.FirmwareReVision,
			FirmwareType:             allLogs.FaultLogs.FirmwareVersion,
			MonitorCommVersion:       allLogs.FaultLogs.MonitorCommVersion,
			RmsEngineFirmwareType:    pgDeviceDetail.RMSEngineFirmwareType,
			RmsEngineFirmwareVersion: pgDeviceDetail.RMSEngineFirmwareVersion,
			Logic24FirmwareType:      "",
			Logic24FirmwareVersion:   "",
			Manufacturer:             "EDI",
		}

		unified = ConvertSchemasToCMU2212Base(
			allLogs.FaultLogs.PubsubTimestamp,
			schema,
			pgDeviceDetail.DeviceID,
			deviceDetail,
			allLogs.LogMonitorReset,
			allLogs.LogPreviousFail,
			allLogs.LogConfiguration,
			allLogs.LogACLineEvent,
			allLogs.LogFaultSignalSequence,
		)
	}

	// 1) Marshal struct to indented JSON
	raw, err := jsonMarshalIndentFn(unified, "", "  ")
	if err != nil {
		logger.Errorf("failed to marshal unified log: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// 2) Replace nulls with [] via helper
	clean, err := jsonCleanFn(raw)
	if err != nil {
		logger.Errorf("failed to clean JSON: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// 3) Wrap the cleaned JSON in a RawMessage so CreateSuccessResponse will inline it
	var data json.RawMessage = clean

	// 3) Send the cleaned JSON as success response payload
	response.CreateSuccessResponse(data, w)
}

var getPGDeviceDetail = func(pg connect.DatabaseExecutor, deviceUUID string) (*PGDeviceDetail, error) {
	// TODO: Need to fetch the date and loguuid from a postgress table DeviceLog to be used in the bigquery query
	query := `SELECT
  d.Id                          AS DeviceID,
  sg.Id                         AS SoftWareGateWayID,
  COALESCE(dm.MonitorId,0)      AS MonitorID,
  COALESCE(dm.MonitorName,'')   AS MonitorName,
  COALESCE(er.EngineVersion,'0')  AS RMSEngineFirmwareType,
  COALESCE(er.EngineRevision,'0') AS RMSEngineFirmwareVersion,
  o.Id                          AS OrganizationID
  FROM {{Device}} d
  LEFT JOIN {{DeviceMonitorName}} dm 
    ON dm.DeviceId = d.Id
  LEFT JOIN {{DeviceRMSEngine}} er 
    ON er.DeviceId = d.Id
  LEFT JOIN {{SoftwareGateway}} sg
    ON sg.id = d.softwaregatewayid
  LEFT JOIN {{Organization}} o
    ON o.id = sg.organizationid
  WHERE d.Id = $1;`
	pgDeviceDetail := &PGDeviceDetail{}
	err := pg.QueryRowStruct(pgDeviceDetail, query, deviceUUID)
	if err != nil {
		return nil, err
	}

	return pgDeviceDetail, nil
}

var getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID string, softwaregatewayID string, orgID string, filterDays ...int) (schemas.AllLogs, error) {
	// TODO: Need to fetch the date and loguuid from a postgress and use those as filters in the fault log table
	// Remove filterDays and days variables
	days := 90
	if len(filterDays) > 0 {
		days = filterDays[0]
	}

	query := `
	WITH fault_logs AS (
		SELECT *
		FROM {{FaultLogs}}
		WHERE
			pubsubtimestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL $1 DAY)
			AND deviceid                = $2
	)

	SELECT
		(SELECT AS STRUCT fl.* EXCEPT(loguuid,rawlogmessages),
		COALESCE(mr.devicemodel,pf.devicemodel,ae.devicemodel,fs.devicemodel,cc.devicemodel, "") as devicemodel,
		COALESCE(mr.header.monitorid,pf.header.monitorid,ae.header.monitorid,fs.header.monitorid,cc.header.monitorid, 0) as monitorid,
		COALESCE(mr.header.firmwareversion,pf.header.firmwareversion,ae.header.firmwareversion,fs.header.firmwareversion,cc.header.firmwareversion, "0") as firmwareversion,
		COALESCE(mr.header.firmwarerevision,pf.header.firmwarerevision,ae.header.firmwarerevision,fs.header.firmwarerevision,cc.header.firmwarerevision, "0") as firmwarerevision,
		COALESCE(mr.header.commversion,pf.header.commversion,ae.header.commversion,fs.header.commversion,cc.header.commversion, "0") as commversion,
		COALESCE(mr.header.model,pf.header.model,ae.header.model,fs.header.model,cc.header.model, 0) as model,   
		) AS fault_logs,

		(
			SELECT AS STRUCT
				mr.records as records,
		) AS monitor_reset,

		(
			SELECT AS STRUCT
				ARRAY(
					SELECT AS STRUCT
						r.datetime,
						r.fault,
						r.acline,
						r.t48vdcsignalbus,
						r.redenable,
						r.mccoilee,
						r.specialfunction1,
						r.specialfunction2,
						r.wdtmonitor,
						r.t24vdcinput,
						r.t12vdcinput,
						r.temperature,
						r.lsflashbit,
						r.faultstatus,
						r.channelgreenstatus,
						r.channelyellowstatus,
						r.channelredstatus,
						r.channelwalkstatus,
						r.channelgreenfieldcheckstatus,
						r.channelyellowfieldcheckstatus,
						r.channelredfieldcheckstatus,
						r.channelwalkfieldcheckstatus,
						r.channelgreenrecurrentpulsestatus,
						r.channelyellowrecurrentpulsestatus,
						r.channelredrecurrentpulsestatus,
						r.channelwalkrecurrentpulsestatus,
						r.channelgreenrmsvoltage,
						r.channelyellowrmsvoltage,
						r.channelredrmsvoltage,
						r.channelwalkrmsvoltage,
						r.hdspsignalvoltages,
						r.nextconflictingchannels,
						r.channelredcurrentstatus,
						r.channelyellowcurrentstatus,
						r.channelgreencurrentstatus,
						r.channelredrmscurrent,
						r.channelyellowrmscurrent,
						r.channelgreenrmscurrent
					FROM UNNEST(pf.records) AS r
				) as records,
		) AS previous_fail,

		(
			SELECT AS STRUCT
				ae.record as record,
				COALESCE(ae.voltagetype, 1) as voltagetype,
		) AS ac_line_event,

		(
			SELECT AS STRUCT
				fs.records AS records,
		) AS fault_signal_sequence,

		(
			SELECT AS STRUCT
				cc.record AS record,
		) AS configuration_change

	FROM fault_logs AS fl

	LEFT JOIN {{LogMonitorReset}} mr
		ON mr.loguuid            = fl.loguuid
	AND mr.deviceid               = fl.deviceid

	LEFT JOIN {{logPreviousFail}} pf
		ON pf.loguuid            = fl.loguuid
	AND pf.deviceid               = fl.deviceid

	LEFT JOIN {{logACLineEvent}} ae
		ON ae.loguuid            = fl.loguuid
	AND ae.deviceid               = fl.deviceid

	LEFT JOIN {{logFaultSignalSequence}} fs
		ON fs.loguuid            = fl.loguuid
	AND fs.deviceid               = fl.deviceid

	LEFT JOIN {{logConfiguration}} cc
		ON cc.loguuid            = fl.loguuid
	AND cc.deviceid               = fl.deviceid
	;`

	allLogs := schemas.AllLogs{}
	err := bq.QueryRowStruct(&allLogs, query, days, deviceID)

	if err != nil && err != sql.ErrNoRows {
		return allLogs, err
	}
	return allLogs, nil
}

var parseRequest = func(r *http.Request) (deviceOrigID int64, deviceUUID string, err error) {
	rQuery := r.URL.Query()
	deviceIdString := rQuery.Get("deviceid")
	if deviceIdString == "" {
		return deviceOrigID, deviceUUID, fmt.Errorf("%w: %v", ErrInvalidUrlQuery, rQuery)
	}

	// Parse as json.RawMessage to use ParseInt64OrUUID
	deviceIdRaw := json.RawMessage(fmt.Sprintf(`"%s"`, deviceIdString))
	deviceOrigID, deviceUUID, err = brokerShared.ParseInt64OrUUID(deviceIdRaw)
	if err != nil {
		logger.Infof("unable to convert query string parameter deviceid to int64: %v", err)
		return deviceOrigID, deviceUUID, fmt.Errorf("%w: %v", ErrConvertQueryParam, err)
	}

	return deviceOrigID, deviceUUID, nil
}

var getSchema = func(model int64) string {
	switch edihelper.MonitorModel(model) {
	case edihelper.Ecl2010:
		return "fault_ecl2010_fplus.proto"
	case edihelper.Ecl2018:
		return "fault_ecl2018_base.proto"
	case edihelper.Kcl2018:
		return "fault_kcl2018_base.proto"
	case edihelper.CMUip2212_hv:
		return "fault_cmu2212_base.proto"
	case edihelper.Mmu16le:
		return "fault_mmu2_16leip.proto"
	default:
		// return "Unknow Device"
		return "fault_mmu2_16leip.proto"
	}
}
