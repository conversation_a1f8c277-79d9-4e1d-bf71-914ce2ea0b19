package fault

import (
	"encoding/json"
	"strconv"
	"time"

	ediHelper "synapse-its.com/shared/devices/edi/helper"
	devicesHelper "synapse-its.com/shared/devices/helper"
	"synapse-its.com/shared/schemas"
)

// helper to translate slices
var translateSlice = func(bools []bool, key string) []string {
	tr := ediHelper.EtlTranslators[key]
	out := make([]string, len(bools))
	for j, v := range bools {
		out[j] = tr.String(v)
	}
	return out
}

// convertVoltageType converts the voltage type to a string
func convertVoltageType(voltageType int64) string {
	if voltageType == 1 {
		return "AC"
	}
	return "DC"
}

// convertInt64SliceToInt32 converts []int64 to []int32
func convertInt64SliceToInt32(slice []int64) []int32 {
	result := make([]int32, len(slice))
	for i, v := range slice {
		result[i] = int32(v)
	}
	return result
}

// convertInt64SliceToBool converts []int64 to []bool (non-zero = true)
func convertInt64SliceToBool(slice []int64) []bool {
	result := make([]bool, len(slice))
	for i, v := range slice {
		result[i] = v != 0
	}
	return result
}

// convertBoolToString converts a bool to a string
func convertBoolToString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

// invertBools inverts the boolean values in a slice
func invertBools(bools []bool) []bool {
	for i := range bools {
		bools[i] = !bools[i]
	}
	return bools
}

// ConvertSchemasToFault builds a UnifiedLogData from BigQuery schemas.
func ConvertSchemasTo16leip(
	messageTime time.Time,
	schemaName string,
	deviceID string,
	detail Logs_16leip_DeviceDetail,

	monitorReset schemas.LogMonitorReset,
	prevFail schemas.LogPreviousFail,
	config schemas.LogConfiguration,
	acEvent schemas.LogACLineEvent,
	faultSeq schemas.LogFaultSignalSequence,
) Logs_16leip_UnifiedLogData {
	// 1) Map LogMonitorReset
	mr := make([]Logs_16leip_LogMonitorResetRecord, len(monitorReset.Records))
	for i, r := range monitorReset.Records {
		mr[i] = Logs_16leip_LogMonitorResetRecord{
			DateTime:  r.EventTimestamp,
			ResetType: r.ResetType,
		}
	}

	// 2) Map LogPreviousFail
	pf := make([]Logs_16leip_LogPreviousFailRecord, len(prevFail.Records))
	for i, r := range prevFail.Records {
		pf[i] = Logs_16leip_LogPreviousFailRecord{
			DateTime:                          r.DateTime,
			Fault:                             r.Fault,
			ACLine:                            r.ACLine,
			T48VDCSignalBus:                   r.T48VDCSignalBus,
			RedEnable:                         r.RedEnable,
			MCCoilEE:                          r.MCCoilEE,
			SpecialFunction1:                  r.SpecialFunction1,
			SpecialFunction2:                  r.SpecialFunction2,
			WDTMonitor:                        r.WDTMonitor,
			T24VDCInput:                       r.T24VDCInput,
			Temperature:                       strconv.Itoa(int(r.Temperature)) + " F",
			LsFlashBit:                        ediHelper.EtlTranslators["LsFlashBit"].String(r.LsFlashBit),
			FaultStatus:                       translateSlice(r.FaultStatus, "FaultStatus"),
			ChannelGreenStatus:                translateSlice(r.ChannelGreenStatus, "ChannelGreenStatus"),
			ChannelYellowStatus:               translateSlice(r.ChannelYellowStatus, "ChannelYellowStatus"),
			ChannelRedStatus:                  translateSlice(r.ChannelRedStatus, "ChannelRedStatus"),
			ChannelWalkStatus:                 translateSlice(r.ChannelWalkStatus, "ChannelWalkStatus"),
			ChannelGreenFieldCheckStatus:      translateSlice(r.ChannelGreenFieldCheckStatus, "ChannelGreenFieldCheckStatus"),
			ChannelYellowFieldCheckStatus:     translateSlice(r.ChannelYellowFieldCheckStatus, "ChannelYellowFieldCheckStatus"),
			ChannelRedFieldCheckStatus:        translateSlice(r.ChannelRedFieldCheckStatus, "ChannelRedFieldCheckStatus"),
			ChannelWalkFieldCheckStatus:       translateSlice(r.ChannelWalkFieldCheckStatus, "ChannelWalkFieldCheckStatus"),
			ChannelGreenRecurrentPulseStatus:  translateSlice(r.ChannelGreenRecurrentPulseStatus, "ChannelGreenRecurrentPulseStatus"),
			ChannelYellowRecurrentPulseStatus: translateSlice(r.ChannelYellowRecurrentPulseStatus, "ChannelYellowRecurrentPulseStatus"),
			ChannelRedRecurrentPulseStatus:    translateSlice(r.ChannelRedRecurrentPulseStatus, "ChannelRedRecurrentPulseStatus"),
			ChannelWalkRecurrentPulseStatus:   translateSlice(r.ChannelWalkRecurrentPulseStatus, "ChannelWalkRecurrentPulseStatus"),
			ChannelGreenRMSVoltage:            r.ChannelGreenRmsVoltage,
			ChannelYellowRMSVoltage:           r.ChannelYellowRmsVoltage,
			ChannelRedRMSVoltage:              r.ChannelRedRmsVoltage,
			ChannelWalkRMSVoltage:             r.ChannelWalkRmsVoltage,
			NextConflictingChannels:           translateSlice(r.NextConflictingChannels, "NextConflictingChannels"),
			ChannelRedCurrentStatus:           translateSlice(r.ChannelRedCurrentStatus, "ChannelRedCurrentStatus"),
			ChannelYellowCurrentStatus:        translateSlice(r.ChannelYellowCurrentStatus, "ChannelYellowCurrentStatus"),
			ChannelGreenCurrentStatus:         translateSlice(r.ChannelGreenCurrentStatus, "ChannelGreenCurrentStatus"),
			ChannelRedRMSCurrent:              r.ChannelRedRmsCurrent,
			ChannelYellowRMSCurrent:           r.ChannelYellowRmsCurrent,
			ChannelGreenRMSCurrent:            r.ChannelGreenRmsCurrent,
		}
	}

	// 3) Map LogConfigurationChange with translation
	cc := make([]Logs_16leip_ConfigurationChangeLogRecord, len(config.Record))
	for i, r := range config.Record {
		// translate scalar bools
		translations, _ := devicesHelper.TranslateConfig(&r, ediHelper.EtlTranslators)

		// get the first element of flashing yellow arrows if it exists
		// FlashingYellowArrows is a slice of strings for one device but just a string for this device
		// this is why we can't have nice things
		flashingYellowArrows := ""
		if len(r.FlashingYellowArrows) > 0 {
			flashingYellowArrows = r.FlashingYellowArrows[0]
		}

		cc[i] = Logs_16leip_ConfigurationChangeLogRecord{
			DateTime:        r.DateTime,
			Ch1Permissives:  r.Ch01Permissives,
			Ch2Permissives:  r.Ch02Permissives,
			Ch3Permissives:  r.Ch03Permissives,
			Ch4Permissives:  r.Ch04Permissives,
			Ch5Permissives:  r.Ch05Permissives,
			Ch6Permissives:  r.Ch06Permissives,
			Ch7Permissives:  r.Ch07Permissives,
			Ch8Permissives:  r.Ch08Permissives,
			Ch9Permissives:  r.Ch09Permissives,
			Ch10Permissives: r.Ch10Permissives,
			Ch11Permissives: r.Ch11Permissives,
			Ch12Permissives: r.Ch12Permissives,
			Ch13Permissives: r.Ch13Permissives,
			Ch14Permissives: r.Ch14Permissives,
			Ch15Permissives: r.Ch15Permissives,

			RedFailEnable:                   translateSlice(r.RedFailEnable, "RedFailEnable"),
			GreenYellowDualEnable:           translateSlice(r.GreenYellowDualEnable, "GreenYellowDualEnable"),
			YellowRedDualEnable:             translateSlice(r.YellowRedDualEnable, "YellowRedDualEnable"),
			GreenRedDualEnable:              translateSlice(r.GreenRedDualEnable, "GreenRedDualEnable"),
			MinimumYellowClearanceEnable:    translateSlice(invertBools(r.MinimumYellowClearanceEnable), "MinimumYellowClearanceEnable"),
			MinimumYellowRedClearanceEnable: translateSlice(invertBools(r.MinimumYellowRedClearanceEnable), "MinimumYellowRedClearanceEnable"),
			FieldCheckEnableGreen:           translateSlice(r.FieldCheckEnableGreen, "FieldCheckEnableGreen"),
			FieldCheckEnableYellow:          translateSlice(r.FieldCheckEnableYellow, "FieldCheckEnableYellow"),
			FieldCheckEnableRed:             translateSlice(r.FieldCheckEnableRed, "FieldCheckEnableRed"),
			YellowEnable:                    translateSlice(r.YellowEnable, "YellowEnable"),
			HdspChannelEnable:               translateSlice(r.HdspChannelEnable, "HdspChannelEnable"),

			WalkEnableTs1:        translations["WalkEnableTs1"],
			RedFaultTiming:       r.RedFaultTiming,
			RecurrentPulse:       translations["RecurrentPulse"],
			WatchdogTiming:       r.WatchdogTiming,
			WatchdogEnableSwitch: translations["WatchdogEnableSwitch"],
			ProgramCardMemory:    translations["ProgramCardMemory"],

			GYEnable:         translations["GYEnable"],
			MinimumFlashTime: r.MinimumFlashTime,
			CvmLatchEnable:   translations["CvmLatchEnable"],
			LogCvmFaults:     translations["LogCvmFaults"],

			X24VIiInputThreshold: r.X24VIiInputThreshold,
			X24VLatchEnable:      translations["X24VLatchEnable"],
			X24VoltInhibit:       translations["X24VoltInhibit"],
			Port1Disable:         translations["Port1Disable"],

			TypeMode:           r.TypeMode,
			LEDGuardThresholds: translations["LEDGuardThresholds"],
			ForceType16Mode:    translations["ForceType16Mode"],
			Type12WithSdlcMode: translations["Type12WithSdlcMode"],

			VmCvm24V3XDayLatch:          translations["VmCvm24V3XDayLatch"],
			RedFailEnabledBySSM:         translations["RedFailEnabledBySSM"],
			DualIndicationFaultTiming:   r.DualIndicationFaultTiming,
			WDTErrorClearOnPU:           translations["WDTErrorClearOnPU"],
			MinimumFlash:                translations["MinimumFlash"],
			ConfigChangeFault:           translations["ConfigChangeFault"],
			RedCableFault:               translations["RedCableFault"],
			AcLineBrownout:              r.AcLineBrownout,
			PinEEPolarity:               r.PinEEPolarity,
			FlashingYellowArrows:        flashingYellowArrows,
			FyaRedAndYellowEnable:       r.FyaRedAndYellowEnable,
			FyaRedAndGreenDisable:       r.FyaRedAndGreenDisable,
			FyaYellowTrapDetection:      translations["FyaYellowTrapDetection"],
			FYAFlashRateFault:           translations["FYAFlashRateFault"],
			FyaFlashRateDetection:       translations["FyaFlashRateDetection"],
			Pplt5Suppression:            r.Pplt5Suppression,
			CheckValue:                  r.CheckValue,
			ChangeSource:                r.ChangeSource,
			RedVirtualChannel:           convertVS(r.RedVirtualChannel),
			YellowVirtualChannel:        convertVS(r.YellowVirtualChannel),
			GreenVirtualChannel:         convertVS(r.GreenVirtualChannel),
			CurrentSenseRedEnabled:      translateSlice(r.CurrentSenseRedEnabled, "CurrentSenseRedEnabled"),
			CurrentSenseYellowEnabled:   translateSlice(r.CurrentSenseYellowEnabled, "CurrentSenseYellowEnabled"),
			CurrentSenseGreenEnabled:    translateSlice(r.CurrentSenseGreenEnabled, "CurrentSenseGreenEnabled"),
			CurrentSenseRedThreshold:    r.CurrentSenseRedThreshold,
			CurrentSenseYellowThreshold: r.CurrentSenseYellowThreshold,
			CurrentSenseGreenThreshold:  r.CurrentSenseGreenThreshold,
			DarkChannelX01:              r.DarkChannelX01,
			DarkChannelX02:              r.DarkChannelX02,
			DarkChannelX03:              r.DarkChannelX03,
			DarkChannelX04:              r.DarkChannelX04,
		}
	}

	ae := make([]Logs_16leip_AcLineEventRecord, len(acEvent.Record))
	for i, r := range acEvent.Record {
		ae[i] = Logs_16leip_AcLineEventRecord{
			EventType:       r.EventType,
			DateTime:        r.DateTime,
			LineVoltageRms:  r.LineVoltageRms,
			LineFrequencyHz: r.LineFrequencyHz,
		}
	}

	// 5) Map LogFaultSignalSequence
	fsRecs := make([]Logs_16leip_FaultSignalSequenceRecord, len(faultSeq.Records))
	for i, seqRec := range faultSeq.Records {
		var buffers []Logs_16leip_FaultSignalBuffer
		// For each TraceBuffer (seqRec.Records), append one FaultSignalBuffer per channel bit
		for _, tb := range seqRec.Records {
			buffers = append(buffers, Logs_16leip_FaultSignalBuffer{
				Timestamp: tb.Timestamp,
				Red:       tb.Reds,
				Yellow:    tb.Yellows,
				Green:     tb.Greens,
				Walk:      tb.Walks,
				EeSfRe:    tb.EE_SF_RE,
				AcVoltage: tb.AcVoltage,
			})
		}
		fsRecs[i] = Logs_16leip_FaultSignalSequenceRecord{
			FaultType: seqRec.FaultType,
			Buffers:   buffers,
		}
	}

	return Logs_16leip_UnifiedLogData{
		MessageTime:  messageTime,
		DeviceID:     deviceID,
		Schema:       schemaName,
		DeviceDetail: detail,

		LogMonitorReset:        Logs_16leip_LogMonitorReset{Record: mr},
		LogPreviousFail:        Logs_16leip_LogPreviousFail{Record: pf},
		LogConfigurationChange: Logs_16leip_LogConfigurationChange{Record: cc},
		LogAcLineEvent:         Logs_16leip_LogAcLineEvent{Record: ae, VoltageType: convertVoltageType(acEvent.VoltageType)},
		LogFaultSignalSequence: Logs_16leip_LogFaultSignalSequence{Record: fsRecs},
	}
}

// ReplaceNullsInJSON takes any JSON blob and replaces all JSON nulls
// with empty slices (and leaves other values intact), then pretty-prints.
func ReplaceNullsInJSON(input []byte) ([]byte, error) {
	var data interface{}
	if err := json.Unmarshal(input, &data); err != nil {
		return nil, err
	}

	cleaned := replaceNulls(data)
	return json.MarshalIndent(cleaned, "", "  ")
}

// replaceNulls recursively replaces JSON nulls with empty slices
func replaceNulls(v interface{}) interface{} {
	switch x := v.(type) {
	case map[string]interface{}:
		for k, val := range x {
			if val == nil {
				x[k] = []interface{}{}
			} else {
				x[k] = replaceNulls(val)
			}
		}
		return x

	case []interface{}:
		for i, item := range x {
			if item == nil {
				x[i] = []interface{}{}
			} else {
				x[i] = replaceNulls(item)
			}
		}
		return x

	default:
		return x
	}
}

func convertVS(src []schemas.VirtualSetting) []Logs_16leip_VirtualSetting {
	dst := make([]Logs_16leip_VirtualSetting, len(src))
	for i, v := range src {
		dst[i] = Logs_16leip_VirtualSetting{
			Color:         v.Color,
			Enabled:       v.Enabled,
			SourceChannel: v.SourceChannel,
			SourceColor:   v.SourceColor,
		}
	}
	return dst
}

// ConvertSchemasToECL2010FPlus builds the JSON structs for an ECL2010 F-Plus log.
func ConvertSchemasToECL2010FPlus(
	messageTime time.Time,
	schemaName string,
	deviceID string,
	detail *Logs_ECL2010_FPlus_LogData_DeviceDetail,

	monitor schemas.LogMonitorReset,
	prevFail schemas.LogPreviousFail,
	config schemas.LogConfiguration,
	acLine schemas.LogACLineEvent,
	faultSeq schemas.LogFaultSignalSequence,
) *Logs_ECL2010_FPlus_LogData {
	out := &Logs_ECL2010_FPlus_LogData{
		MessageTime:  messageTime,
		DeviceId:     deviceID,
		Schema:       schemaName,
		DeviceDetail: detail,
	}

	// — LogMonitorReset
	mr := make([]*Logs_ECL2010_FPlus_LogData_LogMonitorReset_LogMonitorResetRecord, len(monitor.Records))
	for i, r := range monitor.Records {
		mr[i] = &Logs_ECL2010_FPlus_LogData_LogMonitorReset_LogMonitorResetRecord{
			Datetime: r.EventTimestamp,
		}
	}
	out.LogMonitorReset = &Logs_ECL2010_FPlus_LogData_LogMonitorReset{Record: mr}

	// — LogPreviousFail
	pf := make([]*Logs_ECL2010_FPlus_LogData_LogPreviousFail_LogPreviousFailRecord, len(prevFail.Records))
	for i, r := range prevFail.Records {
		pf[i] = &Logs_ECL2010_FPlus_LogData_LogPreviousFail_LogPreviousFailRecord{
			Datetime:                          r.DateTime,
			Fault:                             r.Fault,
			AcLine:                            r.ACLine,
			X48VdcSignalBus:                   r.T48VDCSignalBus,
			RedEnable:                         r.RedEnable,
			McCoilEe:                          r.MCCoilEE,
			SpecialFunction1:                  r.SpecialFunction1,
			SpecialFunction2:                  r.SpecialFunction2,
			WdtMonitor:                        r.WDTMonitor,
			X24VdcInput:                       r.T24VDCInput,
			Temperature:                       strconv.Itoa(int(r.Temperature)) + " F",
			FaultStatus:                       translateSlice(r.FaultStatus, "FaultStatus"),
			ChannelGreenStatus:                translateSlice(r.ChannelGreenStatus, "ChannelGreenStatus"),
			ChannelYellowStatus:               translateSlice(r.ChannelYellowStatus, "ChannelYellowStatus"),
			ChannelRedStatus:                  translateSlice(r.ChannelRedStatus, "ChannelRedStatus"),
			ChannelWalkStatus:                 translateSlice(r.ChannelWalkStatus, "ChannelWalkStatus"),
			ChannelGreenFieldCheckStatus:      translateSlice(r.ChannelGreenFieldCheckStatus, "ChannelGreenFieldCheckStatus"),
			ChannelYellowFieldCheckStatus:     translateSlice(r.ChannelYellowFieldCheckStatus, "ChannelYellowFieldCheckStatus"),
			ChannelRedFieldCheckStatus:        translateSlice(r.ChannelRedFieldCheckStatus, "ChannelRedFieldCheckStatus"),
			ChannelWalkFieldCheckStatus:       translateSlice(r.ChannelWalkFieldCheckStatus, "ChannelWalkFieldCheckStatus"),
			ChannelGreenRecurrentPulseStatus:  translateSlice(r.ChannelGreenRecurrentPulseStatus, "ChannelGreenRecurrentPulseStatus"),
			ChannelYellowRecurrentPulseStatus: translateSlice(r.ChannelYellowRecurrentPulseStatus, "ChannelYellowRecurrentPulseStatus"),
			ChannelRedRecurrentPulseStatus:    translateSlice(r.ChannelRedRecurrentPulseStatus, "ChannelRedRecurrentPulseStatus"),
			ChannelWalkRecurrentPulseStatus:   translateSlice(r.ChannelWalkRecurrentPulseStatus, "ChannelWalkRecurrentPulseStatus"),
			ChannelGreenRmsVoltage:            r.ChannelGreenRmsVoltage,
			ChannelYellowRmsVoltage:           r.ChannelYellowRmsVoltage,
			ChannelRedRmsVoltage:              r.ChannelRedRmsVoltage,
			ChannelWalkRmsVoltage:             r.ChannelWalkRmsVoltage,
		}
	}
	out.LogPreviousFail = &Logs_ECL2010_FPlus_LogData_LogPreviousFail{Record: pf}

	// — LogConfigurationChange
	cc := make([]*Logs_ECL2010_FPlus_LogData_LogConfigurationChange_ConfigurationChangeLogRecord, len(config.Record))
	for i, r := range config.Record {
		translations, _ := devicesHelper.TranslateConfig(&r, ediHelper.EtlTranslators)
		cc[i] = &Logs_ECL2010_FPlus_LogData_LogConfigurationChange_ConfigurationChangeLogRecord{
			Datetime:                     r.DateTime,
			Ch_1Permissives:              r.Ch01Permissives,
			Ch_2Permissives:              r.Ch02Permissives,
			Ch_3Permissives:              r.Ch03Permissives,
			Ch_4Permissives:              r.Ch04Permissives,
			Ch_5Permissives:              r.Ch05Permissives,
			Ch_6Permissives:              r.Ch06Permissives,
			Ch_7Permissives:              r.Ch07Permissives,
			Ch_8Permissives:              r.Ch08Permissives,
			Ch_9Permissives:              r.Ch09Permissives,
			Ch_10Permissives:             r.Ch10Permissives,
			Ch_11Permissives:             r.Ch11Permissives,
			Ch_12Permissives:             r.Ch12Permissives,
			Ch_13Permissives:             r.Ch13Permissives,
			Ch_14Permissives:             r.Ch14Permissives,
			Ch_15Permissives:             r.Ch15Permissives,
			RedFailEnable:                translateSlice(r.RedFailEnable, "RedFailEnable"),
			GreenYellowDualEnable:        translateSlice(r.GreenYellowDualEnable, "GreenYellowDualEnable"),
			YellowRedDualEnable:          translateSlice(r.YellowRedDualEnable, "YellowRedDualEnable"),
			GreenRedDualEnable:           translateSlice(r.GreenRedDualEnable, "GreenRedDualEnable"),
			MinimumYellowClearanceEnable: translateSlice(r.MinimumYellowClearanceEnable, "MinimumYellowClearanceEnable"),
			YellowDisable:                translateSlice(r.YellowEnable, "YellowEnable"),
			HdspChannelEnable:            translateSlice(r.HdspChannelEnable, "HdspChannelEnable"),
			RedFaultTiming:               r.RedFaultTiming,
			RecurrentPulse:               translations["RecurrentPulse"],
			WatchdogTiming:               r.WatchdogTiming,
			WatchdogEnable:               translations["WatchdogEnableSwitch"],
			GYEnable:                     translations["GYEnable"],
			LedGuardThresholds:           translations["LEDGuardThresholds"],
			RedFailEnabledBySSM:          translations["RedFailEnabledBySSM"],
			DualIndicationFaultTiming:    r.DualIndicationFaultTiming,
			WDTErrorClearOnPU:            translations["WDTErrorClearOnPU"],
			MinimumFlash:                 translations["MinimumFlash"],
			ConfigChangeFault:            translations["ConfigChangeFault"],
			RedCableFault:                translations["RedCableFault"],
			AcLineBrownout:               r.AcLineBrownout,
			PinEEPolarity:                r.PinEEPolarity,
			FlashingYellowArrows:         r.FlashingYellowArrows,
			FyaFlashRateFault:            translations["FYAFlashRateFault"],
			CheckValue:                   r.CheckValue + "\n",
		}
	}
	out.LogConfigurationChange = &Logs_ECL2010_FPlus_LogData_LogConfigurationChange{Record: cc}

	// — LogACLineEvent
	ae := &Logs_ECL2010_FPlus_LogData_LogACLineEvent{
		VoltageType: convertVoltageType(acLine.VoltageType),
	}
	ae.Events = make([]*Logs_ECL2010_FPlus_LogData_LogACLineEvent_LogACLineEventRecord, len(acLine.Record))
	for i, r := range acLine.Record {
		ae.Events[i] = &Logs_ECL2010_FPlus_LogData_LogACLineEvent_LogACLineEventRecord{
			EventType:       r.EventType,
			Datetime:        r.DateTime,
			LineVoltageVrms: r.LineVoltageRms,
			LineFrequencyHz: r.LineFrequencyHz,
		}
	}
	out.LogAcLineEvent = ae

	// — LogFaultSignalSequence
	// — LogFaultSignalSequence (updated for new nested schema)
	fsOut := make([]*Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord, len(faultSeq.Records))
	for i, seqRec := range faultSeq.Records {
		buf := make([]*Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer, len(seqRec.Records))
		for j, tb := range seqRec.Records {
			buf[j] = &Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer{
				Timestamp: tb.Timestamp,
				Red:       tb.Reds,
				Yellow:    tb.Yellows,
				Green:     tb.Greens,
				Walk:      tb.Walks,
				EeSfRe:    tb.EE_SF_RE,
				AcVoltage: tb.AcVoltage,
			}
		}

		fsOut[i] = &Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord{
			FaultType: seqRec.FaultType,
			Buffers:   buf,
		}
	}
	out.LogFaultSignalSequence = &Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence{Record: fsOut}

	return out
}

// ConvertSchemasToECL2018Base builds the JSON structs for an ECL2018 Base log.
func ConvertSchemasToECL2018Base(
	messageTime time.Time,
	schemaName string,
	deviceID string,
	detail *Logs_ECL2018_Base_LogData_DeviceDetail,

	monitor schemas.LogMonitorReset,
	prevFail schemas.LogPreviousFail,
	config schemas.LogConfiguration,
	acLine schemas.LogACLineEvent,
	faultSeq schemas.LogFaultSignalSequence,
) *Logs_ECL2018_Base_LogData {
	out := &Logs_ECL2018_Base_LogData{
		MessageTime:  messageTime,
		DeviceId:     deviceID,
		Schema:       schemaName,
		DeviceDetail: detail,
	}

	// — LogMonitorReset
	mr := make([]*Logs_ECL2018_Base_LogData_LogMonitorReset_LogMonitorResetRecord, len(monitor.Records))
	for i, r := range monitor.Records {
		mr[i] = &Logs_ECL2018_Base_LogData_LogMonitorReset_LogMonitorResetRecord{
			Datetime: r.EventTimestamp,
		}
	}
	out.LogMonitorReset = &Logs_ECL2018_Base_LogData_LogMonitorReset{Record: mr}

	// — LogPreviousFail
	pf := make([]*Logs_ECL2018_Base_LogData_LogPreviousFail_LogPreviousFailRecord, len(prevFail.Records))
	for i, r := range prevFail.Records {
		pf[i] = &Logs_ECL2018_Base_LogData_LogPreviousFail_LogPreviousFailRecord{
			Datetime:         r.DateTime,
			Fault:            r.Fault,
			AcLine:           r.ACLine,
			X48VdcSignalBus:  r.T48VDCSignalBus,
			RedEnable:        r.RedEnable,
			McCoilEe:         r.MCCoilEE,
			SpecialFunction1: r.SpecialFunction1,
			SpecialFunction2: r.SpecialFunction2,
			WdtMonitor:       r.WDTMonitor,
			X24VdcInput:                       r.T24VDCInput,
			X12VdcInput:                       r.T12VDCInput,
			Temperature:                       r.Temperature,
			LsFlashBit:                        convertBoolToString(r.LsFlashBit),
			FaultStatus:                       translateSlice(r.FaultStatus, "FaultStatus"),
			ChannelGreenStatus:                translateSlice(r.ChannelGreenStatus, "ChannelGreenStatus"),
			ChannelYellowStatus:               translateSlice(r.ChannelYellowStatus, "ChannelYellowStatus"),
			ChannelRedStatus:                  translateSlice(r.ChannelRedStatus, "ChannelRedStatus"),
			ChannelWalkStatus:                 translateSlice(r.ChannelWalkStatus, "ChannelWalkStatus"),
			ChannelGreenFieldCheckStatus:      translateSlice(r.ChannelGreenFieldCheckStatus, "ChannelGreenFieldCheckStatus"),
			ChannelYellowFieldCheckStatus:     translateSlice(r.ChannelYellowFieldCheckStatus, "ChannelYellowFieldCheckStatus"),
			ChannelRedFieldCheckStatus:        translateSlice(r.ChannelRedFieldCheckStatus, "ChannelRedFieldCheckStatus"),
			ChannelWalkFieldCheckStatus:       translateSlice(r.ChannelWalkFieldCheckStatus, "ChannelWalkFieldCheckStatus"),
			ChannelGreenRecurrentPulseStatus:  translateSlice(r.ChannelGreenRecurrentPulseStatus, "ChannelGreenRecurrentPulseStatus"),
			ChannelYellowRecurrentPulseStatus: translateSlice(r.ChannelYellowRecurrentPulseStatus, "ChannelYellowRecurrentPulseStatus"),
			ChannelRedRecurrentPulseStatus:    translateSlice(r.ChannelRedRecurrentPulseStatus, "ChannelRedRecurrentPulseStatus"),
			ChannelWalkRecurrentPulseStatus:   translateSlice(r.ChannelWalkRecurrentPulseStatus, "ChannelWalkRecurrentPulseStatus"),
			ChannelGreenRmsVoltage:            convertInt64SliceToInt32(r.ChannelGreenRmsVoltage),
			ChannelYellowRmsVoltage:           convertInt64SliceToInt32(r.ChannelYellowRmsVoltage),
			ChannelRedRmsVoltage:              convertInt64SliceToInt32(r.ChannelRedRmsVoltage),
			ChannelWalkRmsVoltage:             convertInt64SliceToInt32(r.ChannelWalkRmsVoltage),
			HdspSignalVoltages:                r.HDSPSignalVoltages,
			NextConflictingChannels:    r.NextConflictingChannels,
			ChannelRedCurrentStatus:    r.ChannelRedCurrentStatus,
			ChannelYellowCurrentStatus: r.ChannelYellowCurrentStatus,
			ChannelGreenCurrentStatus:  r.ChannelGreenCurrentStatus,
			ChannelRedRmsCurrent:       convertInt64SliceToInt32(r.ChannelRedRmsCurrent),
			ChannelYellowRmsCurrent:    convertInt64SliceToInt32(r.ChannelYellowRmsCurrent),
			ChannelGreenRmsCurrent:     convertInt64SliceToInt32(r.ChannelGreenRmsCurrent),
		}
	}
	out.LogPreviousFail = &Logs_ECL2018_Base_LogData_LogPreviousFail{Record: pf}

	// — LogConfigurationChange
	cc := make([]*Logs_ECL2018_Base_LogData_LogConfigurationChange_LogConfigurationChangeRecord, len(config.Record))
	for i, r := range config.Record {
		// Convert configuration fields from the basic record structure
		conflictMap := make([]int32, 54) // Default 54 elements for conflict map
		for j := 0; j < 54 && j < len(r.Ch01Permissives); j++ {
			conflictMap[j] = int32(j) // Simple mapping for now
		}

		cc[i] = &Logs_ECL2018_Base_LogData_LogConfigurationChange_LogConfigurationChangeRecord{
			Datetime:           r.DateTime,
			ConflictMap:        conflictMap,
			MinimumGreen:       0, // Default values for ECL2018
			YellowClearance:    0,
			RedClearance:       0,
			MaximumInitial:     0,
			MinimumInitial:     0,
			ExtensionIncrement: 0,
			MaximumExtension:   0,
			CheckValue:         r.CheckValue,

			// Map individual channel permissives
			Ch01Permissives: r.Ch01Permissives,
			Ch02Permissives: r.Ch02Permissives,
			Ch03Permissives: r.Ch03Permissives,
			Ch04Permissives: r.Ch04Permissives,
			Ch05Permissives: r.Ch05Permissives,
			Ch06Permissives: r.Ch06Permissives,
			Ch07Permissives: r.Ch07Permissives,
			Ch08Permissives: r.Ch08Permissives,
			Ch09Permissives: r.Ch09Permissives,
			Ch10Permissives: r.Ch10Permissives,
			Ch11Permissives: r.Ch11Permissives,
			Ch12Permissives: r.Ch12Permissives,
			Ch13Permissives: r.Ch13Permissives,
			Ch14Permissives: r.Ch14Permissives,
			Ch15Permissives: r.Ch15Permissives,
			Ch16Permissives: r.Ch16Permissives,
			Ch17Permissives: r.Ch17Permissives,
			Ch18Permissives: r.Ch18Permissives,

			// Map channel enable bitfields (convert bool arrays to string arrays)
			RedFailEnable:                   translateSlice(r.RedFailEnable, "enable"),
			GreenYellowDualEnable:           translateSlice(r.GreenYellowDualEnable, "enable"),
			YellowRedDualEnable:             translateSlice(r.YellowRedDualEnable, "enable"),
			GreenRedDualEnable:              translateSlice(r.GreenRedDualEnable, "enable"),
			MinimumYellowClearanceEnable:    translateSlice(r.MinimumYellowClearanceEnable, "enable"),
			MinimumYellowRedClearanceEnable: translateSlice(r.MinimumYellowRedClearanceEnable, "enable"),
			YellowEnable:                    translateSlice(r.YellowEnable, "enable"),

			// Map configuration option flags and settings (convert bool to string)
			WatchdogEnableSwitch: ediHelper.EtlTranslators["enable"].String(r.WatchdogEnableSwitch),
			RecurrentPulse:       ediHelper.EtlTranslators["enable"].String(r.RecurrentPulse),
			RedFaultTiming:       r.RedFaultTiming,
			WatchdogTiming:       r.WatchdogTiming,
			GYEnable:             ediHelper.EtlTranslators["enable"].String(r.GYEnable),
			LEDguardThresholds:   ediHelper.EtlTranslators["enable"].String(r.LEDGuardThresholds),
			RedFailEnabledbySSM:  ediHelper.EtlTranslators["enable"].String(r.RedFailEnabledBySSM),
			FlashingYellowArrows: r.FlashingYellowArrows,
			MinimumFlashTime:     r.MinimumFlashTime,
		}
	}
	out.LogConfigurationChange = &Logs_ECL2018_Base_LogData_LogConfigurationChange{Record: cc}

	// — LogACLineEvent
	ae := &Logs_ECL2018_Base_LogData_LogACLineEvent{
		VoltageType: convertVoltageType(acLine.VoltageType),
	}
	ae.Events = make([]*Logs_ECL2018_Base_LogData_LogACLineEvent_LogACLineEventRecord, len(acLine.Record))
	for i, r := range acLine.Record {
		ae.Events[i] = &Logs_ECL2018_Base_LogData_LogACLineEvent_LogACLineEventRecord{
			EventType:       r.EventType,
			Datetime:        r.DateTime,
			LineVoltageVrms: r.LineVoltageRms,
			LineFrequencyHz: r.LineFrequencyHz,
		}
	}
	out.LogAcLineEvent = ae

	// — LogFaultSignalSequence
	fsOut := make([]*Logs_ECL2018_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord, len(faultSeq.Records))
	for i, seqRec := range faultSeq.Records {
		buf := make([]*Logs_ECL2018_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer, len(seqRec.Records))
		for j, tb := range seqRec.Records {
			buf[j] = &Logs_ECL2018_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer{
				BufferRawBytes: tb.BufferRawBytes,
				Timestamp:      tb.Timestamp,
				Red:            tb.Reds,
				Yellow:         tb.Yellows,
				Green:          tb.Greens,
				Walk:           tb.Walks,
				EeSfRe:         tb.EE_SF_RE,
				AcVoltage:      tb.AcVoltage,
			}
		}
		fsOut[i] = &Logs_ECL2018_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord{
			FaultType: seqRec.FaultType,
			Buffers:   buf,
		}
	}
	out.LogFaultSignalSequence = &Logs_ECL2018_Base_LogData_LogFaultSignalSequence{Record: fsOut}

	return out
}

// ConvertSchemasToCMU2212Base builds the JSON structs for a CMU2212-Base log.
func ConvertSchemasToCMU2212Base(
	messageTime time.Time,
	schemaName string,
	deviceID string,
	detail *Logs_CMU2212_Base_LogData_DeviceDetail,

	monitor schemas.LogMonitorReset,
	prevFail schemas.LogPreviousFail,
	config schemas.LogConfiguration,
	acLine schemas.LogACLineEvent,
	faultSeq schemas.LogFaultSignalSequence,
) *Logs_CMU2212_Base_LogData {
	out := &Logs_CMU2212_Base_LogData{
		MessageTime:  messageTime,
		DeviceId:     deviceID,
		Schema:       schemaName,
		DeviceDetail: detail,
	}

	// — LogMonitorReset
	mr := make([]*Logs_CMU2212_Base_LogData_LogMonitorReset_LogMonitorResetRecord, len(monitor.Records))
	for i, r := range monitor.Records {
		mr[i] = &Logs_CMU2212_Base_LogData_LogMonitorReset_LogMonitorResetRecord{
			Datetime: r.EventTimestamp,
		}
	}
	out.LogMonitorReset = &Logs_CMU2212_Base_LogData_LogMonitorReset{Record: mr}

	// — LogPreviousFail
	pf := make([]*Logs_CMU2212_Base_LogData_LogPreviousFail_LogPreviousFailRecord, len(prevFail.Records))
	for i, r := range prevFail.Records {
		pf[i] = &Logs_CMU2212_Base_LogData_LogPreviousFail_LogPreviousFailRecord{
			Datetime:                          r.DateTime,
			Fault:                             r.Fault,
			AcLine:                            r.ACLine,
			T48VdcSignalBus:                   r.T48VDCSignalBus,
			RedEnable:                         r.RedEnable,
			McCoilEe:                          r.MCCoilEE,
			SpecialFunction1:                  r.SpecialFunction1,
			SpecialFunction2:                  r.SpecialFunction2,
			WdtMonitor:                        r.WDTMonitor,
			T24VdcInput:                       r.T24VDCInput,
			T12VdcInput:                       r.T12VDCInput,
			Temperature:                       strconv.Itoa(int(r.Temperature)) + " F",
			LsFlashBit:                        ediHelper.EtlTranslators["LsFlashBit"].String(r.LsFlashBit),
			FaultStatus:                       translateSlice(r.FaultStatus, "FaultStatus"),
			ChannelGreenStatus:                translateSlice(r.ChannelGreenStatus, "ChannelGreenStatus"),
			ChannelYellowStatus:               translateSlice(r.ChannelYellowStatus, "ChannelYellowStatus"),
			ChannelRedStatus:                  translateSlice(r.ChannelRedStatus, "ChannelRedStatus"),
			ChannelWalkStatus:                 translateSlice(r.ChannelWalkStatus, "ChannelWalkStatus"),
			ChannelGreenFieldCheckStatus:      translateSlice(r.ChannelGreenFieldCheckStatus, "ChannelGreenFieldCheckStatus"),
			ChannelYellowFieldCheckStatus:     translateSlice(r.ChannelYellowFieldCheckStatus, "ChannelYellowFieldCheckStatus"),
			ChannelRedFieldCheckStatus:        translateSlice(r.ChannelRedFieldCheckStatus, "ChannelRedFieldCheckStatus"),
			ChannelWalkFieldCheckStatus:       translateSlice(r.ChannelWalkFieldCheckStatus, "ChannelWalkFieldCheckStatus"),
			ChannelGreenRecurrentPulseStatus:  translateSlice(r.ChannelGreenRecurrentPulseStatus, "ChannelGreenRecurrentPulseStatus"),
			ChannelYellowRecurrentPulseStatus: translateSlice(r.ChannelYellowRecurrentPulseStatus, "ChannelYellowRecurrentPulseStatus"),
			ChannelRedRecurrentPulseStatus:    translateSlice(r.ChannelRedRecurrentPulseStatus, "ChannelRedRecurrentPulseStatus"),
			ChannelWalkRecurrentPulseStatus:   translateSlice(r.ChannelWalkRecurrentPulseStatus, "ChannelWalkRecurrentPulseStatus"),
			ChannelGreenRmsVoltage:            r.ChannelGreenRmsVoltage,
			ChannelYellowRmsVoltage:           r.ChannelYellowRmsVoltage,
			ChannelRedRmsVoltage:              r.ChannelRedRmsVoltage,
			ChannelWalkRmsVoltage:             r.ChannelWalkRmsVoltage,
			HdspSignalVoltages:                r.HDSPSignalVoltages,
			NextConflictingChannels:           translateSlice(r.NextConflictingChannels, "NextConflictingChannels"),
			ChannelRedCurrentStatus:           translateSlice(r.ChannelRedCurrentStatus, "ChannelRedCurrentStatus"),
			ChannelYellowCurrentStatus:        translateSlice(r.ChannelYellowCurrentStatus, "ChannelYellowCurrentStatus"),
			ChannelGreenCurrentStatus:         translateSlice(r.ChannelGreenCurrentStatus, "ChannelGreenCurrentStatus"),
			ChannelRedRmsCurrent:              r.ChannelRedRmsCurrent,
			ChannelYellowRmsCurrent:           r.ChannelYellowRmsCurrent,
			ChannelGreenRmsCurrent:            r.ChannelGreenRmsCurrent,
		}
	}
	out.LogPreviousFail = &Logs_CMU2212_Base_LogData_LogPreviousFail{Record: pf}
	// — LogConfigurationChange
	cc := make([]*Logs_CMU2212_Base_LogData_LogConfigurationChange_ConfigurationChangeLogRecord, len(config.Record))
	for i, r := range config.Record {
		translations, _ := devicesHelper.TranslateConfig(&r, ediHelper.EtlTranslators)
		cc[i] = &Logs_CMU2212_Base_LogData_LogConfigurationChange_ConfigurationChangeLogRecord{
			Datetime:                     r.DateTime,
			Ch_1Permissives:              r.Ch01Permissives,
			Ch_2Permissives:              r.Ch02Permissives,
			Ch_3Permissives:              r.Ch03Permissives,
			Ch_4Permissives:              r.Ch04Permissives,
			Ch_5Permissives:              r.Ch05Permissives,
			Ch_6Permissives:              r.Ch06Permissives,
			Ch_7Permissives:              r.Ch07Permissives,
			Ch_8Permissives:              r.Ch08Permissives,
			Ch_9Permissives:              r.Ch09Permissives,
			Ch_10Permissives:             r.Ch10Permissives,
			Ch_11Permissives:             r.Ch11Permissives,
			Ch_12Permissives:             r.Ch12Permissives,
			Ch_13Permissives:             r.Ch13Permissives,
			Ch_14Permissives:             r.Ch14Permissives,
			Ch_15Permissives:             r.Ch15Permissives,
			Ch_16Permissives:             r.Ch16Permissives,
			Ch_17Permissives:             r.Ch17Permissives,
			Ch_18Permissives:             r.Ch18Permissives,
			Ch_19Permissives:             r.Ch19Permissives,
			Ch_20Permissives:             r.Ch20Permissives,
			Ch_21Permissives:             r.Ch21Permissives,
			Ch_22Permissives:             r.Ch22Permissives,
			Ch_23Permissives:             r.Ch23Permissives,
			Ch_24Permissives:             r.Ch24Permissives,
			Ch_25Permissives:             r.Ch25Permissives,
			Ch_26Permissives:             r.Ch26Permissives,
			Ch_27Permissives:             r.Ch27Permissives,
			Ch_28Permissives:             r.Ch28Permissives,
			Ch_29Permissives:             r.Ch29Permissives,
			Ch_30Permissives:             r.Ch30Permissives,
			Ch_31Permissives:             r.Ch31Permissives,
			RedFailEnable:                translateSlice(r.RedFailEnable, "RedFailEnable"),
			GreenYellowDualEnable:        translateSlice(r.GreenYellowDualEnable, "GreenYellowDualEnable"),
			YellowRedDualEnable:          translateSlice(r.YellowRedDualEnable, "YellowRedDualEnable"),
			GreenRedDualEnable:           translateSlice(r.GreenRedDualEnable, "GreenRedDualEnable"),
			MinimumYellowClearanceEnable: translateSlice(r.MinimumYellowClearanceEnable, "MinimumYellowClearanceEnable"),
			YellowDisable:                translateSlice(r.YellowEnable, "YellowEnable"),
			HdspChannelEnable:            translateSlice(r.HdspChannelEnable, "HdspChannelEnable"),
			RedFaultTiming:               r.RedFaultTiming,
			RecurrentPulse:               translations["RecurrentPulse"],
			WatchdogTiming:               r.WatchdogTiming,
			WatchdogEnable:               translations["WatchdogEnableSwitch"],
			GYEnable:                     translations["GYEnable"],
			LedGuardThresholds:           translations["LEDGuardThresholds"],
			RedFailEnabledBySSM:          translations["RedFailEnabledBySSM"],
			DualIndicationFaultTiming:    r.DualIndicationFaultTiming,
			WDTErrorClearOnPU:            translations["WDTErrorClearOnPU"],
			MinimumFlash:                 translations["MinimumFlash"],
			ConfigChangeFault:            translations["ConfigChangeFault"],
			RedCableFault:                translations["RedCableFault"],
			AcLineBrownout:               r.AcLineBrownout,
			PinEEPolarity:                r.PinEEPolarity,
			FlashingYellowArrows:         r.FlashingYellowArrows,
			FyaFlashRateFault:            translations["FYAFlashRateFault"],
			X12vPowerSupplyMonitor:       translations["X12vPowerSupplyMonitor"],
			X48vPowerSupplyMonitor:       translations["X48vPowerSupplyMonitor"],
			CheckValue:                   r.CheckValue + "\n",
		}
	}
	out.LogConfigurationChange = &Logs_CMU2212_Base_LogData_LogConfigurationChange{Record: cc}

	// — LogACLineEvent
	ae := &Logs_CMU2212_Base_LogData_LogACLineEvent{
		VoltageType: convertVoltageType(acLine.VoltageType),
	}
	ae.Events = make([]*Logs_CMU2212_Base_LogData_LogACLineEvent_LogACLineEventRecord, len(acLine.Record))
	for i, r := range acLine.Record {
		ae.Events[i] = &Logs_CMU2212_Base_LogData_LogACLineEvent_LogACLineEventRecord{
			Datetime:        r.DateTime,
			EventType:       r.EventType,
			LineVoltageVrms: r.LineVoltageRms,
		}
	}
	out.LogAcLineEvent = ae

	// — LogFaultSignalSequence
	// Now updated to match ECL2010FPlus pattern with comprehensive buffer handling
	fsOut := make([]*Logs_CMU2212_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord, len(faultSeq.Records))
	for i, seqRec := range faultSeq.Records {
		buf := make([]*Logs_CMU2212_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer, len(seqRec.Records))
		for j, tb := range seqRec.Records {
			buf[j] = &Logs_CMU2212_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer{
				BufferRawBytes: tb.BufferRawBytes,
				Timestamp:      tb.Timestamp,
				Red:            tb.Reds,
				Yellow:         tb.Yellows,
				Green:          tb.Greens,
				Walk:           tb.Walks,
				EeSfRe:         tb.EE_SF_RE,
				AcVoltage:      tb.AcVoltage,
			}
		}

		fsOut[i] = &Logs_CMU2212_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord{
			FaultType: seqRec.FaultType,
			Buffers:   buf,
		}
	}
	out.LogFaultSignalSequence = &Logs_CMU2212_Base_LogData_LogFaultSignalSequence{Record: fsOut}

	return out
}

// ConvertSchemasToKCL2018Base converts log schemas to KCL2018 base format
// KCL2018 is similar to ECL2018 but with enhanced configuration records (86 bytes vs 85 bytes)
// that include Flashing Yellow Arrow (FYA) configuration
func ConvertSchemasToKCL2018Base(
	messageTime time.Time,
	schemaName string,
	deviceID string,
	detail *Logs_KCL2018_Base_LogData_DeviceDetail,

	monitor schemas.LogMonitorReset,
	prevFail schemas.LogPreviousFail,
	config schemas.LogConfiguration,
	acLine schemas.LogACLineEvent,
	faultSeq schemas.LogFaultSignalSequence,
) *Logs_KCL2018_Base_LogData {
	out := &Logs_KCL2018_Base_LogData{
		MessageTime:  messageTime,
		DeviceId:     deviceID,
		Schema:       schemaName,
		DeviceDetail: detail,
	}

	// — LogMonitorReset (same as ECL2018)
	mr := make([]*Logs_KCL2018_Base_LogData_LogMonitorReset_LogMonitorResetRecord, len(monitor.Records))
	for i, r := range monitor.Records {
		mr[i] = &Logs_KCL2018_Base_LogData_LogMonitorReset_LogMonitorResetRecord{
			Datetime: r.EventTimestamp,
		}
	}
	out.LogMonitorReset = &Logs_KCL2018_Base_LogData_LogMonitorReset{Record: mr}

	// — LogPreviousFail (same as ECL2018)
	pf := make([]*Logs_KCL2018_Base_LogData_LogPreviousFail_LogPreviousFailRecord, len(prevFail.Records))
	for i, r := range prevFail.Records {
		pf[i] = &Logs_KCL2018_Base_LogData_LogPreviousFail_LogPreviousFailRecord{
			Datetime:                          r.DateTime,
			Fault:                             r.Fault,
			AcLine:                            r.ACLine,
			X48VdcSignalBus:                   r.T48VDCSignalBus,
			RedEnable:                         r.RedEnable,
			McCoilEe:                          r.MCCoilEE,
			SpecialFunction1:                  r.SpecialFunction1,
			SpecialFunction2:                  r.SpecialFunction2,
			WdtMonitor:                        r.WDTMonitor,
			X24VdcInput:                       r.T24VDCInput,
			X12VdcInput:                       r.T12VDCInput,
			Temperature:                       r.Temperature,
			LsFlashBit:                        convertBoolToString(r.LsFlashBit),
			FaultStatus:                       translateSlice(r.FaultStatus, "FaultStatus"),
			ChannelGreenStatus:                translateSlice(r.ChannelGreenStatus, "ChannelGreenStatus"),
			ChannelYellowStatus:               translateSlice(r.ChannelYellowStatus, "ChannelYellowStatus"),
			ChannelRedStatus:                  translateSlice(r.ChannelRedStatus, "ChannelRedStatus"),
			ChannelWalkStatus:                 translateSlice(r.ChannelWalkStatus, "ChannelWalkStatus"),
			ChannelGreenFieldCheckStatus:      translateSlice(r.ChannelGreenFieldCheckStatus, "ChannelGreenFieldCheckStatus"),
			ChannelYellowFieldCheckStatus:     translateSlice(r.ChannelYellowFieldCheckStatus, "ChannelYellowFieldCheckStatus"),
			ChannelRedFieldCheckStatus:        translateSlice(r.ChannelRedFieldCheckStatus, "ChannelRedFieldCheckStatus"),
			ChannelWalkFieldCheckStatus:       translateSlice(r.ChannelWalkFieldCheckStatus, "ChannelWalkFieldCheckStatus"),
			ChannelGreenRecurrentPulseStatus:  translateSlice(r.ChannelGreenRecurrentPulseStatus, "ChannelGreenRecurrentPulseStatus"),
			ChannelYellowRecurrentPulseStatus: translateSlice(r.ChannelYellowRecurrentPulseStatus, "ChannelYellowRecurrentPulseStatus"),
			ChannelRedRecurrentPulseStatus:    translateSlice(r.ChannelRedRecurrentPulseStatus, "ChannelRedRecurrentPulseStatus"),
			ChannelWalkRecurrentPulseStatus:   translateSlice(r.ChannelWalkRecurrentPulseStatus, "ChannelWalkRecurrentPulseStatus"),
			ChannelGreenRmsVoltage:            convertInt64SliceToInt32(r.ChannelGreenRmsVoltage),
			ChannelYellowRmsVoltage:           convertInt64SliceToInt32(r.ChannelYellowRmsVoltage),
			ChannelRedRmsVoltage:              convertInt64SliceToInt32(r.ChannelRedRmsVoltage),
			ChannelWalkRmsVoltage:             convertInt64SliceToInt32(r.ChannelWalkRmsVoltage),
			HdspSignalVoltages:                r.HDSPSignalVoltages,
			NextConflictingChannels:           r.NextConflictingChannels,
			ChannelRedCurrentStatus:           r.ChannelRedCurrentStatus,
			ChannelYellowCurrentStatus:        r.ChannelYellowCurrentStatus,
			ChannelGreenCurrentStatus:         r.ChannelGreenCurrentStatus,
			ChannelRedRmsCurrent:              convertInt64SliceToInt32(r.ChannelRedRmsCurrent),
			ChannelYellowRmsCurrent:           convertInt64SliceToInt32(r.ChannelYellowRmsCurrent),
			ChannelGreenRmsCurrent:            convertInt64SliceToInt32(r.ChannelGreenRmsCurrent),
		}
	}
	out.LogPreviousFail = &Logs_KCL2018_Base_LogData_LogPreviousFail{Record: pf}

	// — LogConfigurationChange (enhanced for KCL2018 with FYA support)
	cc := make([]*Logs_KCL2018_Base_LogData_LogConfigurationChange_LogConfigurationChangeRecord, len(config.Record))
	for i, r := range config.Record {
		// Convert configuration fields from the basic record structure
		conflictMap := make([]int32, 54) // Default 54 elements for conflict map
		for j := 0; j < 54 && j < len(r.Ch01Permissives); j++ {
			conflictMap[j] = int32(j) // Simple mapping for now
		}

		cc[i] = &Logs_KCL2018_Base_LogData_LogConfigurationChange_LogConfigurationChangeRecord{
			Datetime:           r.DateTime,
			ConflictMap:        conflictMap,
			MinimumGreen:       0, // Default values for KCL2018 (enhanced vs ECL2018)
			YellowClearance:    0,
			RedClearance:       0,
			MaximumInitial:     0,
			MinimumInitial:     0,
			ExtensionIncrement: 0,
			MaximumExtension:   0,

			// Map individual channel permissives
			Ch01Permissives: r.Ch01Permissives,
			Ch02Permissives: r.Ch02Permissives,
			Ch03Permissives: r.Ch03Permissives,
			Ch04Permissives: r.Ch04Permissives,
			Ch05Permissives: r.Ch05Permissives,
			Ch06Permissives: r.Ch06Permissives,
			Ch07Permissives: r.Ch07Permissives,
			Ch08Permissives: r.Ch08Permissives,
			Ch09Permissives: r.Ch09Permissives,
			Ch10Permissives: r.Ch10Permissives,
			Ch11Permissives: r.Ch11Permissives,
			Ch12Permissives: r.Ch12Permissives,
			Ch13Permissives: r.Ch13Permissives,
			Ch14Permissives: r.Ch14Permissives,
			Ch15Permissives: r.Ch15Permissives,
			Ch16Permissives: r.Ch16Permissives,
			Ch17Permissives: r.Ch17Permissives,
			Ch18Permissives: r.Ch18Permissives,

			// Map channel enable bitfields (convert bool arrays to string arrays)
			RedFailEnable:                   translateSlice(r.RedFailEnable, "enable"),
			GreenYellowDualEnable:           translateSlice(r.GreenYellowDualEnable, "enable"),
			YellowRedDualEnable:             translateSlice(r.YellowRedDualEnable, "enable"),
			GreenRedDualEnable:              translateSlice(r.GreenRedDualEnable, "enable"),
			MinimumYellowClearanceEnable:    translateSlice(r.MinimumYellowClearanceEnable, "enable"),
			MinimumYellowRedClearanceEnable: translateSlice(r.MinimumYellowRedClearanceEnable, "enable"),
			YellowEnable:                    translateSlice(r.YellowEnable, "enable"),

			// Map configuration option flags (convert bool to string)
			WatchdogEnableSwitch: ediHelper.EtlTranslators["enable"].String(r.WatchdogEnableSwitch),
			RecurrentPulse:       ediHelper.EtlTranslators["enable"].String(r.RecurrentPulse),

			// Map KCL2018-specific fields
			FlashingYellowArrows: r.FlashingYellowArrows,
			MinimumFlashTime:     r.MinimumFlashTime,
		}
	}
	out.LogConfigurationChange = &Logs_KCL2018_Base_LogData_LogConfigurationChange{Record: cc}

	// — LogACLineEvent (same as ECL2018)
	ae := &Logs_KCL2018_Base_LogData_LogACLineEvent{
		VoltageType: convertVoltageType(acLine.VoltageType),
	}
	ae.Events = make([]*Logs_KCL2018_Base_LogData_LogACLineEvent_LogACLineEventRecord, len(acLine.Record))
	for i, r := range acLine.Record {
		ae.Events[i] = &Logs_KCL2018_Base_LogData_LogACLineEvent_LogACLineEventRecord{
			EventType:       r.EventType,
			Datetime:        r.DateTime,
			LineVoltageVrms: r.LineVoltageRms,
			LineFrequencyHz: r.LineFrequencyHz,
		}
	}
	out.LogAcLineEvent = ae

	// — LogFaultSignalSequence (same as ECL2018)
	fs := make([]*Logs_KCL2018_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord, len(faultSeq.Records))
	for i, seqRec := range faultSeq.Records {
		buf := make([]*Logs_KCL2018_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer, len(seqRec.Records))
		for j, tb := range seqRec.Records {
			buf[j] = &Logs_KCL2018_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer{
				BufferRawBytes: tb.BufferRawBytes,
				Timestamp:      tb.Timestamp,
				Red:            tb.Reds,
				Yellow:         tb.Yellows,
				Green:          tb.Greens,
				Walk:           tb.Walks,
				EeSfRe:         tb.EE_SF_RE,
				AcVoltage:      tb.AcVoltage,
			}
		}

		fs[i] = &Logs_KCL2018_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord{
			FaultType: seqRec.FaultType,
			Buffers:   buf,
		}
	}
	out.LogFaultSignalSequence = &Logs_KCL2018_Base_LogData_LogFaultSignalSequence{Record: fs}

	return out
}

