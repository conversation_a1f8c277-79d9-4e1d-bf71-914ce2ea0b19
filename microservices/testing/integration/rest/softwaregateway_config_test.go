package rest

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/connect"
	config "synapse-its.com/shared/rest/onramp/softwaregateway/config"
	"synapse-its.com/testing/utils"
)

// Test data constants from dev.sql
var (
	// Test Organization IDs from dev.sql
	testOrg1ID     = uuid.MustParse("ff41bb5d-9772-5066-bc59-e67e8a8159ee") // Test Organization 1
	corneliusOrgID = uuid.MustParse("c469a554-f7a8-5de5-a57e-e1ba16f970d3") // Synapse-Plano-Demo (has software gateways)

	// Software Gateway IDs from dev.sql
	testSoftwareGatewayID = uuid.MustParse("fb334ba5-0d43-517d-b76b-08fca829f388") // Test Gateway 1 in testOrg1ID

	// Test user credentials from dev.sql
	adminUserName     = "gateway_endpoint_verify"
	adminUserPassword = "!software:)" // Hash in dev.sql corresponds to this

	// Synapse admin user credentials for base settings tests
	synapseAdminUserName     = "synapse_admin"
	synapseAdminUserPassword = "!software:)" // Hash in dev.sql corresponds to this

	// Base URL for onramp service
	baseURL = "http://onramp:8080/api"
)

// Helper structures for API responses
type APIResponse struct {
	Code    int         `json:"code"`
	Data    interface{} `json:"data"`
	Message string      `json:"message"`
	Status  string      `json:"status"`
}

// setupIntegrationTest sets up the test environment and returns an authenticated HTTP client
func setupIntegrationTest(t *testing.T) (*http.Client, *http.Cookie) {
	t.Helper()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Wait for onramp service to be ready
	require.NoError(t, utils.AwaitOnramp(ctx, 5*time.Second), "onramp service should be ready")

	// Perform login to get session cookie
	sessionCookie := utils.PerformBasicAuthLogin(t, adminUserName, adminUserPassword)
	require.NotNil(t, sessionCookie, "Should have received a session cookie")

	// Create HTTP client with timeout
	client := &http.Client{Timeout: 10 * time.Second}

	return client, sessionCookie
}

// setupSynapseAdminTest sets up the test environment with synapse admin user and returns an authenticated HTTP client
func setupSynapseAdminTest(t *testing.T) (*http.Client, *http.Cookie) {
	t.Helper()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Wait for onramp service to be ready
	require.NoError(t, utils.AwaitOnramp(ctx, 5*time.Second), "onramp service should be ready")

	// Perform login to get session cookie with synapse admin credentials
	sessionCookie := utils.PerformBasicAuthLogin(t, synapseAdminUserName, synapseAdminUserPassword)
	require.NotNil(t, sessionCookie, "Should have received a session cookie")

	// Create HTTP client with timeout
	client := &http.Client{Timeout: 10 * time.Second}

	return client, sessionCookie
}

// makeAuthenticatedRequest makes an HTTP request with the session cookie
func makeAuthenticatedRequest(t *testing.T, client *http.Client, sessionCookie *http.Cookie, method, url string, body interface{}) *http.Response {
	t.Helper()

	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		require.NoError(t, err, "Should be able to marshal request body")
		bodyReader = bytes.NewReader(bodyBytes)
	}

	req, err := http.NewRequest(method, url, bodyReader)
	require.NoError(t, err, "Should be able to create request")

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	req.AddCookie(sessionCookie)

	resp, err := client.Do(req)
	require.NoError(t, err, "Should be able to make request")

	return resp
}

// parseAPIResponse parses an API response into the standard format
func parseAPIResponse(t *testing.T, resp *http.Response, target interface{}) *APIResponse {
	t.Helper()

	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err, "Should be able to read response body")
	defer resp.Body.Close()

	var apiResp APIResponse
	err = json.Unmarshal(body, &apiResp)
	require.NoError(t, err, "Should be able to parse JSON response")

	if target != nil && apiResp.Data != nil {
		// Re-marshal and unmarshal to convert interface{} to target type
		dataBytes, err := json.Marshal(apiResp.Data)
		require.NoError(t, err, "Should be able to marshal data")
		err = json.Unmarshal(dataBytes, target)
		require.NoError(t, err, "Should be able to unmarshal data to target type")
	}

	return &apiResp
}

// retryWithExponentialBackoff performs a retry operation with exponential backoff
// It retries the operation up to maxAttempts times, with delays starting at baseDelay
// and doubling each attempt, capped at maxDelay
func retryWithExponentialBackoff(t *testing.T, operation func(attempt int) bool, maxAttempts int, baseDelay time.Duration, maxDelay time.Duration, operationName string) bool {
	t.Helper()

	for attempt := 0; attempt < maxAttempts; attempt++ {
		if attempt > 0 {
			// Exponential backoff with cap
			delay := time.Duration(int64(baseDelay) * (1 << uint(attempt-1)))
			if delay > maxDelay {
				delay = maxDelay
			}
			time.Sleep(delay)
		}

		if operation(attempt) {
			if attempt > 0 {
				t.Logf("%s succeeded on attempt %d", operationName, attempt+1)
			}
			return true
		}

		t.Logf("%s attempt %d failed, retrying...", operationName, attempt+1)
	}

	t.Logf("%s failed after %d attempts", operationName, maxAttempts)
	return false
}

// Test Template Management Endpoints
func TestSoftwareGatewayConfig_TemplateManagement_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	t.Run("Create Template", func(t *testing.T) {
		// Create a new template
		createReq := config.CreateGatewayConfigTemplateRequest{
			Name:        "Test Integration Template",
			Description: "Template created by integration test",
		}

		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates", baseURL, testOrg1ID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		var template config.GatewayConfigTemplate
		apiResp := parseAPIResponse(t, resp, &template)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, createReq.Name, template.Name)
		assert.Equal(t, createReq.Description, template.Description)
		assert.Equal(t, testOrg1ID, template.OrganizationId)
		assert.NotEmpty(t, template.Id)

		// Store template ID for later tests
		templateID := template.Id

		t.Run("Get Template", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var retrievedTemplate config.GatewayConfigTemplate
			apiResp := parseAPIResponse(t, resp, &retrievedTemplate)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Equal(t, template.Id, retrievedTemplate.Id)
			assert.Equal(t, template.Name, retrievedTemplate.Name)
		})

		t.Run("Update Template", func(t *testing.T) {
			updateReq := config.UpdateGatewayConfigTemplateRequest{
				Name:        "Updated Integration Template",
				Description: "Updated by integration test",
			}

			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, updateReq)

			var updatedTemplate config.GatewayConfigTemplate
			apiResp := parseAPIResponse(t, resp, &updatedTemplate)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Equal(t, updateReq.Name, updatedTemplate.Name)
			assert.Equal(t, updateReq.Description, updatedTemplate.Description)
		})

		t.Run("List Templates", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates", baseURL, testOrg1ID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var templates []config.GatewayConfigTemplate
			apiResp := parseAPIResponse(t, resp, &templates)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.GreaterOrEqual(t, len(templates), 1, "Should have at least the template we created")

			// Find our template
			found := false
			for _, tmpl := range templates {
				if tmpl.Id == templateID {
					found = true
					assert.Equal(t, "Updated Integration Template", tmpl.Name)
					break
				}
			}
			assert.True(t, found, "Should find our created template in the list")
		})

		t.Run("Delete Template", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, url, nil)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify template is deleted by trying to get it (should return 404)
			getResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
			assert.Equal(t, http.StatusNotFound, getResp.StatusCode)
		})
	})
}

// Test Template Settings Management
func TestSoftwareGatewayConfig_TemplateSettings_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	// Get an existing template from the database (created by dev.sql)
	ctx := context.Background()
	connections := connect.NewConnections(ctx)
	defer connections.Close()

	// Query for a template in testOrg1
	query := `SELECT Id FROM {{GatewayConfigTemplate}} WHERE OrganizationId = $1 LIMIT 1`
	var templateResult struct {
		Id uuid.UUID `db:"id"`
	}
	err := connections.Postgres.QueryRowStruct(&templateResult, query, testOrg1ID)
	require.NoError(t, err, "Should find an existing template")
	templateID := templateResult.Id

	t.Run("Template Settings CRUD", func(t *testing.T) {
		// Create a setting
		createReq := config.CreateOrUpdateGatewayConfigTemplateSettingRequest{
			GatewayConfigTemplateId: templateID,
			Setting:                 "test_integration_setting",
			Value:                   "test_value",
		}

		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings", baseURL, testOrg1ID, templateID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		var setting config.GatewayConfigTemplateSetting
		apiResp := parseAPIResponse(t, resp, &setting)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, createReq.Setting, setting.Setting)
		assert.Equal(t, createReq.Value, setting.Value)

		t.Run("Get Template Settings", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings", baseURL, testOrg1ID, templateID)

			// Retry mechanism to handle race conditions with template setting creation
			var settings []config.GatewayConfigTemplateSetting
			var apiResp *APIResponse
			var resp *http.Response
			found := false

			// Retry up to 15 times with exponential backoff
			for attempt := 0; attempt < 15; attempt++ {
				if attempt > 0 {
					// Exponential backoff: 50ms, 100ms, 200ms, 400ms, 800ms, then cap at 1s
					delay := time.Duration(50*(1<<uint(attempt))) * time.Millisecond
					if delay > time.Second {
						delay = time.Second
					}
					time.Sleep(delay)
				}

				resp = makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
				if resp.StatusCode != http.StatusOK {
					t.Logf("Template settings GET attempt %d: HTTP status %d", attempt+1, resp.StatusCode)
					continue
				}

				settings = nil // Reset slice
				apiResp = parseAPIResponse(t, resp, &settings)

				if apiResp.Status != "success" {
					t.Logf("Template settings GET attempt %d: API status %s", attempt+1, apiResp.Status)
					continue
				}

				if len(settings) > 0 {
					// Find our test setting
					for _, s := range settings {
						if s.Setting == "test_integration_setting" {
							found = true
							assert.Equal(t, "test_value", s.Value)
							break
						}
					}

					if found {
						t.Logf("Template setting found on attempt %d", attempt+1)
						break // Success - exit retry loop
					}
				}
				t.Logf("Template settings GET attempt %d: setting not found yet, found %d settings", attempt+1, len(settings))
			}

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Greater(t, len(settings), 0, "Should have settings")
			assert.True(t, found, "Should find our test setting")
		})

		t.Run("Bulk Replace Template Settings", func(t *testing.T) {
			bulkSettings := []config.GatewayConfigTemplateSetting{
				{
					GatewayConfigTemplateId: templateID,
					Setting:                 "bulk_setting_1",
					Value:                   "bulk_value_1",
				},
				{
					GatewayConfigTemplateId: templateID,
					Setting:                 "bulk_setting_2",
					Value:                   "bulk_value_2",
				},
			}

			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPut, url, bulkSettings)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify the settings were replaced - with retry for race conditions
			var settings []config.GatewayConfigTemplateSetting
			var getResp *http.Response
			settingMap := make(map[string]string)
			foundCorrectSettings := false

			// Retry up to 10 times with exponential backoff
			for attempt := 0; attempt < 10; attempt++ {
				if attempt > 0 {
					// Exponential backoff: 100ms, 200ms, 400ms, 800ms, then cap at 1s
					delay := time.Duration(100*(1<<uint(attempt))) * time.Millisecond
					if delay > time.Second {
						delay = time.Second
					}
					time.Sleep(delay)
				}

				getResp = makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
				if getResp.StatusCode != http.StatusOK {
					t.Logf("Bulk replace verification attempt %d: HTTP status %d", attempt+1, getResp.StatusCode)
					continue
				}

				settings = nil                       // Reset slice
				settingMap = make(map[string]string) // Reset map
				apiResp := parseAPIResponse(t, getResp, &settings)

				if apiResp.Status != "success" {
					t.Logf("Bulk replace verification attempt %d: API status %s", attempt+1, apiResp.Status)
					continue
				}

				t.Logf("Bulk replace verification attempt %d: found %d settings", attempt+1, len(settings))

				// Check if we have the expected 2 settings
				if len(settings) == 2 {
					for _, s := range settings {
						settingMap[s.Setting] = s.Value
					}

					// Check if we have the correct settings
					if settingMap["bulk_setting_1"] == "bulk_value_1" && settingMap["bulk_setting_2"] == "bulk_value_2" {
						foundCorrectSettings = true
						t.Logf("Bulk replace verification successful on attempt %d", attempt+1)
						break
					}
				}

				t.Logf("Bulk replace verification attempt %d: incorrect settings found", attempt+1)
				for setting, value := range settingMap {
					t.Logf("  - %s: %s", setting, value)
				}
			}

			// Should only have the 2 bulk settings now (previous settings replaced)
			assert.Len(t, settings, 2)
			assert.True(t, foundCorrectSettings, "Should find the correct bulk settings after retries")
			assert.Equal(t, "bulk_value_1", settingMap["bulk_setting_1"])
			assert.Equal(t, "bulk_value_2", settingMap["bulk_setting_2"])
		})

		t.Run("Delete Template Setting", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings/bulk_setting_1", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, url, nil)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify setting is deleted with retry mechanism
			getUrl := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings", baseURL, testOrg1ID, templateID)

			deletionVerified := retryWithExponentialBackoff(t, func(attempt int) bool {
				getResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, getUrl, nil)
				if getResp.StatusCode != http.StatusOK {
					return false
				}

				var settings []config.GatewayConfigTemplateSetting
				apiResp := parseAPIResponse(t, getResp, &settings)
				if apiResp.Status != "success" {
					return false
				}

				// Check that the deleted setting is not present
				for _, s := range settings {
					if s.Setting == "bulk_setting_1" {
						return false // Setting still found, deletion not complete
					}
				}
				return true // Setting not found, deletion verified
			}, 10, 50*time.Millisecond, time.Second, "Template setting deletion verification")

			assert.True(t, deletionVerified, "Deleted template setting should not be present")
		})
	})
}

// Test Override Management (including new bulk upsert)
func TestSoftwareGatewayConfig_Overrides_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	// Use the existing software gateway from dev.sql
	softwareGatewayID := testSoftwareGatewayID
	orgID := testOrg1ID // Test Gateway 1 belongs to Test Organization 1

	t.Logf("Using Software Gateway ID: %s, Organization ID: %s", softwareGatewayID, orgID)

	t.Run("Override CRUD Operations", func(t *testing.T) {
		// First, clean up any existing test overrides to ensure clean state
		cleanupURL := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides/test_override_setting", baseURL, orgID, softwareGatewayID)
		t.Logf("Cleaning up any existing test override at: %s", cleanupURL)
		cleanupResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, cleanupURL, nil)
		t.Logf("Cleanup response: %d (this may be 404 if no existing override, which is fine)", cleanupResp.StatusCode)

		// Small delay after cleanup
		time.Sleep(50 * time.Millisecond)

		// Create an override (base setting will be created automatically by the handler)
		createReq := config.CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
			SoftwareGatewayId: softwareGatewayID,
			Setting:           "test_override_setting",
			Value:             "override_value",
		}

		url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, orgID, softwareGatewayID)
		t.Logf("Creating override at URL: %s", url)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		var override config.GatewayConfigTemplateSettingOverride
		apiResp := parseAPIResponse(t, resp, &override)

		// Log the creation response for debugging
		t.Logf("Override creation response: Status=%d, API Status=%s", resp.StatusCode, apiResp.Status)
		if resp.StatusCode == http.StatusOK && apiResp.Status == "success" {
			t.Logf("Created override: Setting='%s', Value='%s', SoftwareGatewayId=%s", override.Setting, override.Value, override.SoftwareGatewayId)
		} else {
			t.Logf("Override creation failed: HTTP Status=%d, API Status=%s, Message=%s", resp.StatusCode, apiResp.Status, apiResp.Message)
		}

		require.Equal(t, http.StatusOK, resp.StatusCode, "Override creation should succeed")
		require.Equal(t, "success", apiResp.Status, "Override creation API should return success")
		require.Equal(t, createReq.Setting, override.Setting, "Override setting name should match")
		require.Equal(t, createReq.Value, override.Value, "Override value should match")
		require.Equal(t, softwareGatewayID, override.SoftwareGatewayId, "Override software gateway ID should match")

		// Add a small delay to ensure the creation is fully committed before proceeding
		time.Sleep(100 * time.Millisecond)

		// Verify that the override was actually created by doing an immediate check
		verifyURL := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, orgID, softwareGatewayID)
		t.Logf("Verifying override creation immediately at: %s", verifyURL)
		verifyResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, verifyURL, nil)
		if verifyResp.StatusCode == http.StatusOK {
			var verifyOverrides []config.GatewayConfigTemplateSettingOverride
			verifyApiResp := parseAPIResponse(t, verifyResp, &verifyOverrides)
			if verifyApiResp.Status == "success" {
				t.Logf("Immediate verification: found %d overrides", len(verifyOverrides))
				verifyFound := false
				for _, o := range verifyOverrides {
					if o.Setting == "test_override_setting" {
						verifyFound = true
						t.Logf("Immediate verification: test override found with value '%s'", o.Value)
						break
					}
				}
				if !verifyFound {
					t.Logf("WARNING: Immediate verification failed - test override not found among %d overrides", len(verifyOverrides))
				}
			} else {
				t.Logf("WARNING: Immediate verification failed - API status: %s", verifyApiResp.Status)
			}
		} else {
			t.Logf("WARNING: Immediate verification failed - HTTP status: %d", verifyResp.StatusCode)
		}

		t.Run("Get Overrides", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, orgID, softwareGatewayID)
			t.Logf("Getting overrides from URL: %s", url)

			// Retry mechanism to handle potential race conditions
			var overrides []config.GatewayConfigTemplateSettingOverride
			var apiResp *APIResponse
			var resp *http.Response
			found := false

			// Retry up to 10 times with exponential backoff
			for attempt := 0; attempt < 10; attempt++ {
				if attempt > 0 {
					// Exponential backoff: 100ms, 200ms, 400ms, 800ms, then cap at 1s
					delay := time.Duration(100*(1<<uint(attempt))) * time.Millisecond
					if delay > time.Second {
						delay = time.Second
					}
					time.Sleep(delay)
				}

				resp = makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
				if resp.StatusCode != http.StatusOK {
					t.Logf("Get overrides attempt %d: HTTP status %d", attempt+1, resp.StatusCode)
					continue
				}

				overrides = nil // Reset slice
				apiResp = parseAPIResponse(t, resp, &overrides)

				if apiResp.Status != "success" {
					t.Logf("Get overrides attempt %d: API status %s", attempt+1, apiResp.Status)
					continue
				}

				t.Logf("Get overrides attempt %d: found %d overrides", attempt+1, len(overrides))

				if len(overrides) > 0 {
					// Log all found overrides for debugging
					t.Logf("Get overrides attempt %d: Override settings found:", attempt+1)
					for i, o := range overrides {
						t.Logf("  [%d] Setting: '%s', Value: '%s', SoftwareGatewayId: %s", i+1, o.Setting, o.Value, o.SoftwareGatewayId)
					}

					// Check if any overrides belong to a different gateway (potential issue)
					expectedGatewayStr := softwareGatewayID.String()
					wrongGatewayCount := 0
					for _, o := range overrides {
						if o.SoftwareGatewayId.String() != expectedGatewayStr {
							wrongGatewayCount++
						}
					}
					if wrongGatewayCount > 0 {
						t.Logf("WARNING: Found %d overrides belonging to different software gateways (expected: %s)", wrongGatewayCount, expectedGatewayStr)
					}

					// Find our test override
					for _, o := range overrides {
						if o.Setting == "test_override_setting" {
							found = true
							assert.Equal(t, "override_value", o.Value)
							assert.Equal(t, softwareGatewayID, o.SoftwareGatewayId, "Override should belong to the correct software gateway")
							break
						}
					}

					if found {
						t.Logf("Test override found on attempt %d", attempt+1)
						break // Success - exit retry loop
					}
				}

				t.Logf("Get overrides attempt %d: test override 'test_override_setting' not found yet", attempt+1)
			}

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Greater(t, len(overrides), 0, "Should have overrides")
			assert.True(t, found, "Should find our test override")
		})

		t.Run("Bulk Upsert Overrides", func(t *testing.T) {
			// Base settings will be created automatically by the handler
			bulkRequest := config.BulkUpsertGatewayConfigTemplateSettingOverridesRequest{
				"upsert_setting_1":      "upsert_value_1",
				"upsert_setting_2":      "upsert_value_2",
				"test_override_setting": "updated_override_value", // This should update the existing one
			}

			url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides/bulk", baseURL, orgID, softwareGatewayID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, bulkRequest)

			var overrides []config.GatewayConfigTemplateSettingOverride
			apiResp := parseAPIResponse(t, resp, &overrides)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Len(t, overrides, 3, "Should return 3 overrides")

			// Verify all settings are present with correct values
			overrideMap := make(map[string]string)
			for _, o := range overrides {
				overrideMap[o.Setting] = o.Value
				assert.Equal(t, softwareGatewayID, o.SoftwareGatewayId)
			}
			assert.Equal(t, "upsert_value_1", overrideMap["upsert_setting_1"])
			assert.Equal(t, "upsert_value_2", overrideMap["upsert_setting_2"])
			assert.Equal(t, "updated_override_value", overrideMap["test_override_setting"])

			// Database-level sync: Ensure transaction is committed and data is visible
			// by performing a dedicated read operation to confirm all upserted records exist
			syncUrl := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, orgID, softwareGatewayID)
			syncResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, syncUrl, nil)
			require.Equal(t, http.StatusOK, syncResp.StatusCode, "Database sync read should succeed")
			
			var syncOverrides []config.GatewayConfigTemplateSettingOverride
			parseAPIResponse(t, syncResp, &syncOverrides)
			
			// Verify all expected records are visible (confirms transaction commit)
			syncOverrideMap := make(map[string]string)
			for _, o := range syncOverrides {
				syncOverrideMap[o.Setting] = o.Value
			}
			require.Contains(t, syncOverrideMap, "upsert_setting_1", "Database sync: upsert_setting_1 should be visible")
			require.Contains(t, syncOverrideMap, "upsert_setting_2", "Database sync: upsert_setting_2 should be visible")
			require.Contains(t, syncOverrideMap, "test_override_setting", "Database sync: test_override_setting should be visible")
		})

		t.Run("Delete Override", func(t *testing.T) {
			// First, verify the override exists before attempting to delete it
			getUrl := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, orgID, softwareGatewayID)

			// Retry mechanism to ensure override exists before deletion (handles race conditions from bulk upsert)
			var overridesToDelete []config.GatewayConfigTemplateSettingOverride
			overrideExists := false

			// Retry up to 15 times with exponential backoff to handle database consistency delays
			for attempt := 0; attempt < 15; attempt++ {
				if attempt > 0 {
					// Exponential backoff: 50ms, 100ms, 200ms, 400ms, 800ms, then cap at 1s
					delay := time.Duration(50*(1<<uint(attempt))) * time.Millisecond
					if delay > time.Second {
						delay = time.Second
					}
					time.Sleep(delay)
				}

				getResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, getUrl, nil)
				if getResp.StatusCode != http.StatusOK {
					t.Logf("Attempt %d: GET request failed with status %d", attempt+1, getResp.StatusCode)
					continue
				}

				overridesToDelete = nil // Reset slice
				apiResp := parseAPIResponse(t, getResp, &overridesToDelete)

				if apiResp.Status != "success" {
					t.Logf("Attempt %d: API response status was %s", attempt+1, apiResp.Status)
					continue
				}

				t.Logf("Attempt %d: Found %d overrides", attempt+1, len(overridesToDelete))

				// Check if the setting we want to delete exists
				for _, o := range overridesToDelete {
					t.Logf("Attempt %d: Found override setting: %s", attempt+1, o.Setting)
					if o.Setting == "upsert_setting_1" {
						overrideExists = true
						break
					}
				}

				if overrideExists {
					t.Logf("Attempt %d: Override 'upsert_setting_1' found, proceeding with deletion", attempt+1)
					break // Override found, proceed with deletion
				}

				t.Logf("Attempt %d: Override 'upsert_setting_1' not found yet, retrying...", attempt+1)
			}

			require.True(t, overrideExists, "Override 'upsert_setting_1' should exist before deletion (race condition check)")

			// Now attempt deletion
			deleteUrl := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides/upsert_setting_1", baseURL, orgID, softwareGatewayID)
			deleteResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, deleteUrl, nil)

			apiResp := parseAPIResponse(t, deleteResp, nil)

			if deleteResp.StatusCode != http.StatusOK || apiResp.Status != "success" {
				t.Logf("Delete operation failed: Status=%d, Response=%s", deleteResp.StatusCode, apiResp.Status)
				// Log current state for debugging
				debugResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, getUrl, nil)
				var debugOverrides []config.GatewayConfigTemplateSettingOverride
				parseAPIResponse(t, debugResp, &debugOverrides)
				t.Logf("Current overrides after failed delete:")
				for _, o := range debugOverrides {
					t.Logf("  - %s: %s", o.Setting, o.Value)
				}
			}

			assert.Equal(t, http.StatusOK, deleteResp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify override is actually deleted with retry mechanism
			deletionVerified := false
			for attempt := 0; attempt < 10; attempt++ {
				if attempt > 0 {
					// Exponential backoff for verification too
					delay := time.Duration(100*(1<<uint(attempt))) * time.Millisecond
					if delay > time.Second {
						delay = time.Second
					}
					time.Sleep(delay)
				}

				verifyResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, getUrl, nil)
				var remainingOverrides []config.GatewayConfigTemplateSettingOverride
				parseAPIResponse(t, verifyResp, &remainingOverrides)

				settingFound := false
				for _, o := range remainingOverrides {
					if o.Setting == "upsert_setting_1" {
						settingFound = true
						break
					}
				}

				if !settingFound {
					deletionVerified = true
					t.Logf("Deletion verified on attempt %d", attempt+1)
					break
				}
				t.Logf("Deletion verification attempt %d: setting still found", attempt+1)
			}

			assert.True(t, deletionVerified, "Deleted override 'upsert_setting_1' should not be present after deletion")
		})
	})
}

// Test Legacy Bulk Upsert Endpoint (without organizationId in URL)
func TestSoftwareGatewayConfig_LegacyBulkUpsert_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	// Use the existing software gateway from dev.sql
	softwareGatewayID := testSoftwareGatewayID

	t.Run("Legacy Bulk Upsert Without OrganizationId", func(t *testing.T) {
		// Base settings will be created automatically by the handler
		bulkRequest := config.BulkUpsertGatewayConfigTemplateSettingOverridesRequest{
			"legacy_setting_1": "legacy_value_1",
			"legacy_setting_2": "legacy_value_2",
		}

		// Note: URL does not include organizationId - this is the legacy endpoint
		url := fmt.Sprintf("%s/softwaregateway/%s/config", baseURL, softwareGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, bulkRequest)

		var overrides []config.GatewayConfigTemplateSettingOverride
		apiResp := parseAPIResponse(t, resp, &overrides)

		assert.Equal(t, http.StatusForbidden, resp.StatusCode)
		assert.Equal(t, "forbidden", apiResp.Status)
	})

	t.Run("Legacy Endpoint Error Cases", func(t *testing.T) {
		// Test with invalid gateway ID
		invalidGatewayID := uuid.New()
		bulkRequest := config.BulkUpsertGatewayConfigTemplateSettingOverridesRequest{
			"setting": "value",
		}

		url := fmt.Sprintf("%s/softwaregateway/%s/config", baseURL, invalidGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, bulkRequest)

		assert.Equal(t, http.StatusForbidden, resp.StatusCode, "Should return 404 for non-existent gateway")

		// Test with empty settings
		emptyRequest := config.BulkUpsertGatewayConfigTemplateSettingOverridesRequest{}
		url = fmt.Sprintf("%s/softwaregateway/%s/config", baseURL, softwareGatewayID)
		resp = makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, emptyRequest)

		assert.Equal(t, http.StatusForbidden, resp.StatusCode, "Should return 400 for empty settings")
	})
}

// Test Base Settings Management
func TestSoftwareGatewayConfig_BaseSettings_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupSynapseAdminTest(t)

	t.Run("Base Settings CRUD", func(t *testing.T) {
		// Create a base setting
		baseSetting := config.GatewayConfigTemplateBaseSetting{
			Setting:      "test_integration_base_setting",
			DefaultValue: "default_test_value",
			Name:         "Test Integration Base Setting",
			Description:  "Base setting created by integration test",
			Format:       `{"type":"string"}`,
		}

		url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings", baseURL)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, baseSetting)

		apiResp := parseAPIResponse(t, resp, nil)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)

		t.Run("Get Base Setting", func(t *testing.T) {
			url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings/%s", baseURL, baseSetting.Setting)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var retrievedSetting config.GatewayConfigTemplateBaseSetting
			apiResp := parseAPIResponse(t, resp, &retrievedSetting)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Equal(t, baseSetting.Setting, retrievedSetting.Setting)
			assert.Equal(t, baseSetting.Name, retrievedSetting.Name)
			assert.Equal(t, baseSetting.DefaultValue, retrievedSetting.DefaultValue)
		})

		t.Run("Update Base Setting", func(t *testing.T) {
			updatedSetting := baseSetting
			updatedSetting.DefaultValue = "updated_default_value"
			updatedSetting.Name = "Updated Test Base Setting"

			url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings/%s", baseURL, baseSetting.Setting)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, updatedSetting)

			var result config.GatewayConfigTemplateBaseSetting
			apiResp := parseAPIResponse(t, resp, &result)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Equal(t, updatedSetting.DefaultValue, result.DefaultValue)
			assert.Equal(t, updatedSetting.Name, result.Name)
		})

		t.Run("List Base Settings", func(t *testing.T) {
			url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings", baseURL)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var settings []config.GatewayConfigTemplateBaseSetting
			apiResp := parseAPIResponse(t, resp, &settings)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Greater(t, len(settings), 0, "Should have base settings")

			// Find our test setting
			found := false
			for _, s := range settings {
				if s.Setting == baseSetting.Setting {
					found = true
					assert.Equal(t, "updated_default_value", s.DefaultValue)
					break
				}
			}
			assert.True(t, found, "Should find our test base setting")
		})

		t.Run("Delete Base Setting", func(t *testing.T) {
			url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings/%s", baseURL, baseSetting.Setting)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, url, nil)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify setting is deleted with retry mechanism
			deletionVerified := retryWithExponentialBackoff(t, func(attempt int) bool {
				getResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
				return getResp.StatusCode == http.StatusNotFound
			}, 10, 50*time.Millisecond, time.Second, "Base setting deletion verification")

			assert.True(t, deletionVerified, "Deleted base setting should return 404")
		})
	})
}

// Test Error Scenarios and Permission Handling
func TestSoftwareGatewayConfig_ErrorScenarios_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	t.Run("Invalid Organization ID", func(t *testing.T) {
		invalidOrgID := uuid.New()
		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates", baseURL, invalidOrgID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

		// Should return empty list for non-existent organization
		var templates []config.GatewayConfigTemplate
		apiResp := parseAPIResponse(t, resp, &templates)

		assert.Equal(t, http.StatusForbidden, resp.StatusCode)
		assert.Equal(t, "forbidden", apiResp.Status)
	})

	t.Run("Invalid Template ID", func(t *testing.T) {
		invalidTemplateID := uuid.New()
		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s", baseURL, testOrg1ID, invalidTemplateID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})

	t.Run("Invalid Software Gateway ID", func(t *testing.T) {
		invalidGatewayID := uuid.New()
		url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, testOrg1ID, invalidGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})

	t.Run("Malformed JSON", func(t *testing.T) {
		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates", baseURL, testOrg1ID)

		req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader([]byte("{invalid json")))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")
		req.AddCookie(sessionCookie)

		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusBadRequest, resp.StatusCode)
	})
}

// Test Database Consistency and Relationships
func TestSoftwareGatewayConfig_DatabaseConsistency_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	// Test that creating overrides for non-existent base settings still works
	// (the system should allow any setting name, not just those in base settings)
	t.Run("Override with Custom Setting Name", func(t *testing.T) {
		customSettingName := fmt.Sprintf("custom_setting_%d", time.Now().Unix())

		createReq := config.CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
			SoftwareGatewayId: testSoftwareGatewayID,
			Setting:           customSettingName,
			Value:             "custom_value",
		}

		url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, testOrg1ID, testSoftwareGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		var override config.GatewayConfigTemplateSettingOverride
		apiResp := parseAPIResponse(t, resp, &override)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, customSettingName, override.Setting)
		assert.Equal(t, "custom_value", override.Value)
	})

	// Test cascade behavior - this depends on your database constraints
	t.Run("Software Gateway Organization Consistency", func(t *testing.T) {
		// Try to create override for gateway in different organization
		// This should fail due to the verification logic in the handlers

		createReq := config.CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
			SoftwareGatewayId: testSoftwareGatewayID, // This gateway belongs to testOrg1ID
			Setting:           "test_cross_org_setting",
			Value:             "should_fail",
		}

		// Try to create it under corneliusOrgID (wrong organization)
		url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, corneliusOrgID, testSoftwareGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		assert.Equal(t, http.StatusForbidden, resp.StatusCode, "Should fail when gateway doesn't belong to organization")
	})
}
