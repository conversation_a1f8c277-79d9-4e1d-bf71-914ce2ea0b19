package ediecl2018

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestRMSStatus(t *testing.T) {
	device := EDIECL2018{}
	header := &helper.HeaderRecord{
		Model:            helper.Ecl2018,
		MaxChannels:      16,
		Volt220:          false,
		VoltDC:           false,
		CommVersion:      "27",
		FirmwareRevision: "51",
	}
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}

	t.Run("valid_data_with_fault", func(t *testing.T) {
		data := make([]byte, HeaderLength+Get2018RmsStatusLength)

		// Add header (7 bytes)
		copy(data[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07})

		// Add fault code (4 bytes at offset 7-10)
		copy(data[7:11], []byte{0x01, 0x02, 0x03, 0x04})

		// Add timestamp data (BCD format) at offsets 22-27
		data[22] = 0x00 // second
		data[23] = 0x00 // minute
		data[24] = 0x12 // hour
		data[25] = 0x15 // day
		data[26] = 0x06 // month (June)
		data[27] = 0x23 // year (2023)

		// Set temperature at offset 28
		data[28] = 0x50 // Temperature (80 - 40 = 40°C)

		// Set voltages at proper offsets
		data[34] = 0x00
		data[35] = 0x64 // First red voltage = 100
		data[52] = 0x00
		data[53] = 0x78 // First yellow voltage = 120
		data[70] = 0x00
		data[71] = 0x5A // First green voltage = 90

		// Calculate checksum
		var sum uint8
		for i := 0; i < len(data)-1; i++ {
			sum += data[i]
		}
		data[len(data)-1] = ^sum

		result, err := device.RMSStatus(httpHeader, data, header)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(40), result.Temperature) // 0x50 (80) - 40 = 40°C
		assert.True(t, result.IsFaulted)
	})

	t.Run("no_fault", func(t *testing.T) {
		data := make([]byte, HeaderLength+Get2018RmsStatusLength)
		copy(data[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07})

		// Test with fault code 0 (no fault)
		copy(data[7:11], []byte{0x00, 0x00, 0x00, 0x00})

		// Add valid timestamp
		data[22] = 0x00
		data[23] = 0x00
		data[24] = 0x12
		data[25] = 0x15
		data[26] = 0x06
		data[27] = 0x23

		data[28] = 0x50 // Temperature

		// Calculate checksum
		var sum uint8
		for i := 0; i < len(data)-1; i++ {
			sum += data[i]
		}
		data[len(data)-1] = ^sum

		result, err := device.RMSStatus(httpHeader, data, header)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.False(t, result.IsFaulted)
		assert.Equal(t, "No Fault", result.Fault)
	})

	t.Run("nil_header", func(t *testing.T) {
		data := make([]byte, HeaderLength+Get2018RmsStatusLength)
		_, err := device.RMSStatus(httpHeader, data, nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "header is nil")
	})

	t.Run("insufficient_data", func(t *testing.T) {
		shortData := make([]byte, 10)
		_, err := device.RMSStatus(httpHeader, shortData, header)
		assert.Error(t, err)
		// The error could be either "should be" (length) or "checksum failure"
		assert.True(t,
			err.Error() == "error validating the checksum : checksum failure" ||
				strings.Contains(err.Error(), "should be"))
	})

	t.Run("checksum_failure", func(t *testing.T) {
		data := make([]byte, HeaderLength+Get2018RmsStatusLength)
		copy(data[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07})
		data[len(data)-1] = 0x00 // Wrong checksum

		_, err := device.RMSStatus(httpHeader, data, header)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "checksum")
	})
}

func TestGetFaultMessage(t *testing.T) {
	device := EDIECL2018{}
	header := &helper.HeaderRecord{
		Model:            helper.Ecl2018,
		MaxChannels:      16,
		CommVersion:      "27",
		FirmwareRevision: "51",
	}

	t.Run("all_fault_codes", func(t *testing.T) {
		// Test fault codes that exist in the actual implementation
		testCases := map[int]string{
			0:   "undefined fault type", // Not explicitly handled
			1:   "+24VDC Low Fault (VDC Fail)",
			2:   "CU Watchdog Fault (WDT Error)",
			3:   "Conflict Fault",
			4:   "Dual Indication Fault",
			5:   "Red Fail Fault",
			6:   "Clearance (Skipped Yellow) Fault",
			7:   "BND Fault",
			8:   "", // Returns empty string for message when faultStatus=0
			9:   "Program Card Ajar Fault",
			10:  "AC Line Low Voltage",
			12:  "Red Cable Fault",
			13:  "Configuration Change Fault",
			14:  "Clearance (Short Yellow) Fault",
			15:  "Recurrent Pulse Conflict",
			16:  "Recurrent Pulse Dual Indication",
			17:  "Recurrent Pulse Red Fail",
			18:  "+48VDC Fault (VDC Fail)",
			19:  "Data Key Absent",
			20:  "Data Key FCS Error",
			21:  "Data Key Invalid Parameter Error",
			22:  "Minimum Yellow + Red Clearance Fault",
			23:  "+24VDC High Fault",
			24:  "+24VDC Ripple Fault",
			25:  "Program Card Fault",
			26:  "AC Line Frequency Fault",
			27:  "FYA Flash Rate Fault",
			999: "undefined fault type", // default case
		}

		for faultCode, expectedMsg := range testCases {
			msg, statusMsg := device.getFaultMessage(faultCode, 0, header)
			assert.Equal(t, expectedMsg, msg, "Fault code %d", faultCode)
			assert.NotNil(t, statusMsg)
		}
	})

	t.Run("fault_8_special_status", func(t *testing.T) {
		msg, statusMsg := device.getFaultMessage(8, 1430, header)
		assert.Equal(t, "", msg) // Fault code 8 returns empty message
		assert.Equal(t, "(RMS Engine A/D error)", statusMsg)

		msg, statusMsg = device.getFaultMessage(8, 1670, header)
		assert.Equal(t, "(RMS Engine comm error)", statusMsg)
	})

	t.Run("fault_25_status_message", func(t *testing.T) {
		msg, statusMsg := device.getFaultMessage(25, 0, header)
		assert.Equal(t, "Program Card Fault", msg)
		assert.Equal(t, "(Program Card is not 18 Channel)", statusMsg)
	})

	t.Run("fault_13_newer_version", func(t *testing.T) {
		header.CommVersion = "30" // Test newer version for fault 13
		msg, statusMsg := device.getFaultMessage(13, 1234, header)
		assert.Equal(t, "Configuration Change Fault", msg)
		assert.Equal(t, "Check Value = 1234", statusMsg)

		header.CommVersion = "27" // Reset
	})
}

func TestHelperFunctions(t *testing.T) {
	header := &helper.HeaderRecord{
		Model:       helper.Ecl2018,
		MaxChannels: 16,
		Volt220:     false,
		VoltDC:      false,
	}

	t.Run("formatVoltages", func(t *testing.T) {
		byteData := []byte{0x00, 0x64, 0x00, 0x78, 0x00, 0x5A}
		result := formatVoltages(byteData, header)
		assert.IsType(t, []int64{}, result)
		assert.Len(t, result, 6)
	})

	t.Run("combineBytes", func(t *testing.T) {
		result := combineBytes([]byte{0x01, 0x02, 0x03})
		expected := (0x01 << 16) | (0x02 << 8) | 0x03
		assert.Equal(t, expected, result)
	})

	t.Run("parseChannelStatus", func(t *testing.T) {
		status := helper.ParseChannelStatus(uint32(0b11000011), 8)
		expected := []bool{true, true, false, false, false, false, true, true}
		assert.Equal(t, expected, status)
	})


	t.Run("applyVoltageAdjustment", func(t *testing.T) {
		// Test no adjustments
		result := applyVoltageAdjustment(100, header)
		assert.Equal(t, int32(100), result)

		// Test Volt220 only
		header.Volt220 = true
		result = applyVoltageAdjustment(100, header)
		assert.Equal(t, int32(200), result)

		// Test VoltDC only
		header.Volt220 = false
		header.VoltDC = true
		result = applyVoltageAdjustment(100, header)
		assert.Equal(t, int32(25), result)

		// Test both
		header.Volt220 = true
		header.VoltDC = true
		result = applyVoltageAdjustment(100, header)
		assert.Equal(t, int32(50), result)

		// Reset
		header.Volt220 = false
		header.VoltDC = false
	})
}

func TestGetFaultMessageCoverage(t *testing.T) {
	device := EDIECL2018{}
	header := &helper.HeaderRecord{
		Model:       helper.Ecl2018,
		MaxChannels: 16,
		CommVersion: "27",
	}

	t.Run("uncovered_fault_codes", func(t *testing.T) {
		// Test fault code 11 which should be undefined
		msg, statusMsg := device.getFaultMessage(11, 0, header)
		assert.Equal(t, "undefined fault type", msg)
		assert.NotNil(t, statusMsg)

		// Test fault code 26 which is actually defined
		msg, statusMsg = device.getFaultMessage(26, 0, header)
		assert.Equal(t, "AC Line Frequency Fault", msg)
		assert.NotNil(t, statusMsg)
	})

	t.Run("fault_8_different_status", func(t *testing.T) {
		// Test fault code 8 which returns empty message but has status
		msg, statusMsg := device.getFaultMessage(8, 1430, header)
		assert.Equal(t, "", msg)
		assert.Equal(t, "(RMS Engine A/D error)", statusMsg)
	})

	t.Run("fault_status_default_case", func(t *testing.T) {
		// Test default case for fault status (covers uncovered lines)
		msg, statusMsg := device.getFaultMessage(8, 999, header)
		assert.Equal(t, "", msg)       // fault code 8 always returns empty message
		assert.Equal(t, "", statusMsg) // default case returns empty status
	})

	t.Run("fault_status_all_switch_cases", func(t *testing.T) {
		// Cover lines 139-148: all fault status switch cases
		testCases := []struct {
			status   int
			expected string
		}{
			{2000, "(EEprom error)"},             // line 139-140
			{2500, "(program card serial path)"}, // line 141-142
			{3330, "(switch serial path)"},       // line 143-144
			{5000, "(display serial path)"},      // line 145-146
			{10000, "(logic serial path)"},       // line 147-148
		}

		for _, tc := range testCases {
			msg, statusMsg := device.getFaultMessage(8, tc.status, header)
			assert.Equal(t, "", msg) // fault code 8 always returns empty message
			assert.Equal(t, tc.expected, statusMsg)
		}
	})
}

func TestRMSStatusErrorPaths(t *testing.T) {
	device := EDIECL2018{}
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}

	t.Run("nil_header", func(t *testing.T) {
		// Covers line 75-77: nil header check
		data := make([]byte, 50)
		_, err := device.RMSStatus(httpHeader, data, nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "header is nil")
	})

	t.Run("fault_message_edge_cases", func(t *testing.T) {
		// Cover lines 101-103: edge cases in getFaultMessage
		header := &helper.HeaderRecord{
			Model:       helper.Ecl2018,
			MaxChannels: 16,
			CommVersion: "27",
		}

		// Test fault code that doesn't exist
		msg, statusMsg := device.getFaultMessage(99, 0, header)
		assert.Equal(t, "undefined fault type", msg)
		assert.Equal(t, "", statusMsg)
	})

	t.Run("byte_length_validation_error", func(t *testing.T) {
		// Cover lines 75-77: byte length validation error
		header := &helper.HeaderRecord{
			Model:       helper.Ecl2018,
			MaxChannels: 16,
		}

		// Create data that's too short to even trigger checksum validation
		shortData := make([]byte, 5) // Way too short for RMS status
		_, err := device.RMSStatus(httpHeader, shortData, header)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "checksum failure")
	})

	t.Run("timestamp_parsing_error", func(t *testing.T) {
		// Cover lines 101-103: timestamp parsing error
		header := &helper.HeaderRecord{
			Model:       helper.Ecl2018,
			MaxChannels: 16,
		}

		// Create proper length data but with invalid BCD timestamp
		data := make([]byte, HeaderLength+Get2018RmsStatusLength)

		// Set invalid BCD values that will cause datetime parsing to fail
		// Based on ConvertBCDBytesToDateTimeII call in parseRMSStatusRecord:
		// byteMsg[26], byteMsg[25] = month, day
		// byteMsg[27], byteMsg[24] = year, hour  
		// byteMsg[23], byteMsg[22] = minute, second
		data[HeaderLength+22] = 0xFF // Invalid BCD second
		data[HeaderLength+23] = 0xFF // Invalid BCD minute
		data[HeaderLength+24] = 0xFF // Invalid BCD hour
		data[HeaderLength+25] = 0xFF // Invalid BCD day
		data[HeaderLength+26] = 0xFF // Invalid BCD month (too high)
		data[HeaderLength+27] = 0xFF // Invalid BCD year

		// Fill other bytes with valid data (avoiding the timestamp bytes)
		for i := HeaderLength; i < len(data)-1; i++ {
			if i < HeaderLength+22 || i > HeaderLength+27 {
				data[i] = byte((i % 50) + 1)
			}
		}
		
		// Calculate checksum (checksum is the last byte)
		var sum uint8
		for i := 0; i < len(data)-1; i++ {
			sum += data[i]
		}
		data[len(data)-1] = ^sum

		_, err := device.RMSStatus(httpHeader, data, header)
		assert.Error(t, err)
		// The error should be a BCD parsing error from ConvertBCDBytesToDateTimeII
		assert.Contains(t, err.Error(), "invalid BCD byte")
		// This covers the timestamp parsing error path (lines 101-103)
	})

	t.Run("byte_length_validation_error", func(t *testing.T) {
		// Cover lines 75-77: byte length validation error in RMSStatus
		header := &helper.HeaderRecord{
			Model:       helper.Ecl2018,
			MaxChannels: 16,
		}
		
		// Create data that passes checksum validation but fails byte length validation
		shortData := make([]byte, 20) // Too short for RMS Status
		
		// Calculate valid checksum for this short array
		var sum uint8
		for i := 0; i < len(shortData)-1; i++ {
			shortData[i] = byte(i + 5) // Some test data
			sum += shortData[i]
		}
		shortData[len(shortData)-1] = ^sum // Valid checksum
		
		_, err := device.RMSStatus(httpHeader, shortData, header)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "should be")
	})
}
