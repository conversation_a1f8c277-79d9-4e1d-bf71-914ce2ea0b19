package ediecl2018

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// Test to cover the missing lines 123-125, 143-145, and 149-166 in parseConfigurationChangeRecord
func TestEDIECL2018_ParseConfigurationChangeRecord_MissingCoverage(t *testing.T) {
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}

	// Test 1: Invalid record length (lines 123-125)
	t.Run("invalid_record_length", func(t *testing.T) {
		shortData := make([]byte, 50) // Too short, should be CF2018LogLength (85)
		record, err := parseConfigurationChangeRecord(httpHeader, shortData)
		assert.Error(t, err)
		assert.Nil(t, record)
		assert.Contains(t, err.Error(), "invalid record data length")
	})

	// Test 2: ParsePermissives error (lines 143-145) - NOW TESTABLE!
	t.Run("parse_permissives_error", func(t *testing.T) {
		// Save original function
		origParsePermissives := parseECL2018PermissivesFunc
		
		// Override with mock that returns an error
		parseECL2018PermissivesFunc = func(data []byte, channelCount int) ([][]string, error) {
			return nil, fmt.Errorf("not enough bytes for ECL2018 conflict map: have %d bytes, need 54", len(data))
		}
		
		// Restore original function after test
		defer func() {
			parseECL2018PermissivesFunc = origParsePermissives
		}()
		
		recordData := make([]byte, CF2018LogLength)
		
		// Set valid BCD timestamp (VB6 offsets: TimeOffset=78, DateOffset=81)
		recordData[78] = 0x30 // Second = 30 (TimeOffset + 0)
		recordData[79] = 0x45 // Minute = 45 (TimeOffset + 1)
		recordData[80] = 0x14 // Hour = 14 (TimeOffset + 2)
		recordData[81] = 0x21 // Day = 21 (DateOffset + 0)
		recordData[82] = 0x06 // Month = 06 (DateOffset + 1)
		recordData[83] = 0x23 // Year = 23 (DateOffset + 2)
		
		record, err := parseConfigurationChangeRecord(httpHeader, recordData)
		
		// This should now trigger the error branch at lines 143-145
		assert.Error(t, err)
		assert.Nil(t, record)
		assert.Contains(t, err.Error(), "failed to parse conflict map")
	})

	// Test 3: Insufficient permissives (lines 149-166)
	t.Run("insufficient_permissives", func(t *testing.T) {
		// Save original function
		origParsePermissives := parseECL2018PermissivesFunc
		
		// Override with mock that returns fewer than 18 channels
		parseECL2018PermissivesFunc = func(data []byte, channelCount int) ([][]string, error) {
			// Return only 10 channels instead of 18
			return make([][]string, 10), nil
		}
		
		// Restore original function after test
		defer func() {
			parseECL2018PermissivesFunc = origParsePermissives
		}()
		
		recordData := make([]byte, CF2018LogLength)
		
		// Set valid BCD timestamp (VB6 offsets: TimeOffset=78, DateOffset=81)
		recordData[78] = 0x30 // Second = 30 (TimeOffset + 0)
		recordData[79] = 0x45 // Minute = 45 (TimeOffset + 1)
		recordData[80] = 0x14 // Hour = 14 (TimeOffset + 2)
		recordData[81] = 0x21 // Day = 21 (DateOffset + 0)
		recordData[82] = 0x06 // Month = 06 (DateOffset + 1)
		recordData[83] = 0x23 // Year = 23 (DateOffset + 2)
		
		record, err := parseConfigurationChangeRecord(httpHeader, recordData)
		assert.NoError(t, err)
		assert.NotNil(t, record)
		
		// This should trigger the else branch at lines 149-166
		// All channel permissives should be empty slices
		assert.Equal(t, []string{}, record.Ch01Permissives)
		assert.Equal(t, []string{}, record.Ch02Permissives)
		assert.Equal(t, []string{}, record.Ch03Permissives)
		assert.Equal(t, []string{}, record.Ch04Permissives)
		assert.Equal(t, []string{}, record.Ch05Permissives)
		assert.Equal(t, []string{}, record.Ch06Permissives)
		assert.Equal(t, []string{}, record.Ch07Permissives)
		assert.Equal(t, []string{}, record.Ch08Permissives)
		assert.Equal(t, []string{}, record.Ch09Permissives)
		assert.Equal(t, []string{}, record.Ch10Permissives)
		assert.Equal(t, []string{}, record.Ch11Permissives)
		assert.Equal(t, []string{}, record.Ch12Permissives)
		assert.Equal(t, []string{}, record.Ch13Permissives)
		assert.Equal(t, []string{}, record.Ch14Permissives)
		assert.Equal(t, []string{}, record.Ch15Permissives)
		assert.Equal(t, []string{}, record.Ch16Permissives)
	})

	// Test 4: Sufficient permissives (lines 147-148 + 149-166 success path)
	t.Run("sufficient_permissives_success", func(t *testing.T) {
		// Save original function
		origParsePermissives := parseECL2018PermissivesFunc
		
		// Don't mock - use the real function to test actual behavior
		// The real parseECL2018Permissives will return empty results for test data
		// This tests the actual code path rather than fake behavior
		
		// Restore original function after test
		defer func() {
			parseECL2018PermissivesFunc = origParsePermissives
		}()
		
		recordData := make([]byte, CF2018LogLength)
		
		// Set valid BCD timestamp (VB6 offsets: TimeOffset=78, DateOffset=81)
		recordData[78] = 0x30 // Second = 30 (TimeOffset + 0)
		recordData[79] = 0x45 // Minute = 45 (TimeOffset + 1)
		recordData[80] = 0x14 // Hour = 14 (TimeOffset + 2)
		recordData[81] = 0x21 // Day = 21 (DateOffset + 0)
		recordData[82] = 0x06 // Month = 06 (DateOffset + 1)
		recordData[83] = 0x23 // Year = 23 (DateOffset + 2)
		
		record, err := parseConfigurationChangeRecord(httpHeader, recordData)
		assert.NoError(t, err)
		assert.NotNil(t, record)
		
		// This should trigger the main branch at lines 147-148 + success path
		// All channel permissives should have the test data
		// ECL2018 test data also has all bits=0 (all permissive) - same as KCL2018!
		assert.Equal(t, []string{"2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch01Permissives)
		assert.Equal(t, []string{"3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch02Permissives)
		assert.Equal(t, []string{"4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch03Permissives)
		assert.Equal(t, []string{"5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch04Permissives)
		assert.Equal(t, []string{"6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch05Permissives)
		assert.Equal(t, []string{"7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch06Permissives)
		assert.Equal(t, []string{"8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch07Permissives)
		assert.Equal(t, []string{"9", "10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch08Permissives)
		assert.Equal(t, []string{"10", "11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch09Permissives)
		assert.Equal(t, []string{"11", "12", "13", "14", "15", "16", "17", "18"}, record.Ch10Permissives)
		assert.Equal(t, []string{"12", "13", "14", "15", "16", "17", "18"}, record.Ch11Permissives)
		assert.Equal(t, []string{"13", "14", "15", "16", "17", "18"}, record.Ch12Permissives)
		assert.Equal(t, []string{"14", "15", "16", "17", "18"}, record.Ch13Permissives)
		assert.Equal(t, []string{"15", "16", "17", "18"}, record.Ch14Permissives)
		assert.Equal(t, []string{"16", "17", "18"}, record.Ch15Permissives)
		assert.Equal(t, []string{"17", "18"}, record.Ch16Permissives)
		assert.Equal(t, []string{"18"}, record.Ch17Permissives)
	})
}

// Comprehensive tests for LogConfiguration function to achieve 100% coverage
func TestEDIECL2018_LogConfiguration_CompleteCoverage(t *testing.T) {
	device := EDIECL2018{}
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}

	t.Run("nil_header", func(t *testing.T) {
		byteMsg := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x00, 0x00} // Header + 0 records + checksum
		result, err := device.LogConfiguration(httpHeader, byteMsg, nil)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "header is nil")
	})

	t.Run("checksum_error", func(t *testing.T) {
		header := &helper.HeaderRecord{MonitorId: 123}
		byteMsg := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x00, 0xFF} // Bad checksum
		result, err := device.LogConfiguration(httpHeader, byteMsg, header)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "checksum")
	})

	t.Run("zero_records", func(t *testing.T) {
		header := &helper.HeaderRecord{MonitorId: 123}
		byteMsg := make([]byte, HeaderLength+1+1)                            // Header + count + checksum
		copy(byteMsg[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}) // Header
		byteMsg[7] = 0                                                       // 0 records

		sum := byte(0)
		for i := 0; i < len(byteMsg)-1; i++ {
			sum += byteMsg[i]
		}
		byteMsg[len(byteMsg)-1] = ^sum // One's complement checksum

		result, err := device.LogConfiguration(httpHeader, byteMsg, header)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, 0, len(result.Record))
		assert.Equal(t, DeviceModel, result.DeviceModel)
		assert.Equal(t, byteMsg, result.RawMessage)
	})

	t.Run("invalid_byte_length", func(t *testing.T) {
		header := &helper.HeaderRecord{MonitorId: 123}
		byteMsg := make([]byte, HeaderLength+1+10)                           // Too short for 1 record
		copy(byteMsg[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}) // Header
		byteMsg[7] = 1                                                       // 1 record (but not enough bytes)

		sum := byte(0)
		for i := 0; i < len(byteMsg)-1; i++ {
			sum += byteMsg[i]
		}
		byteMsg[len(byteMsg)-1] = ^sum // One's complement checksum

		result, err := device.LogConfiguration(httpHeader, byteMsg, header)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "byte length")
	})

	t.Run("robust_bcd_handling", func(t *testing.T) {
		header := &helper.HeaderRecord{MonitorId: 123}
		// Create message with 1 record but invalid BCD timestamp data
		byteMsg := make([]byte, HeaderLength+1+CF2018LogLength+1)            // Header + count + 1 record + checksum
		copy(byteMsg[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}) // Header
		byteMsg[7] = 1                                                       // 1 record

		recordStart := 8
		for i := 0; i < CF2018LogLength; i++ {
			byteMsg[recordStart+i] = 0xFF // Invalid BCD
		}
		
		// Calculate checksum
		sum := byte(0)
		for i := 0; i < len(byteMsg)-1; i++ {
			sum += byteMsg[i]
		}
		byteMsg[len(byteMsg)-1] = ^sum // One's complement checksum
		
		result, err := device.LogConfiguration(httpHeader, byteMsg, header)
		// Should succeed with robust BCD handling
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.Record, 1)
		
		// Should use fallback timestamp (Unix epoch)
		expectedTime := time.Unix(0, 0).UTC()
		assert.Equal(t, expectedTime, result.Record[0].DateTime)
	})

	t.Run("single_record_success", func(t *testing.T) {
		header := &helper.HeaderRecord{MonitorId: 123}
		// Create message with 1 valid record
		byteMsg := make([]byte, HeaderLength+1+CF2018LogLength+1)            // Header + count + 1 record + checksum
		copy(byteMsg[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}) // Header
		byteMsg[7] = 1                                                       // 1 record

		recordStart := 8
		for i := 0; i < CF2018LogLength; i++ {
			byteMsg[recordStart+i] = byte(i % 256)
		}
		
		// Set valid BCD timestamp (VB6 offsets: TimeOffset=78, DateOffset=81)
		byteMsg[recordStart+78] = 0x30 // Second = 30 (TimeOffset + 0)
		byteMsg[recordStart+79] = 0x45 // Minute = 45 (TimeOffset + 1)
		byteMsg[recordStart+80] = 0x14 // Hour = 14 (TimeOffset + 2)
		byteMsg[recordStart+81] = 0x21 // Day = 21 (DateOffset + 0)
		byteMsg[recordStart+82] = 0x06 // Month = 06 (DateOffset + 1)
		byteMsg[recordStart+83] = 0x23 // Year = 23 (DateOffset + 2)
		
		// Calculate checksum
		sum := byte(0)
		for i := 0; i < len(byteMsg)-1; i++ {
			sum += byteMsg[i]
		}
		byteMsg[len(byteMsg)-1] = ^sum // One's complement checksum
		
		result, err := device.LogConfiguration(httpHeader, byteMsg, header)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, 1, len(result.Record))
		assert.Equal(t, DeviceModel, result.DeviceModel)
		assert.Equal(t, byteMsg, result.RawMessage)
	})

	t.Run("multiple_records_success", func(t *testing.T) {
		header := &helper.HeaderRecord{MonitorId: 123}
		numRecords := 2
		// Create message with 2 records
		byteMsg := make([]byte, HeaderLength+1+(numRecords*CF2018LogLength)+1) // Header + count + 2 records + checksum
		copy(byteMsg[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}) // Header
		byteMsg[7] = byte(numRecords) // 2 records
		
		// Fill both records with valid data
		copy(byteMsg[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07})   // Header
		byteMsg[7] = byte(numRecords)                                          // 2 records

		for recordIdx := 0; recordIdx < numRecords; recordIdx++ {
			recordStart := 8 + (recordIdx * CF2018LogLength)
			for i := 0; i < CF2018LogLength; i++ {
				byteMsg[recordStart+i] = byte((i + recordIdx) % 256)
			}
			
			// Set valid BCD timestamp for each record (VB6 offsets: TimeOffset=78, DateOffset=81)
			byteMsg[recordStart+78] = 0x30 // Second = 30 (TimeOffset + 0)
			byteMsg[recordStart+79] = 0x45 // Minute = 45 (TimeOffset + 1)
			byteMsg[recordStart+80] = 0x14 // Hour = 14 (TimeOffset + 2)
			byteMsg[recordStart+81] = 0x21 // Day = 21 (DateOffset + 0)
			byteMsg[recordStart+82] = 0x06 // Month = 06 (DateOffset + 1)
			byteMsg[recordStart+83] = 0x23 // Year = 23 (DateOffset + 2)
		}
		
		// Calculate checksum
		sum := byte(0)
		for i := 0; i < len(byteMsg)-1; i++ {
			sum += byteMsg[i]
		}
		byteMsg[len(byteMsg)-1] = ^sum // One's complement checksum
		
		result, err := device.LogConfiguration(httpHeader, byteMsg, header)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, numRecords, len(result.Record))
		assert.Equal(t, DeviceModel, result.DeviceModel)
		assert.Equal(t, byteMsg, result.RawMessage)
		
		// Verify both records were parsed
		for i := 0; i < numRecords; i++ {
			assert.NotNil(t, result.Record[i])
		}
	})

	// Test 5: Configuration parameters parsing (bytes 73-77)
	t.Run("configuration_parameters_parsing", func(t *testing.T) {
		recordData := make([]byte, CF2018LogLength)
		
		// Set valid BCD timestamp (VB6 offsets: TimeOffset=78, DateOffset=81)
		recordData[78] = 0x30 // Second = 30 (TimeOffset + 0)
		recordData[79] = 0x45 // Minute = 45 (TimeOffset + 1)
		recordData[80] = 0x14 // Hour = 14 (TimeOffset + 2)
		recordData[81] = 0x21 // Day = 21 (DateOffset + 0)
		recordData[82] = 0x06 // Month = 06 (DateOffset + 1)
		recordData[83] = 0x23 // Year = 23 (DateOffset + 2)
		
		// Set configuration parameters at bytes 73-77

		recordData[73] = 0x01 // WDEnableOffset: Watchdog enabled
		recordData[74] = 0xE5 // Options1: Multiple flags set (0xE5 = 229 = 11100101 binary)
		// Bits: 0=RF timing(1), 1=RP disabled(0), 2=WD timing(1), 3=GY enabled(0), 5=LEDguard(1), 6=RF SSM(1), 7=unused(1)
		recordData[75] = 0x0F // Options2: All FYA channels enabled (0x0F = 00001111)
		recordData[76] = 0x1E // Select1: Multiple flags set (0x1E = 30 = 00011110 binary)
		recordData[77] = 0x03 // Select2: Multiple flags set (0x03 = 3 = 00000011 binary)
		// Bits: 0=EE polarity(1), 1=Dual timing(1), 6=FYA fault(0)

		record, err := parseConfigurationChangeRecord(httpHeader, recordData)
		assert.NoError(t, err)
		assert.NotNil(t, record)

		// Verify configuration parameter parsing
		assert.True(t, record.WatchdogEnableSwitch)
		assert.Equal(t, "1200-1500 ms", record.RedFaultTiming)
		assert.True(t, record.RecurrentPulse) // bit 1=0 means enabled
		assert.Equal(t, "1 second", record.WatchdogTiming)
		assert.False(t, record.GYEnable) // bit 3=0 means disabled
		assert.True(t, record.LEDguardThresholds)
		assert.True(t, record.RedFailEnabledbySSM)

		// FYA should have all channels enabled
		expectedFYA := []string{"1-9", "3-10", "5-11", "7-12"}
		assert.Equal(t, expectedFYA, record.FlashingYellowArrows)

		assert.True(t, record.WDTErrorClearonPU) // bit 0=0 means enabled
		assert.True(t, record.MinimumFlash)
		assert.True(t, record.ConfigChangeFault)
		assert.True(t, record.RedCableFault)
		assert.Equal(t, "98 +/- 2 Vrms", record.AcLineBrownout)

		assert.Equal(t, "INVERT", record.PinEEPolarity)
		assert.Equal(t, "700-1000 ms", record.DualIndicationFaultTiming)
		assert.True(t, record.FYAFlashRateFault) // bit 6=0 means enabled
	})
}

// Test the new ECL2018-specific permissives parsing function
func TestParseECL2018Permissives(t *testing.T) {
	t.Run("basic_functionality", func(t *testing.T) {
		// Test data: 54 bytes of conflict map with some bits clear (permissive)
		data := make([]byte, 54)
		// Set some bits to 0 to create permissives (VB6 logic: bit=0 means permissive)
		// For primary channel 1, set bit for secondary channel 2 to 0 (permissive)
		// Bits are in big-endian format: byte0 << 16 | byte1 << 8 | byte2
		// Primary channel 1 uses bytes 0-2, secondary channel 2 needs bit 1 (mask=2) to be clear
		data[2] = 0xFD // 11111101 - bit 1 is clear, so channel 1 → 2 is permissive

		result, err := parseECL2018Permissives(data, 18)
		assert.NoError(t, err)
		assert.Equal(t, 17, len(result)) // 17 primary channels (1-17)

		// Check that channel 1 has channel 2 as permissive
		assert.Contains(t, result[0], "2") // Primary channel 1 (index 0) should have secondary channel 2
	})

	t.Run("channel_count_validation", func(t *testing.T) {
		data := make([]byte, 54)

		// Test channel count less than 2
		_, err := parseECL2018Permissives(data, 1)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "channelCount must be at least 2")
	})

	t.Run("error_cases", func(t *testing.T) {
		// Test insufficient data
		_, err := parseECL2018Permissives(make([]byte, 10), 18)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not enough bytes for ECL2018 conflict map")
	})
}

// Test to cover the missing line in LogConfiguration function where parseConfigurationChangeRecord fails
func TestEDIECL2018_LogConfiguration_ParseError(t *testing.T) {
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId: 123,
	}
	
	// Save original function
	origParsePermissives := parseECL2018PermissivesFunc
	
	// Override with mock that returns an error to trigger parseConfigurationChangeRecord failure
	parseECL2018PermissivesFunc = func(data []byte, channelCount int) ([][]string, error) {
		return nil, fmt.Errorf("mock error for testing")
	}
	
	// Restore original function after test
	defer func() {
		parseECL2018PermissivesFunc = origParsePermissives
	}()
	
	// Create a message with 1 record
	numRecords := 1
	totalLength := HeaderLength + ConfigurationChangeLogRecordsCountLength + (numRecords * CF2018LogLength) + ChecksumLength
	byteMsg := make([]byte, totalLength)
	
	// Set valid header
	copy(byteMsg[0:HeaderLength], []byte{0x01, 0x02, 0x03, 0x04, 0x05})
	
	// Set number of records
	byteMsg[ConfigurationChangeLogRecordsCountOffset] = byte(numRecords)
	
	// Set valid timestamp data (won't matter since permissives will fail)
	recordStart := ConfigurationChangeLogRecordsStartOffset
	byteMsg[recordStart+78] = 0x30 // Valid second
	byteMsg[recordStart+79] = 0x45 // Valid minute
	byteMsg[recordStart+80] = 0x14 // Valid hour
	byteMsg[recordStart+81] = 0x15 // Valid day
	byteMsg[recordStart+82] = 0x06 // Valid month
	byteMsg[recordStart+83] = 0x23 // Valid year

	// Calculate and set checksum
	sum := byte(0)
	for i := 0; i < len(byteMsg)-1; i++ {
		sum += byteMsg[i]
	}
	byteMsg[len(byteMsg)-1] = ^sum // One's complement checksum

	device := EDIECL2018{}
	result, err := device.LogConfiguration(httpHeader, byteMsg, header)

	// Should get error from parseConfigurationChangeRecord
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to parse configuration change record 1")
}