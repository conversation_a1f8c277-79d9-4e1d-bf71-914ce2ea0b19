package helper

import (
	"testing"
	"time"

	"cloud.google.com/go/pubsub"

	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"

	"github.com/stretchr/testify/assert"
)

func Test_LogMonitorResetRecord_ToBigQuerySchema(t *testing.T) {
	tests := []struct {
		name     string
		record   LogMonitorResetRecord
		expected schemas.LogMonitorResetRecord
	}{
		{
			name: "basic conversion",
			record: LogMonitorResetRecord{
				DateTime:  time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				ResetType: "test_reset",
			},
			expected: schemas.LogMonitorResetRecord{
				EventTimestamp: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				ResetType:      "test_reset",
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.<PERSON>()
			result := tt.record.ToBigQuerySchema()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_LogACLineEventRecord_ToBigQuerySchema(t *testing.T) {
	tests := []struct {
		name     string
		record   LogACLineEventRecord
		expected schemas.LogACLineEventRecord
	}{
		{
			name: "basic conversion",
			record: LogACLineEventRecord{
				EventType:       "test_event",
				DateTime:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				LineVoltageRms:  220,
				LineFrequencyHz: ptr(int32(50)),
			},
			expected: schemas.LogACLineEventRecord{
				EventType:       "test_event",
				DateTime:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				LineVoltageRms:  220,
				LineFrequencyHz: 50,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := tt.record.ToBigQuerySchema()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_LogPreviousFailRecord_ToBigQuerySchema(t *testing.T) {
	tests := []struct {
		name     string
		record   LogPreviousFailRecord
		expected schemas.LogPreviousFailRecord
	}{
		{
			name: "basic conversion",
			record: LogPreviousFailRecord{
				DateTime:                          time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Fault:                             "test_fault",
				AcLine:                            "test_acline",
				T48VDCSignalBus:                   "test_bus",
				RedEnable:                         "test_red",
				MCCoilEE:                          "test_coil",
				SpecialFunction1:                  "test_sf1",
				SpecialFunction2:                  "test_sf2",
				WDTMonitor:                        "test_wdt",
				T24VDCInput:                       "test_input",
				Temperature:                       25,
				LsFlashBit:                        true,
				FaultStatus:                       []bool{true, false},
				ChannelGreenStatus:                []bool{true, false},
				ChannelYellowStatus:               []bool{true, false},
				ChannelRedStatus:                  []bool{true, false},
				ChannelWalkStatus:                 []bool{true, false},
				ChannelGreenFieldCheckStatus:      []bool{true, false},
				ChannelYellowFieldCheckStatus:     []bool{true, false},
				ChannelRedFieldCheckStatus:        []bool{true, false},
				ChannelWalkFieldCheckStatus:       []bool{true, false},
				ChannelGreenRecurrentPulseStatus:  []bool{true, false},
				ChannelYellowRecurrentPulseStatus: []bool{true, false},
				ChannelRedRecurrentPulseStatus:    []bool{true, false},
				ChannelWalkRecurrentPulseStatus:   []bool{true, false},
				ChannelGreenRmsVoltage:            []int32{220, 230},
				ChannelYellowRmsVoltage:           []int32{220, 230},
				ChannelRedRmsVoltage:              []int32{220, 230},
				ChannelWalkRmsVoltage:             []int32{220, 230},
				NextConflictingChannels:           []bool{true, false},
				ChannelRedCurrentStatus:           []bool{true, false},
				ChannelYellowCurrentStatus:        []bool{true, false},
				ChannelGreenCurrentStatus:         []bool{true, false},
				ChannelRedRmsCurrent:              []int32{10, 20},
				ChannelYellowRmsCurrent:           []int32{10, 20},
				ChannelGreenRmsCurrent:            []int32{10, 20},
			},
			expected: schemas.LogPreviousFailRecord{
				DateTime:                          time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Fault:                             "test_fault",
				ACLine:                            "test_acline",
				T48VDCSignalBus:                   "test_bus",
				RedEnable:                         "test_red",
				MCCoilEE:                          "test_coil",
				SpecialFunction1:                  "test_sf1",
				SpecialFunction2:                  "test_sf2",
				WDTMonitor:                        "test_wdt",
				T24VDCInput:                       "test_input",
				Temperature:                       25,
				LsFlashBit:                        true,
				FaultStatus:                       []bool{true, false},
				ChannelGreenStatus:                []bool{true, false},
				ChannelYellowStatus:               []bool{true, false},
				ChannelRedStatus:                  []bool{true, false},
				ChannelWalkStatus:                 []bool{true, false},
				ChannelGreenFieldCheckStatus:      []bool{true, false},
				ChannelYellowFieldCheckStatus:     []bool{true, false},
				ChannelRedFieldCheckStatus:        []bool{true, false},
				ChannelWalkFieldCheckStatus:       []bool{true, false},
				ChannelGreenRecurrentPulseStatus:  []bool{true, false},
				ChannelYellowRecurrentPulseStatus: []bool{true, false},
				ChannelRedRecurrentPulseStatus:    []bool{true, false},
				ChannelWalkRecurrentPulseStatus:   []bool{true, false},
				ChannelGreenRmsVoltage:            []int64{220, 230},
				ChannelYellowRmsVoltage:           []int64{220, 230},
				ChannelRedRmsVoltage:              []int64{220, 230},
				ChannelWalkRmsVoltage:             []int64{220, 230},
				NextConflictingChannels:           []bool{true, false},
				ChannelRedCurrentStatus:           []bool{true, false},
				ChannelYellowCurrentStatus:        []bool{true, false},
				ChannelGreenCurrentStatus:         []bool{true, false},
				ChannelRedRmsCurrent:              []int64{10, 20},
				ChannelYellowRmsCurrent:           []int64{10, 20},
				ChannelGreenRmsCurrent:            []int64{10, 20},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := tt.record.ToBigQuerySchema()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_TraceBuffer_ToBigQuerySchema(t *testing.T) {
	tests := []struct {
		name     string
		record   TraceBuffer
		expected schemas.TraceBuffer
	}{
		{
			name: "basic conversion",
			record: TraceBuffer{
				BufferRawBytes: []byte{1, 2, 3},
				Timestamp:      1234567890,
				Reds:           []bool{true, false},
				Yellows:        []bool{true, false},
				Greens:         []bool{true, false},
				Walks:          []bool{true, false},
				EE_SF_RE:       true,
				AcVoltage:      220,
			},
			expected: schemas.TraceBuffer{
				BufferRawBytes: []byte{1, 2, 3},
				Timestamp:      1234567890,
				Reds:           []bool{true, false},
				Yellows:        []bool{true, false},
				Greens:         []bool{true, false},
				Walks:          []bool{true, false},
				EE_SF_RE:       true,
				AcVoltage:      220,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := tt.record.ToBigQuerySchema()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_ConfigurationChangeLogRecord_ToBigQuerySchema(t *testing.T) {
	tests := []struct {
		name     string
		record   ConfigurationChangeLogRecord
		expected schemas.ConfigurationChangeLogRecord
	}{
		{
			name: "basic conversion",
			record: ConfigurationChangeLogRecord{
				DateTime:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Ch01Permissives: []string{"test1", "test2"},
				RedFailEnable:   []bool{true, false},
				RedVirtualChannel: []VirtualSetting{
					{
						Color:         "red",
						Enabled:       true,
						SourceChannel: 1,
						SourceColor:   "green",
					},
				},
			},
			expected: schemas.ConfigurationChangeLogRecord{
				DateTime:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Ch01Permissives: []string{"test1", "test2"},
				RedFailEnable:   []bool{true, false},
				RedVirtualChannel: []schemas.VirtualSetting{
					{
						Color:         "red",
						Enabled:       true,
						SourceChannel: 1,
						SourceColor:   "green",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := tt.record.ToBigQuerySchema()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_ConvertInt32SliceToInt64(t *testing.T) {
	tests := []struct {
		name     string
		input    []int32
		expected []int64
	}{
		{
			name:     "nil slice",
			input:    nil,
			expected: nil,
		},
		{
			name:     "empty slice",
			input:    []int32{},
			expected: []int64{},
		},
		{
			name:     "conversion",
			input:    []int32{1, 2, 3},
			expected: []int64{1, 2, 3},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := ConvertInt32SliceToInt64(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_convertVirtualSettings(t *testing.T) {
	tests := []struct {
		name     string
		input    []VirtualSetting
		expected []schemas.VirtualSetting
	}{
		{
			name:     "nil slice",
			input:    nil,
			expected: nil,
		},
		{
			name:     "empty slice",
			input:    []VirtualSetting{},
			expected: []schemas.VirtualSetting{},
		},
		{
			name: "conversion",
			input: []VirtualSetting{
				{
					Color:         "red",
					Enabled:       true,
					SourceChannel: 1,
					SourceColor:   "green",
				},
			},
			expected: []schemas.VirtualSetting{
				{
					Color:         "red",
					Enabled:       true,
					SourceChannel: 1,
					SourceColor:   "green",
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := convertVirtualSettings(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_NewLogMonitorResetBQConverter(t *testing.T) {
	tests := []struct {
		name          string
		httpHeader    *pubsubdata.HeaderDetails
		commonAttrs   *pubsubdata.CommonAttributes
		pubsubMessage *pubsub.Message
		translater    LogMonitorResetTranslater
		input         []byte
		expected      schemas.LogMonitorReset
		expectedError bool
	}{
		{
			name: "successful conversion",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*LogMonitorResetRecords, *HeaderRecord, error) {
				return &LogMonitorResetRecords{
						Records: []LogMonitorResetRecord{
							{
								DateTime:  time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
								ResetType: "test_reset",
							},
						},
						DeviceModel: "test_model",
						RawMessage:  []byte{1, 2, 3},
					}, &HeaderRecord{
						MonitorId: 1,
					}, nil
			},
			input: []byte{1, 2, 3},
			expected: schemas.LogMonitorReset{
				OrganizationIdentifier: "test_org",
				SoftwareGatewayID:      "test_gateway",
				TZ:                     "UTC",
				Topic:                  "test_topic",
				PubsubID:               "test_id",
				DeviceID:               "device 123",
				PubsubTimestamp:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Header: schemas.HeaderRecord{
					MonitorId: 1,
				},
				Records: []schemas.LogMonitorResetRecord{
					{
						EventTimestamp: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
						ResetType:      "test_reset",
					},
				},
				DeviceModel: "test_model",
				RawMessage:  []byte{1, 2, 3},
				LogUUID:     "123",
			},
			expectedError: false,
		},
		{
			name: "translater error",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*LogMonitorResetRecords, *HeaderRecord, error) {
				return nil, nil, assert.AnError
			},
			input:         []byte{1, 2, 3},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			converter := NewLogMonitorResetBQConverter(tt.httpHeader, tt.commonAttrs, tt.pubsubMessage, tt.translater, "123", "device 123")
			result, err := converter(tt.input)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, result)
				return
			}

			assert.NoError(t, err)
			assert.IsType(t, schemas.LogMonitorReset{}, result)
			logMonitorReset := result.(schemas.LogMonitorReset)
			assert.Equal(t, tt.expected, logMonitorReset)
		})
	}
}

func Test_NewLogACLineEventBQConverter(t *testing.T) {
	tests := []struct {
		name          string
		httpHeader    *pubsubdata.HeaderDetails
		commonAttrs   *pubsubdata.CommonAttributes
		pubsubMessage *pubsub.Message
		translater    LogACLineEventTranslater
		input         []byte
		expected      schemas.LogACLineEvent
		expectedError bool
	}{
		{
			name: "successful conversion",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*LogACLineEventRecords, *HeaderRecord, error) {
				return &LogACLineEventRecords{
						Records: []LogACLineEventRecord{
							{
								EventType:       "test_event",
								DateTime:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
								LineVoltageRms:  220,
								LineFrequencyHz: ptr(int32(50)),
							},
						},
						DeviceModel: "test_model",
						RawMessage:  []byte{1, 2, 3},
					}, &HeaderRecord{
						MonitorId: 1,
					}, nil
			},
			input: []byte{1, 2, 3},
			expected: schemas.LogACLineEvent{
				OrganizationIdentifier: "test_org",
				SoftwareGatewayID:      "test_gateway",
				TZ:                     "UTC",
				Topic:                  "test_topic",
				PubsubID:               "test_id",
				DeviceID:               "device 123",
				PubsubTimestamp:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Header: schemas.HeaderRecord{
					MonitorId: 1,
				},
				Record: []schemas.LogACLineEventRecord{
					{
						EventType:       "test_event",
						DateTime:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
						LineVoltageRms:  220,
						LineFrequencyHz: 50,
					},
				},
				DeviceModel: "test_model",
				RawMessage:  []byte{1, 2, 3},
				LogUUID:     "123",
				VoltageType: 0,
			},
			expectedError: false,
		},
		{
			name: "translater error",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*LogACLineEventRecords, *HeaderRecord, error) {
				return nil, nil, assert.AnError
			},
			input:         []byte{1, 2, 3},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			converter := NewLogACLineEventBQConverter(tt.httpHeader, tt.commonAttrs, tt.pubsubMessage, tt.translater, "123", "device 123")
			result, err := converter(tt.input)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, result)
				return
			}

			assert.NoError(t, err)
			assert.IsType(t, schemas.LogACLineEvent{}, result)
			logACLineEvent := result.(schemas.LogACLineEvent)
			assert.Equal(t, tt.expected, logACLineEvent)
		})
	}
}

func Test_NewLogPreviousFailBQConverter(t *testing.T) {
	tests := []struct {
		name          string
		httpHeader    *pubsubdata.HeaderDetails
		commonAttrs   *pubsubdata.CommonAttributes
		pubsubMessage *pubsub.Message
		translater    LogPreviousFailTranslater
		input         []byte
		expected      schemas.LogPreviousFail
		expectedError bool
	}{
		{
			name: "successful conversion",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*LogPreviousFailRecords, *HeaderRecord, error) {
				return &LogPreviousFailRecords{
						Records: []LogPreviousFailRecord{
							{
								DateTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
								Fault:    "test_fault",
								AcLine:   "test_acline",
							},
						},
						DeviceModel: "test_model",
						RawMessage:  []byte{1, 2, 3},
					}, &HeaderRecord{
						MonitorId: 1,
					}, nil
			},
			input: []byte{1, 2, 3},
			expected: schemas.LogPreviousFail{
				OrganizationIdentifier: "test_org",
				SoftwareGatewayID:      "test_gateway",
				TZ:                     "UTC",
				Topic:                  "test_topic",
				PubsubID:               "test_id",
				DeviceID:               "device 123",
				PubsubTimestamp:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Header: schemas.HeaderRecord{
					MonitorId: 1,
				},
				Records: []schemas.LogPreviousFailRecord{
					{
						DateTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
						Fault:    "test_fault",
						ACLine:   "test_acline",
					},
				},
				DeviceModel: "test_model",
				RawMessage:  []byte{1, 2, 3},
				LogUUID:     "123",
			},
			expectedError: false,
		},
		{
			name: "translater error",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*LogPreviousFailRecords, *HeaderRecord, error) {
				return nil, nil, assert.AnError
			},
			input:         []byte{1, 2, 3},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			converter := NewLogPreviousFailBQConverter(tt.httpHeader, tt.commonAttrs, tt.pubsubMessage, tt.translater, "123", "device 123")
			result, err := converter(tt.input)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, result)
				return
			}

			assert.NoError(t, err)
			assert.IsType(t, schemas.LogPreviousFail{}, result)
			logPreviousFail := result.(schemas.LogPreviousFail)
			assert.Equal(t, tt.expected, logPreviousFail)
		})
	}
}

func Test_NewFaultSignalSequenceBQConverter(t *testing.T) {
	tests := []struct {
		name          string
		httpHeader    *pubsubdata.HeaderDetails
		commonAttrs   *pubsubdata.CommonAttributes
		pubsubMessage *pubsub.Message
		translater    FaultSignalSequenceTranslater
		input         []byte
		expected      schemas.LogFaultSignalSequence
		expectedError bool
	}{
		{
			name: "successful conversion",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*FaultSignalSequenceRecords, *HeaderRecord, error) {
				return &FaultSignalSequenceRecords{
						Records: []TraceBuffer{
							{
								BufferRawBytes: []byte{1, 2, 3},
								Timestamp:      1234567890,
							},
						},
						DeviceModel: "test_model",
						RawMessage:  []byte{1, 2, 3},
						FaultType:   "test_fault",
					}, &HeaderRecord{
						MonitorId: 1,
					}, nil
			},
			input: []byte{1, 2, 3},
			expected: schemas.LogFaultSignalSequence{
				OrganizationIdentifier: "test_org",
				SoftwareGatewayID:      "test_gateway",
				TZ:                     "UTC",
				Topic:                  "test_topic",
				PubsubID:               "test_id",
				DeviceID:               "device 123",
				PubsubTimestamp:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Header:                 &schemas.HeaderRecord{MonitorId: 1},

				// The translator’s RawMessage + FaultType go into the nested slice instead:
				Records: []schemas.LogFaultSignalSequenceRecords{
					{
						TraceRawBytes: []byte{1, 2, 3},
						FaultType:     "test_fault",
						Records: []schemas.TraceBuffer{
							{
								BufferRawBytes: []byte{1, 2, 3},
								Timestamp:      1234567890,
								// other fields remain zero‐value
							},
						},
					},
				},

				DeviceModel: "test_model",
				LogUUID:     "123", // <—— now matches actual
			},
			expectedError: false,
		},
		{
			name: "translater error",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*FaultSignalSequenceRecords, *HeaderRecord, error) {
				return nil, nil, assert.AnError
			},
			input:         []byte{1, 2, 3},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			existing := &schemas.LogFaultSignalSequence{}
			converter := NewFaultSignalSequenceBQConverter(tt.httpHeader, tt.commonAttrs, tt.pubsubMessage, tt.translater, existing, "123", "device 123")
			result, err := converter(tt.input)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, result)
				return
			}

			assert.NoError(t, err)
			assert.IsType(t, schemas.LogFaultSignalSequence{}, result)
			logFaultSignalSequence := result.(schemas.LogFaultSignalSequence)
			assert.Equal(t, tt.expected, logFaultSignalSequence)
		})
	}
}

func Test_NewConfigurationChangeLogBQConverter(t *testing.T) {
	tests := []struct {
		name          string
		httpHeader    *pubsubdata.HeaderDetails
		commonAttrs   *pubsubdata.CommonAttributes
		pubsubMessage *pubsub.Message
		translater    ConfigurationChangeLogTranslater
		input         []byte
		expected      schemas.LogConfiguration
		expectedError bool
	}{
		{
			name: "successful conversion",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*ConfigurationChangeLogRecords, *HeaderRecord, error) {
				return &ConfigurationChangeLogRecords{
						Record: []ConfigurationChangeLogRecord{
							{
								DateTime:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
								Ch01Permissives: []string{"test1", "test2"},
							},
						},
						DeviceModel: "test_model",
						RawMessage:  []byte{1, 2, 3},
					}, &HeaderRecord{
						MonitorId: 1,
					}, nil
			},
			input: []byte{1, 2, 3},
			expected: schemas.LogConfiguration{
				OrganizationIdentifier: "test_org",
				SoftwareGatewayID:      "test_gateway",
				TZ:                     "UTC",
				Topic:                  "test_topic",
				PubsubID:               "test_id",
				DeviceID:               "device 123",
				PubsubTimestamp:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				Header: &schemas.HeaderRecord{
					MonitorId: 1,
				},
				Record: []schemas.ConfigurationChangeLogRecord{
					{
						DateTime:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
						Ch01Permissives: []string{"test1", "test2"},
					},
				},
				DeviceModel: "test_model",
				RawMessage:  []byte{1, 2, 3},
				LogUUID:     "123",
			},
			expectedError: false,
		},
		{
			name: "translater error",
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayDeviceID: "test_gateway",
				GatewayTimezone: "UTC",
			},
			commonAttrs: &pubsubdata.CommonAttributes{
				OrganizationIdentifier: "test_org",
				Topic:                  "test_topic",
			},
			pubsubMessage: &pubsub.Message{
				ID:          "test_id",
				PublishTime: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			translater: func(*pubsubdata.HeaderDetails, []byte) (*ConfigurationChangeLogRecords, *HeaderRecord, error) {
				return nil, nil, assert.AnError
			},
			input:         []byte{1, 2, 3},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			converter := NewConfigurationChangeLogBQConverter(tt.httpHeader, tt.commonAttrs, tt.pubsubMessage, tt.translater, "123", "device 123")
			result, err := converter(tt.input)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, result)
				return
			}

			assert.NoError(t, err)
			assert.IsType(t, schemas.LogConfiguration{}, result)
			logConfiguration := result.(schemas.LogConfiguration)
			assert.Equal(t, tt.expected, logConfiguration)
		})
	}
}

// Helper function to create a pointer to int32
func ptr(i int32) *int32 {
	return &i
}
