package edikcl2018

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetEventTypeKCL2018_AllCases(t *testing.T) {
	tests := []struct {
		eventType int
		expected  string
	}{
		{0, "AC Line Restoration"},
		{1, "AC Line Failure"},
		{2, "AC Line Brownout"},
		{3, "AC Line High"},          // Updated to match new implementation
		{4, "DC Line Low"},           // Updated to match new implementation
		{5, "DC Line High"},          // Updated to match new implementation
		{99, "Unknown Event Type 99"},
		{-1, "Unknown Event Type -1"},
	}

	for _, test := range tests {
		result := getEventTypeKCL2018(test.eventType)
		assert.Equal(t, test.expected, result, "getEventTypeKCL2018(%d)", test.eventType)
	}
}

func TestGetFaultTextKCL2018_AllCases(t *testing.T) {
	// Test all the defined cases from 0-47 plus some edge cases
	tests := []struct {
		faultCode int
		expected  string
	}{
		{0, "No Fault"},
		{1, "Red Fail"},
		{2, "Yellow Fail"},
		{3, "Green Fail"},
		{4, "Walk Fail"},
		{5, "Ped Clear Fail"},
		{6, "Conflict Monitor"},
		{7, "MMU Fault"},
		{8, "Local Flash"},
		{9, "Startup Flash"},
		{10, "Preempt"},
		{11, "Cabinet Door"},
		{12, "Red Cable Fault"},
		{13, "Configuration Change"},
		{14, "Sequence (Short Yellow)"},
		{15, "Recurrent Pulse Conflict"},
		{16, "Recurrent Pulse Dual"},
		{17, "24V Logic Fault"},
		{18, "Stop Time"},
		{19, "AC Fail"},
		{20, "Conflict Inhibit"},
		{21, "Walk Rest"},
		{22, "Call Rest"},
		{23, "Ped Rest"},
		{24, "Min Recall"},
		{25, "Max Recall"},
		{26, "Ped Recall"},
		{27, "Red Rest"},
		{28, "Program Card"},
		{29, "Manual Control"},
		{30, "Stop and Go"},
		{31, "Interval Advance"},
		{32, "External Start"},
		{33, "Not Used"},
		{34, "Coordination"},
		{35, "Section Control"},
		{36, "Interconnect"},
		{37, "Sign Control"},
		{38, "Speed Control"},
		{39, "Central Control"},
		{40, "Malfunction Management Unit"},
		{41, "Control Mode"},
		{42, "Conflict Flash"},
		{43, "RYG Voltage"},
		{44, "Phase Failure"},
		{45, "Detector Failure"},
		{46, "Power Monitor"},
		{47, "Standards Compliance"},
		{48, "Unknown Fault Code: 48"}, // Beyond defined cases
		{99, "Unknown Fault Code: 99"},
		{-1, "Unknown Fault Code: -1"},
	}

	for _, test := range tests {
		result := getFaultTextKCL2018(test.faultCode)
		assert.Equal(t, test.expected, result, "getFaultTextKCL2018(%d)", test.faultCode)
	}
}

// TestGetResetTypeKCL2018_AllCases removed - KCL2018 MonitorReset uses 6-byte structure without reset type field
// All records use generic "Monitor Reset" type, same as ECL2018


