package edikcl2018

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestEDIKCL2018_RMSEngineData_Success(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId:         123,
		CommVersion:       "27", // >= 0x27 condition
		FirmwareRevision:  "51", // > 0x50 condition
	}
	
	// Create valid RMS engine data (20 bytes: 2 records x 10 bytes each)
	data := make([]byte, 20)
	
	// Both records must have identical headers (first 7 bytes)
	headerBytes := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}
	
	// First 10-byte record (version record)
	copy(data[0:7], headerBytes)    // Same header
	data[7] = 0x08  // Version data
	data[8] = 0x09  // Version data
	var sum1 uint8
	for _, b := range data[0:9] {
		sum1 += b
	}
	data[9] = ^sum1
	
	// Second 10-byte record (revision record)
	copy(data[10:17], headerBytes)  // Same header as first record
	data[17] = 0x11 // Revision data
	data[18] = 0x12 // Revision data
	var sum2 uint8
	for _, b := range data[10:19] {
		sum2 += b
	}
	data[19] = ^sum2
	
	result, err := device.RMSEngineData(httpHeader, data, header)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, DeviceModel, result.DeviceModel)
}

func TestEDIKCL2018_MonitorIDandName_Success(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId:         456,
		CommVersion:       "27", // >= 0x27 condition
		FirmwareRevision:  "51", // > 0x50 condition
	}
	
	// Create valid monitor ID and name data (42 bytes for firmware >= 27)
	data := make([]byte, 50) // HeaderLength(7) + KCL2018IDNameLengthFirmware27(42) + ChecksumLength(1)
	copy(data[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x34, 0x12}) // Monitor ID in bytes 5,6 (ls, ms)
	copy(data[9:], []byte("KCL2018 Test Monitor"))

	// Calculate checksum
	var sum uint8
	for _, b := range data[:len(data)-1] {
		sum += b
	}
	data[len(data)-1] = ^sum
	
	result, err := device.MonitorIDandName(httpHeader, data, header)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, DeviceModel, result.DeviceModel)
	assert.Equal(t, int64(0x1234), result.MonitorId) // 0x12 << 8 | 0x34
	assert.Contains(t, result.MonitorName, "KCL2018")
}

func TestEDIKCL2018_RMSEngineData_InvalidLength(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId: 123,
	}
	
	// Invalid data length
	data := make([]byte, 10) // Too short
	
	result, err := device.RMSEngineData(httpHeader, data, header)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "byte length")
}

func TestEDIKCL2018_MonitorIDandName_NilHeader(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	data := make([]byte, 48)
	
	result, err := device.MonitorIDandName(httpHeader, data, nil)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "header is nil")
}

func TestEDIKCL2018_MonitorIDandName_OldFirmware(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	// Header with old firmware (doesn't meet >= 0x27 and > 0x50 conditions)
	header := &helper.HeaderRecord{
		MonitorId:         456,
		CommVersion:       "20", // < 0x27 condition
		FirmwareRevision:  "40", // < 0x50 condition
	}
	
	// Create valid monitor ID and name data for old firmware (40 bytes)
	data := make([]byte, 48) // HeaderLength(7) + KCL2018IDNameLength(40) + ChecksumLength(1)
	copy(data[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x34, 0x12}) // Monitor ID in bytes 5,6
	copy(data[9:], []byte("KCL2018 Test Monitor"))
	
	// Calculate checksum
	var sum uint8
	for _, b := range data[:len(data)-1] {
		sum += b
	}
	data[len(data)-1] = ^sum
	
	result, err := device.MonitorIDandName(httpHeader, data, header)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, DeviceModel, result.DeviceModel)
	// For old firmware, monitor ID and name should be 0 and empty
	assert.Equal(t, int64(0), result.MonitorId)
	assert.Equal(t, "", result.MonitorName)
}

func TestEDIKCL2018_RMSEngineData_ChecksumFailureFirstRecord(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId: 123,
	}
	
	// Create data with invalid checksum on first record
	data := make([]byte, 20)
	headerBytes := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}
	
	// First record with invalid checksum
	copy(data[0:7], headerBytes)
	data[7] = 0x08
	data[8] = 0x09
	data[9] = 0xFF // Invalid checksum
	
	// Second record with valid checksum
	copy(data[10:17], headerBytes)
	data[17] = 0x11
	data[18] = 0x12
	var sum2 uint8
	for _, b := range data[10:19] {
		sum2 += b
	}
	data[19] = ^sum2
	
	result, err := device.RMSEngineData(httpHeader, data, header)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "checksum")
}

func TestEDIKCL2018_RMSEngineData_ChecksumFailureSecondRecord(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId: 123,
	}
	
	// Create data with invalid checksum on second record
	data := make([]byte, 20)
	headerBytes := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}
	
	// First record with valid checksum
	copy(data[0:7], headerBytes)
	data[7] = 0x08
	data[8] = 0x09
	var sum1 uint8
	for _, b := range data[0:9] {
		sum1 += b
	}
	data[9] = ^sum1
	
	// Second record with invalid checksum
	copy(data[10:17], headerBytes)
	data[17] = 0x11
	data[18] = 0x12
	data[19] = 0xFF // Invalid checksum
	
	result, err := device.RMSEngineData(httpHeader, data, header)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "checksum")
}

func TestEDIKCL2018_RMSEngineData_HeaderMismatch(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId: 123,
	}
	
	// Create data with different headers for the two records
	data := make([]byte, 20)
	
	// First record with one header
	copy(data[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07})
	data[7] = 0x08
	data[8] = 0x09
	var sum1 uint8
	for _, b := range data[0:9] {
		sum1 += b
	}
	data[9] = ^sum1
	
	// Second record with different header
	copy(data[10:17], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x08}) // Different last byte
	data[17] = 0x11
	data[18] = 0x12
	var sum2 uint8
	for _, b := range data[10:19] {
		sum2 += b
	}
	data[19] = ^sum2
	
	result, err := device.RMSEngineData(httpHeader, data, header)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "headers do not match")
}

func TestEDIKCL2018_MonitorIDandName_ChecksumFailure(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId:         456,
		CommVersion:       "27",
		FirmwareRevision:  "51",
	}
	
	// Create data with invalid checksum
	data := make([]byte, 50)
	copy(data[0:7], []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x34, 0x12})
	copy(data[9:], []byte("KCL2018 Test Monitor"))
	data[len(data)-1] = 0xFF // Invalid checksum
	
	result, err := device.MonitorIDandName(httpHeader, data, header)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "checksum")
}

func TestEDIKCL2018_MonitorIDandName_InvalidLength(t *testing.T) {
	device := EDIKCL2018{}
	
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	
	header := &helper.HeaderRecord{
		MonitorId:         456,
		CommVersion:       "27",
		FirmwareRevision:  "51",
	}
	
	// Create data with wrong length
	data := make([]byte, 30) // Too short for expected 50 bytes
	
	// Calculate valid checksum
	var sum uint8
	for _, b := range data[:len(data)-1] {
		sum += b
	}
	data[len(data)-1] = ^sum
	
	result, err := device.MonitorIDandName(httpHeader, data, header)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "byte length")
}