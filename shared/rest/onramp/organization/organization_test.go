package organization

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/middlewares"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test helper to create a mock request with JSON body
func createMockRequest(method, url string, body interface{}) *http.Request {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req := httptest.NewRequest(method, url, reqBody)
	req.Header.Set("Content-Type", "application/json")
	return req
}

// Test parseCreateRequest function
func Test_parseCreateRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		requestBody interface{}
		expectedErr error
		wantErr     bool
		description string
	}{
		{
			name: "valid request",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "Test Organization",
				Description:       "Valid organization description",
				OrgTypeIdentifier: "municipality",
			},
			expectedErr: nil,
			wantErr:     false,
			description: "Valid organization description",
		},
		{
			name: "empty name",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "",
				Description:       "Valid description",
				OrgTypeIdentifier: "municipality",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "Valid description",
		},
		{
			name: "empty description",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "Test Org",
				Description:       "",
				OrgTypeIdentifier: "municipality",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "",
		},
		{
			name: "empty org type",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "Test Org",
				Description:       "Valid description",
				OrgTypeIdentifier: "",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "Valid description",
		},
		{
			name:        "invalid JSON",
			requestBody: "invalid json",
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
			description: "",
		},
		{
			name: "unexpected fields",
			requestBody: map[string]interface{}{
				"description": "Valid description",
				"unexpected":  "field",
			},
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
			description: "Valid description",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock request with test body
			req := createMockRequest("POST", "/organizations", tt.requestBody)

			// Execute the function under test
			result, err := parseCreateRequest(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.description, result.Description)
			}
		})
	}
}

// Test parseUpdateRequest function
func Test_parseUpdateRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		requestBody interface{}
		expectedErr error
		wantErr     bool
		description string
	}{
		{
			name: "valid request",
			requestBody: UpdateOrganizationRequest{
				Name:        "Test Organization",
				Description: "Valid organization description",
			},
			expectedErr: nil,
			wantErr:     false,
			description: "Valid organization description",
		},
		{
			name: "empty name",
			requestBody: UpdateOrganizationRequest{
				Name:        "",
				Description: "Valid description",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "Valid description",
		},
		{
			name: "empty description",
			requestBody: UpdateOrganizationRequest{
				Name:        "Test Org",
				Description: "",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "",
		},
		{
			name: "whitespace only name",
			requestBody: UpdateOrganizationRequest{
				Name:        "   ",
				Description: "Valid description",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "Valid description",
		},
		{
			name: "whitespace only description",
			requestBody: UpdateOrganizationRequest{
				Name:        "Test Org",
				Description: "   ",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "   ",
		},
		{
			name:        "invalid JSON",
			requestBody: "invalid json",
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
			description: "",
		},
		{
			name: "unexpected fields",
			requestBody: map[string]interface{}{
				"name":        "Test Org",
				"description": "Valid description",
				"unexpected":  "field",
			},
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
			description: "Valid description",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock request with test body
			req := createMockRequest("PUT", "/organizations/test-id", tt.requestBody)

			// Execute the function under test
			result, err := parseUpdateRequest(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.description, result.Description)
			}
		})
	}
}

// Test Organization.ToResponse method
func Test_Organization_ToResponse(t *testing.T) {
	t.Parallel()

	// Create test organization
	now := time.Now().UTC()
	testId := uuid.New()
	org := Organization{
		Id:                testId,
		Name:              "Test Organization",
		Description:       "Test Organization Description",
		OrgTypeIdentifier: "municipality",
		CreatedAt:         now,
		UpdatedAt:         now.Add(time.Hour),
		IsDeleted:         false,
	}

	// Execute the method under test
	response := org.ToResponse()

	// Assert all fields are correctly mapped
	assert.Equal(t, org.Id, response.Id)
	assert.Equal(t, org.Name, response.Name)
	assert.Equal(t, org.Description, response.Description)
	assert.Equal(t, org.OrgTypeIdentifier, response.OrgTypeIdentifier)
	assert.Equal(t, org.CreatedAt, response.CreatedAt)
	assert.Equal(t, org.UpdatedAt, response.UpdatedAt)
}

// Test createOrganization function
func Test_createOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		request     *CreateAndUpdateOrganizationRequest
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name: "successful creation",
			request: &CreateAndUpdateOrganizationRequest{
				Name:              "Test Organization",
				Description:       "Test Organization Description",
				OrgTypeIdentifier: "municipality",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organization creation
					org := dest.(*Organization)
					org.Id = uuid.New()
					org.Name = "Test Organization"
					org.Description = "Test Organization Description"
					org.OrgTypeIdentifier = "municipality"
					org.CreatedAt = time.Now().UTC()
					org.UpdatedAt = time.Now().UTC()
					org.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name: "database error",
			request: &CreateAndUpdateOrganizationRequest{
				Name:              "Test Organization",
				Description:       "Test Organization Description",
				OrgTypeIdentifier: "municipality",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := createOrganization(mockDB, tt.request)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Name, result.Name)
				assert.Equal(t, tt.request.Description, result.Description)
				assert.Equal(t, tt.request.OrgTypeIdentifier, result.OrgTypeIdentifier)
				assert.NotEmpty(t, result.Id)
			}
		})
	}
}

// Test getAllOrganizations function
func Test_getAllOrganizations(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
		expectedLen int
	}{
		{
			name: "successful retrieval with organizations",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organizations retrieval
					orgs := dest.(*[]Organization)
					*orgs = []Organization{
						{
							Id:                uuid.New(),
							Name:              "Organization 1",
							Description:       "Organization 1 Description",
							OrgTypeIdentifier: "municipality",
							CreatedAt:         time.Now().UTC(),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						},
						{
							Id:                uuid.New(),
							Name:              "Organization 2",
							Description:       "Organization 2 Description",
							OrgTypeIdentifier: "oem",
							CreatedAt:         time.Now().UTC(),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						},
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 2,
		},
		{
			name: "successful retrieval with no organizations",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call with empty result
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate empty organizations list
					orgs := dest.(*[]Organization)
					*orgs = []Organization{}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 0,
		},
		{
			name: "database error",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
			expectedLen: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getAllOrganizations(mockDB)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, *result, tt.expectedLen)
			}
		})
	}
}

// Test getOrganizationByIdentifier function
func Test_getOrganizationByIdentifier(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful retrieval",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organization retrieval
					org := dest.(*Organization)
					org.Id = uuid.New()
					org.Name = "Test Organization"
					org.Description = "Test Organization Description"
					org.OrgTypeIdentifier = "municipality"
					org.CreatedAt = time.Now().UTC()
					org.UpdatedAt = time.Now().UTC()
					org.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "organization not found",
			identifier: "non-existent-org",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock sql.ErrNoRows
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:       "database error",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getOrganizationByIdentifier(mockDB, tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "Test Organization", result.Name)
				assert.Equal(t, "Test Organization Description", result.Description)
				assert.Equal(t, "municipality", result.OrgTypeIdentifier)
				assert.NotEmpty(t, result.Id)
			}
		})
	}
}

// Test updateOrganization function
func Test_updateOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  string
		request     *UpdateOrganizationRequest
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful update",
			identifier: "test-org-123",
			request: &UpdateOrganizationRequest{
				Name:        "Updated Organization",
				Description: "Updated Organization Description",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organization update
					org := dest.(*Organization)
					org.Id = uuid.New()
					org.Name = "Updated Organization"
					org.Description = "Updated Organization Description"
					org.OrgTypeIdentifier = "municipality"
					org.CreatedAt = time.Now().UTC().Add(-time.Hour)
					org.UpdatedAt = time.Now().UTC()
					org.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "organization not found",
			identifier: "non-existent-org",
			request: &UpdateOrganizationRequest{
				Name:        "Updated Organization",
				Description: "Updated Organization Description",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock sql.ErrNoRows
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:       "database error",
			identifier: "test-org-123",
			request: &UpdateOrganizationRequest{
				Name:        "Updated Organization",
				Description: "Updated Organization Description",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := updateOrganization(mockDB, tt.identifier, tt.request)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Name, result.Name)
				assert.Equal(t, tt.request.Description, result.Description)
				assert.NotEmpty(t, result.Id)
			}
		})
	}
}

// Mock sql.Result for testing
type mockSQLResult struct {
	rowsAffected int64
	lastInsertId int64
	err          error
}

func (m *mockSQLResult) LastInsertId() (int64, error) {
	return m.lastInsertId, m.err
}

func (m *mockSQLResult) RowsAffected() (int64, error) {
	return m.rowsAffected, m.err
}

// Test deleteOrganization function
func Test_deleteOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful deletion",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 1 row affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "organization not found (0 rows affected)",
			identifier: "non-existent-org",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 0 rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:       "database error on exec",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error on Exec
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:       "error getting rows affected",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock error when getting rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1, err: errors.New("rows affected error")}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:       "nil result from exec",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock nil result (edge case)
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			err := deleteOrganization(mockDB, tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test CreateHandlerWithDeps function
func Test_CreateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    interface{}
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name: "successful_creation",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "Test Organization",
				Description:       "Test Organization Description",
				OrgTypeIdentifier: "municipality",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						// Return mock connections
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateOrganization: func(pg connect.DatabaseExecutor, req *CreateAndUpdateOrganizationRequest) (*Organization, error) {
						// Return mock organization
						return &Organization{
							Id:                uuid.New(),
							Name:              req.Name,
							Description:       req.Description,
							OrgTypeIdentifier: req.OrgTypeIdentifier,
							CreatedAt:         time.Now().UTC(),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						}, nil
					},
					CreateCustomRolesForOrganization: func(pg connect.DatabaseExecutor, orgId uuid.UUID, orgTypeIdentifier string) error {
						// Return success
						return nil
					},
					CreateDefaultDeviceGroup: func(pg connect.DatabaseExecutor, orgId uuid.UUID, defaultGroupName string) error {
						// Return success
						return nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name: "connection_error",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "Test Organization",
				Description:       "Test Organization Description",
				OrgTypeIdentifier: "municipality",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:        "invalid_request_body",
			requestBody: "invalid json",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name: "create_organization_error",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "Test Organization",
				Description:       "Test Organization Description",
				OrgTypeIdentifier: "municipality",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateOrganization: func(pg connect.DatabaseExecutor, req *CreateAndUpdateOrganizationRequest) (*Organization, error) {
						return nil, errors.New("database error")
					},
					CreateCustomRolesForOrganization: func(pg connect.DatabaseExecutor, orgId uuid.UUID, orgTypeIdentifier string) error {
						return nil
					},
					CreateDefaultDeviceGroup: func(pg connect.DatabaseExecutor, orgId uuid.UUID, defaultGroupName string) error {
						return nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name: "create_custom_roles_error",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "Test Organization",
				Description:       "Test Organization Description",
				OrgTypeIdentifier: "municipality",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateOrganization: func(pg connect.DatabaseExecutor, req *CreateAndUpdateOrganizationRequest) (*Organization, error) {
						return &Organization{
							Id:                uuid.New(),
							Name:              req.Name,
							Description:       req.Description,
							OrgTypeIdentifier: req.OrgTypeIdentifier,
							CreatedAt:         time.Now().UTC(),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						}, nil
					},
					CreateCustomRolesForOrganization: func(pg connect.DatabaseExecutor, orgId uuid.UUID, orgTypeIdentifier string) error {
						return errors.New("custom roles creation failed")
					},
					CreateDefaultDeviceGroup: func(pg connect.DatabaseExecutor, orgId uuid.UUID, defaultGroupName string) error {
						return nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name: "create_default_device_group_error",
			requestBody: CreateAndUpdateOrganizationRequest{
				Name:              "Test Organization",
				Description:       "Test Organization Description",
				OrgTypeIdentifier: "municipality",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateOrganization: func(pg connect.DatabaseExecutor, req *CreateAndUpdateOrganizationRequest) (*Organization, error) {
						return &Organization{
							Id:                uuid.New(),
							Name:              req.Name,
							Description:       req.Description,
							OrgTypeIdentifier: req.OrgTypeIdentifier,
							CreatedAt:         time.Now().UTC(),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						}, nil
					},
					CreateCustomRolesForOrganization: func(pg connect.DatabaseExecutor, orgId uuid.UUID, orgTypeIdentifier string) error {
						return nil
					},
					CreateDefaultDeviceGroup: func(pg connect.DatabaseExecutor, orgId uuid.UUID, defaultGroupName string) error {
						return errors.New("default device group creation failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := CreateHandlerWithDeps(tt.setupDeps())

			// Create mock request
			req := createMockRequest("POST", "/organizations", tt.requestBody)
			req = req.WithContext(context.Background())

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test GetAllHandlerWithDeps function
func Test_GetAllHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name: "successful_retrieval",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetAllOrganizations: func(pg connect.DatabaseExecutor) (*[]Organization, error) {
						// Return mock organizations
						orgs := []Organization{
							{
								Id:                uuid.New(),
								Name:              "Organization 1",
								Description:       "Organization 1 Description",
								OrgTypeIdentifier: "municipality",
								CreatedAt:         time.Now().UTC(),
								UpdatedAt:         time.Now().UTC(),
								IsDeleted:         false,
							},
							{
								Id:                uuid.New(),
								Name:              "Organization 2",
								Description:       "Organization 2 Description",
								OrgTypeIdentifier: "oem",
								CreatedAt:         time.Now().UTC(),
								UpdatedAt:         time.Now().UTC(),
								IsDeleted:         false,
							},
						}
						return &orgs, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name: "connection_error",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name: "get_organizations_error",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						// Return a mock database that will cause getUserOrganizations to fail
						mockDB := &dbexecutor.FakeDBExecutor{}
						mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
							return errors.New("database error")
						}
						return &connect.Connections{
							Postgres: mockDB,
						}, nil
					},
					GetAllOrganizations: func(pg connect.DatabaseExecutor) (*[]Organization, error) {
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := GetAllHandlerWithDeps(tt.setupDeps())

			// Create mock request
			req := httptest.NewRequest("GET", "/organizations", nil)

			// Add user permissions to context for the new access control logic
			ctx := context.Background()
			userPermissions := &authorizer.UserPermissions{
				UserID: "test-user-123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "test-org-123",
						OrganizationID: "test-org-123",
						Permissions:    []string{"org_view_users"},
					},
				},
			}
			ctx = authorizer.AddUserPermissionsToContext(ctx, userPermissions)
			req = req.WithContext(ctx)

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test GetByIdentifierHandlerWithDeps function
func Test_GetByIdentifierHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Generate a valid UUID for organizationId
	validOrgID := uuid.New()
	validOrgIDStr := validOrgID.String()

	tests := []struct {
		name           string
		organizationId string
		setupDeps      func(*testing.T) HandlerDeps
		expectedStatus int
	}{
		{
			name:           "successful_retrieval",
			organizationId: validOrgIDStr,
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetOrganization: func(pg connect.DatabaseExecutor, identifier string) (*Organization, error) {
						assert.Equal(t, validOrgIDStr, identifier)
						return &Organization{
							Id:                validOrgID,
							Name:              "Test Organization",
							Description:       "Test Organization Description",
							OrgTypeIdentifier: "municipality",
							CreatedAt:         time.Now().UTC(),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						}, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:           "connection_error",
			organizationId: validOrgIDStr,
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "invalid_identifier",
			organizationId: "not-a-uuid",
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "get_organization_error",
			organizationId: validOrgIDStr,
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetOrganization: func(pg connect.DatabaseExecutor, identifier string) (*Organization, error) {
						assert.Equal(t, validOrgIDStr, identifier)
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "get_organization_not_found",
			organizationId: validOrgIDStr,
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetOrganization: func(pg connect.DatabaseExecutor, identifier string) (*Organization, error) {
						assert.Equal(t, validOrgIDStr, identifier)
						return nil, ErrOrganizationNotFound
					},
				}
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		tt := tt // capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := GetByIdentifierHandlerWithDeps(tt.setupDeps(t))

			// Create mock request with URL vars
			req := httptest.NewRequest("GET", "/organizations/"+tt.organizationId, nil)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.organizationId})

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test UpdateHandlerWithDeps function
func Test_UpdateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Helper to create a valid UUID string for organizationId
	validOrgID := uuid.New()
	validOrgIDStr := validOrgID.String()

	type testCase struct {
		name           string
		identifier     string
		requestBody    interface{}
		setupDeps      func(*testing.T) HandlerDeps
		expectedStatus int
	}

	tests := []testCase{
		{
			name:       "successful_update",
			identifier: validOrgIDStr,
			requestBody: UpdateOrganizationRequest{
				Name:        "Updated Organization",
				Description: "Updated Organization Description",
			},
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateOrganization: func(pg connect.DatabaseExecutor, identifier string, req *UpdateOrganizationRequest) (*Organization, error) {
						assert.Equal(t, validOrgIDStr, identifier)
						assert.Equal(t, "Updated Organization", req.Name)
						assert.Equal(t, "Updated Organization Description", req.Description)
						return &Organization{
							Id:                validOrgID,
							Name:              req.Name,
							Description:       req.Description,
							OrgTypeIdentifier: "municipality",
							CreatedAt:         time.Now().UTC().Add(-time.Hour),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						}, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:       "connection_error",
			identifier: validOrgIDStr,
			requestBody: UpdateOrganizationRequest{
				Name:        "Updated Organization",
				Description: "Updated Organization Description",
			},
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:       "invalid_identifier",
			identifier: "not-a-uuid",
			requestBody: UpdateOrganizationRequest{
				Name:        "Updated Organization",
				Description: "Updated Organization Description",
			},
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:        "invalid_request_body",
			identifier:  validOrgIDStr,
			requestBody: "invalid json", // This will marshal to a string, not a struct
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:       "update_organization_error",
			identifier: validOrgIDStr,
			requestBody: UpdateOrganizationRequest{
				Name:        "Updated Organization",
				Description: "Updated Organization Description",
			},
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateOrganization: func(pg connect.DatabaseExecutor, identifier string, req *UpdateOrganizationRequest) (*Organization, error) {
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:       "update_organization_not_found",
			identifier: validOrgIDStr,
			requestBody: UpdateOrganizationRequest{
				Name:        "Updated Organization",
				Description: "Updated Organization Description",
			},
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateOrganization: func(pg connect.DatabaseExecutor, identifier string, req *UpdateOrganizationRequest) (*Organization, error) {
						return nil, ErrOrganizationNotFound
					},
				}
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		tt := tt // capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			handler := UpdateHandlerWithDeps(tt.setupDeps(t))

			// Marshal request body to JSON if it's a struct, else use as is
			var req *http.Request
			if s, ok := tt.requestBody.(string); ok {
				// Simulate invalid JSON by passing a string that is not valid JSON for the struct
				req = httptest.NewRequest("PATCH", "/organizations/"+tt.identifier, bytes.NewBuffer([]byte(s)))
			} else {
				req = createMockRequest("PATCH", "/organizations/"+tt.identifier, tt.requestBody)
			}
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.identifier})

			rr := httptest.NewRecorder()
			handler.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test createCustomRolesForOrganization function
func Test_createCustomRolesForOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name              string
		orgId             uuid.UUID
		orgTypeIdentifier string
		setupMock         func(*dbexecutor.FakeDBExecutor)
		expectedErr       error
		wantErr           bool
	}{
		{
			name:              "successful_creation",
			orgId:             uuid.New(),
			orgTypeIdentifier: "municipality",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 1 row affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:              "database_error",
			orgId:             uuid.New(),
			orgTypeIdentifier: "municipality",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error on Exec
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:              "no_rows_affected",
			orgId:             uuid.New(),
			orgTypeIdentifier: "municipality",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 0 rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:              "error_getting_rows_affected",
			orgId:             uuid.New(),
			orgTypeIdentifier: "municipality",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock error when getting rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1, err: errors.New("rows affected error")}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:              "nil_result",
			orgId:             uuid.New(),
			orgTypeIdentifier: "municipality",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock nil result (edge case)
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			err := createCustomRolesForOrganization(mockDB, tt.orgId, tt.orgTypeIdentifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test createDefaultDeviceGroup function
func Test_createDefaultDeviceGroup(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name             string
		orgId            uuid.UUID
		defaultGroupName string
		setupMock        func(*dbexecutor.FakeDBExecutor)
		expectedErr      error
		wantErr          bool
	}{
		{
			name:             "successful_creation",
			orgId:            uuid.New(),
			defaultGroupName: "Default Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 1 row affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:             "database_error",
			orgId:            uuid.New(),
			defaultGroupName: "Default Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error on Exec
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:             "no_rows_affected",
			orgId:            uuid.New(),
			defaultGroupName: "Default Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 0 rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:             "error_getting_rows_affected",
			orgId:            uuid.New(),
			defaultGroupName: "Default Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock error when getting rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1, err: errors.New("rows affected error")}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:             "nil_result",
			orgId:            uuid.New(),
			defaultGroupName: "Default Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock nil result (edge case)
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			err := createDefaultDeviceGroup(mockDB, tt.orgId, tt.defaultGroupName)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test DeleteHandlerWithDeps function
func Test_DeleteHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Generate a valid UUID for organizationId
	validOrgID := uuid.New()
	validOrgIDStr := validOrgID.String()

	tests := []struct {
		name              string
		organizationId    string
		setupDeps         func(*testing.T) HandlerDeps
		expectedStatus    int
		expectSuccessBody bool
	}{
		{
			name:           "successful_deletion",
			organizationId: validOrgIDStr,
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					DeleteOrganization: func(pg connect.DatabaseExecutor, organizationId string) error {
						assert.Equal(t, validOrgIDStr, organizationId)
						return nil
					},
				}
			},
			expectedStatus:    http.StatusOK,
			expectSuccessBody: true,
		},
		{
			name:           "connection_error",
			organizationId: validOrgIDStr,
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus:    http.StatusInternalServerError,
			expectSuccessBody: false,
		},
		{
			name:           "invalid_identifier",
			organizationId: "not-a-uuid",
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus:    http.StatusBadRequest,
			expectSuccessBody: false,
		},
		{
			name:           "delete_organization_error",
			organizationId: validOrgIDStr,
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					DeleteOrganization: func(pg connect.DatabaseExecutor, organizationId string) error {
						assert.Equal(t, validOrgIDStr, organizationId)
						return errors.New("database error")
					},
				}
			},
			expectedStatus:    http.StatusInternalServerError,
			expectSuccessBody: false,
		},
		{
			name:           "delete_organization_not_found",
			organizationId: validOrgIDStr,
			setupDeps: func(t *testing.T) HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					DeleteOrganization: func(pg connect.DatabaseExecutor, organizationId string) error {
						assert.Equal(t, validOrgIDStr, organizationId)
						return ErrOrganizationNotFound
					},
				}
			},
			expectedStatus:    http.StatusNotFound,
			expectSuccessBody: false,
		},
	}

	for _, tt := range tests {
		tt := tt // capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			handler := DeleteHandlerWithDeps(tt.setupDeps(t))

			req := httptest.NewRequest("DELETE", "/organizations/"+tt.organizationId, nil)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.organizationId})

			rr := httptest.NewRecorder()
			handler.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)

			// For successful deletion, check that the response body is a success response (empty string)
			if tt.expectSuccessBody {
				// The response should be a JSON object: {"status":"success","code":200,"message":"Request Succeeded","data":""}
				expected := `{"status":"success","code":200,"message":"Request Succeeded","data":""}`
				assert.JSONEq(t, expected, rr.Body.String())
			}
		})
	}
}

// Test getUserOrganizations function
func Test_getUserOrganizations(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		userID      string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
		expectedLen int
	}{
		{
			name:   "successful retrieval with user organizations",
			userID: "test-user-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful user organizations retrieval
					orgs := dest.(*[]Organization)
					*orgs = []Organization{
						{
							Id:                uuid.New(),
							Name:              "User Organization 1",
							Description:       "User Organization 1 Description",
							OrgTypeIdentifier: "municipality",
							CreatedAt:         time.Now().UTC(),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						},
						{
							Id:                uuid.New(),
							Name:              "User Organization 2",
							Description:       "User Organization 2 Description",
							OrgTypeIdentifier: "municipality",
							CreatedAt:         time.Now().UTC(),
							UpdatedAt:         time.Now().UTC(),
							IsDeleted:         false,
						},
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 2,
		},
		{
			name:   "successful retrieval with no user organizations",
			userID: "test-user-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call with empty result
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate empty user organizations list
					orgs := dest.(*[]Organization)
					*orgs = []Organization{}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 0,
		},
		{
			name:   "database error",
			userID: "test-user-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
			expectedLen: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getUserOrganizations(mockDB, tt.userID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, *result, tt.expectedLen)
			}
		})
	}
}

// Test GetAllHandlerWithDeps with user permissions for access control
func Test_GetAllHandlerWithDeps_AccessControl(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                      string
		userPermissions           *authorizer.UserPermissions
		setupDeps                 func() HandlerDeps
		expectedStatus            int
		expectedOrganizationCount int
		wantErr                   bool
	}{
		{
			name: "user_with_synapse_view_organizations_permission",
			userPermissions: &authorizer.UserPermissions{
				UserID: "synapse-user-123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "synapse-org-123",
						OrganizationID: "synapse-org-123",
						Permissions:    []string{"synapse_view_organizations"},
					},
				},
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetAllOrganizations: func(pg connect.DatabaseExecutor) (*[]Organization, error) {
						// Return all organizations (synapse user sees all)
						orgs := []Organization{
							{
								Id:                uuid.New(),
								Name:              "All Organization 1",
								Description:       "All Organization 1 Description",
								OrgTypeIdentifier: "municipality",
								CreatedAt:         time.Now().UTC(),
								UpdatedAt:         time.Now().UTC(),
								IsDeleted:         false,
							},
							{
								Id:                uuid.New(),
								Name:              "All Organization 2",
								Description:       "All Organization 2 Description",
								OrgTypeIdentifier: "oem",
								CreatedAt:         time.Now().UTC(),
								UpdatedAt:         time.Now().UTC(),
								IsDeleted:         false,
							},
							{
								Id:                uuid.New(),
								Name:              "All Organization 3",
								Description:       "All Organization 3 Description",
								OrgTypeIdentifier: "synapse",
								CreatedAt:         time.Now().UTC(),
								UpdatedAt:         time.Now().UTC(),
								IsDeleted:         false,
							},
						}
						return &orgs, nil
					},
				}
			},
			expectedStatus:            http.StatusOK,
			expectedOrganizationCount: 3,
			wantErr:                   false,
		},
		{
			name: "user_with_synapse_view_organizations_permission_get_all_organizations_error",
			userPermissions: &authorizer.UserPermissions{
				UserID: "synapse-user-123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "synapse-org-123",
						OrganizationID: "synapse-org-123",
						Permissions:    []string{"synapse_view_organizations"},
					},
				},
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetAllOrganizations: func(pg connect.DatabaseExecutor) (*[]Organization, error) {
						// Return error for getAllOrganizations
						return nil, errors.New("failed to get all organizations")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name: "user_without_synapse_view_organizations_permission",
			userPermissions: &authorizer.UserPermissions{
				UserID: "normal-user-123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "user-org-123",
						OrganizationID: "user-org-123",
						Permissions:    []string{"org_view_users"},
					},
				},
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						// Return a mock database that will return user organizations
						mockDB := &dbexecutor.FakeDBExecutor{}
						mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
							// Simulate successful user organizations retrieval
							orgs := dest.(*[]Organization)
							*orgs = []Organization{
								{
									Id:                uuid.New(),
									Name:              "User Organization 1",
									Description:       "User Organization 1 Description",
									OrgTypeIdentifier: "municipality",
									CreatedAt:         time.Now().UTC(),
									UpdatedAt:         time.Now().UTC(),
									IsDeleted:         false,
								},
								{
									Id:                uuid.New(),
									Name:              "User Organization 2",
									Description:       "User Organization 2 Description",
									OrgTypeIdentifier: "municipality",
									CreatedAt:         time.Now().UTC(),
									UpdatedAt:         time.Now().UTC(),
									IsDeleted:         false,
								},
							}
							return nil
						}
						return &connect.Connections{
							Postgres: mockDB,
						}, nil
					},
					GetAllOrganizations: func(pg connect.DatabaseExecutor) (*[]Organization, error) {
						// This should not be called for normal users
						return nil, errors.New("should not be called")
					},
				}
			},
			expectedStatus:            http.StatusOK,
			expectedOrganizationCount: 2, // Only user's organizations
			wantErr:                   false,
		},
		{
			name:            "user_permissions_not_found_in_context",
			userPermissions: nil,
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name: "connection_error",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user-123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "user-org-123",
						OrganizationID: "user-org-123",
						Permissions:    []string{"org_view_users"},
					},
				},
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name: "get_user_organizations_error",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user-123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "user-org-123",
						OrganizationID: "user-org-123",
						Permissions:    []string{"org_view_users"},
					},
				},
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						// Return a mock database that will cause getUserOrganizations to fail
						mockDB := &dbexecutor.FakeDBExecutor{}
						mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
							return errors.New("database connection failed")
						}
						return &connect.Connections{
							Postgres: mockDB,
						}, nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := GetAllHandlerWithDeps(tt.setupDeps())

			// Create mock request
			req := httptest.NewRequest("GET", "/organizations", nil)

			// Add user permissions to context if provided
			ctx := context.Background()
			if tt.userPermissions != nil {
				ctx = authorizer.AddUserPermissionsToContext(ctx, tt.userPermissions)
			}
			req = req.WithContext(ctx)

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)

			// If successful, check response body
			if tt.expectedStatus == http.StatusOK && !tt.wantErr {
				var response map[string]any
				err := json.NewDecoder(rr.Body).Decode(&response)
				assert.NoError(t, err)

				// Check that data field exists and has expected count
				if data, ok := response["data"].([]any); ok {
					assert.Len(t, data, tt.expectedOrganizationCount)
				}
			}
		})
	}
}

// Test GetAllHandlerWithDeps with session data path (not authorizer context)
func Test_GetAllHandlerWithDeps_SessionData(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		sessionData    interface{}
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name: "successful_retrieval_with_session_data",
			sessionData: &domain.Session{
				UserPermissions: &domain.UserPermissions{
					UserID: "test-user-123",
					Permissions: []authorizer.Permission{
						{
							Scope:          "org",
							ScopeID:        "test-org-123",
							OrganizationID: "test-org-123",
							Permissions:    []string{"org_view_users"},
						},
					},
				},
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						// Return a mock database that will return user organizations
						mockDB := &dbexecutor.FakeDBExecutor{}
						mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
							// Simulate successful user organizations retrieval
							orgs := dest.(*[]Organization)
							*orgs = []Organization{
								{
									Id:                uuid.New(),
									Name:              "User Organization 1",
									Description:       "User Organization 1 Description",
									OrgTypeIdentifier: "municipality",
									CreatedAt:         time.Now().UTC(),
									UpdatedAt:         time.Now().UTC(),
									IsDeleted:         false,
								},
							}
							return nil
						}
						return &connect.Connections{
							Postgres: mockDB,
						}, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name:        "session_data_not_found",
			sessionData: nil,
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:        "invalid_session_data_type",
			sessionData: "invalid-type",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name: "session_data_without_user_permissions",
			sessionData: &domain.Session{
				UserPermissions: nil,
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := GetAllHandlerWithDeps(tt.setupDeps())

			// Create mock request
			req := httptest.NewRequest("GET", "/organizations", nil)

			// Add session data to context (simulating onramp microservice context)
			ctx := context.Background()
			if tt.sessionData != nil {
				ctx = context.WithValue(ctx, middlewares.SessionContextKey, tt.sessionData)
			}
			req = req.WithContext(ctx)

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}
