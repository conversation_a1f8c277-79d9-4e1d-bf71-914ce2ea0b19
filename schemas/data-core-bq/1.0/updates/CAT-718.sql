{% UPDATE {{logPreviousFail}}
SET records =
(SELECT ARRAY_AGG(
         (SELECT AS STRUCT records.* REPLACE(COALESCE(records.t12vdcinput, '') AS t12vdcinput))
         ORDER BY off
       ) AS record_array
FROM UNNEST(records) AS records with offset off)
WHERE EXISTS (select 1 from unnest(records) r where r.t12vdcinput is null);

UPDATE {{logConfiguration}}
SET record = 
(SELECT ARRAY_AGG(
        (SELECT AS STRUCT record.* REPLACE(
          COALESCE(record.x12vpowersupplymonitor, false) AS x12vpowersupplymonitor,
          COALESCE(record.x48vpowersupplymonitor, false) as x48vpowersupplymonitor)
        )
        ORDER BY off
      ) AS record_array
FROM UNNEST(record) AS record with offset off)
WHERE EXISTS (select 1 from unnest(record) r where r.x12vpowersupplymonitor is null or r.x48vpowersupplymonitor is null); %}
select 1;