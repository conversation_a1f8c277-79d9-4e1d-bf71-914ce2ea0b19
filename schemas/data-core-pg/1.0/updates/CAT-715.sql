-- CAT-700: Create AppVersion table for managing mobile application versions
-- This table stores version information for different mobile applications and platforms

CREATE TABLE {{AppVersion}} (
  id UUID NOT NULL DEFAULT uuid_generate_v4(),
  appname TEXT NOT NULL,
  platform TEXT NOT NULL,
  version TEXT NOT NULL DEFAULT '1.0.0',
  enforce BOOLEAN NOT NULL DEFAULT FALSE,
  isdeleted BOOLEAN NOT NULL DEFAULT FALSE,
  created TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT {{AppVersion_PK}} PRIMARY KEY (id),
  CONSTRAINT {{AppVersion_appname_platform_UQ}} UNIQUE (appname, platform),
  CONSTRAINT {{AppVersion_platform_CHK}} CHECK (platform IN ('android','ios','windows')),
  CONSTRAINT {{AppVersion_appname_CHK}} CHECK (appname IN ('EDIFieldServiceApp'))
);

-- Create indexes for efficient querying
CREATE INDEX {{AppVersion_appname_IDX}} ON {{AppVersion}} (appname);
CREATE INDEX {{AppVersion_platform_IDX}} ON {{AppVersion}} (platform);
CREATE INDEX {{AppVersion_isdeleted_IDX}} ON {{AppVersion}} (isdeleted);

-- Insert initial seed data for EDIFieldServiceApp
INSERT INTO {{AppVersion}} (appname, platform, version, enforce) VALUES
('EDIFieldServiceApp', 'android', '1.0.0', FALSE),
('EDIFieldServiceApp', 'ios', '1.0.0', FALSE),
('EDIFieldServiceApp', 'windows', '1.0.0', FALSE);
