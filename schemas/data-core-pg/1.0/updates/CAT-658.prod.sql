-- Base organization (already exists in DEV and QA)
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  ('c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Synapse-Plano-Demo', 'Synapse-Plano-Demo', 'municipality')
ON CONFLICT (Id) DO UPDATE
SET Name              = EXCLUDED.Name,
    Description       = EXCLUDED.Description,
    OrgTypeIdentifier = EXCLUDED.OrgTypeIdentifier;
-- Base roles (already exists in DEV and QA)
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  ('12dc75d1-13ac-5bbe-810c-0c40bcddca21', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_admin', 'municipality', 'Admin', 'Administrative permissions', false),
  ('49f5bc0b-f26f-5ab6-96a0-10f2c177a7b6', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_manager', 'municipality', 'Manager', 'Manager permissions', true),
  ('7a838232-87c5-519d-9147-e4f27d4f03ea', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_technician', 'municipality', 'Technician', 'Technician permissions', true),
  ('58925ea8-2cfb-5e7b-9fd3-d1debac814e6', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_anonymous', 'municipality', 'Anonymous', 'Deny all permissions', false)
ON CONFLICT (Id) DO UPDATE
SET OrganizationId         = EXCLUDED.OrganizationId,
    TemplateRoleIdentifier = EXCLUDED.TemplateRoleIdentifier,
    OrgTypeIdentifier      = EXCLUDED.OrgTypeIdentifier,
    Name                   = EXCLUDED.Name,
    Description            = EXCLUDED.Description,
    IsDeletable            = EXCLUDED.IsDeletable;
-- Base softwaregateway (already exists in DEV and QA)
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, ApiKey, Token, DateLastCheckedIn, PushConfigOnNextCheck, IsEnabled, Config, Name, Description) VALUES
  ('f55c66ea-e279-5961-a0b1-b212dca33dc9', '4777b483831293d620b96ba410d14894086104f83cede6bc8477ac5828bbdf2e', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', '2025-06-30 21:51:40', false, true, '', 'Synapse-Plano-Demo', 'Synapse-Plano-Demo')
ON CONFLICT (Id) DO UPDATE
SET MachineKey            = EXCLUDED.MachineKey,
    OrganizationId        = EXCLUDED.OrganizationId,
    ApiKey                = EXCLUDED.ApiKey,
    Token                 = EXCLUDED.Token,
    DateLastCheckedIn     = EXCLUDED.DateLastCheckedIn,
    PushConfigOnNextCheck = EXCLUDED.PushConfigOnNextCheck,
    IsEnabled             = EXCLUDED.IsEnabled,
    Config                = EXCLUDED.Config,
    Name                  = EXCLUDED.Name,
    Description           = EXCLUDED.Description;
-- Base locations (some already exists in DEV and QA)
INSERT INTO {{Location}} (Id, OrganizationId, Name, Description, Latitude, Longitude) VALUES
  ('ced1aca1-bd42-5a04-a6fb-40feb81c5a41', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0227, -96.7353),
  ('37a51eb2-3cb0-5505-a94c-4e94ad2718d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0227, -96.7517),
  ('8c616bb0-439d-5863-a619-e50f8dd7fee4', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0173, -96.7026),
  ('b184a6bc-2eb4-58ae-a91e-10da5538065b', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0285, -96.7025),
  ('4a22b16d-8b1d-56b0-b357-f92aadfe7e65', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0285, -96.7353),
  ('6746f2d7-5d8a-52f8-9dc3-10bb137c42d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0285, -96.7517),
  ('145c1fd1-5a2e-589a-bc7f-431bd9228082', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0408, -96.7354),
  ('c28a137b-30b7-5df5-914b-1f2110934d6d', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0408, -96.7026),
  ('f1e3685a-03b2-52a2-aaac-60c269cb0d99', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0408, -96.7517)
ON CONFLICT (Id) DO UPDATE
SET OrganizationId = EXCLUDED.OrganizationId,
    Name           = EXCLUDED.Name,
    Description    = EXCLUDED.Description,
    Latitude       = EXCLUDED.Latitude,
    Longitude      = EXCLUDED.Longitude;
-- Base devices (some already exists in DEV and QA) Port ranges are 12000-12999 to avoid collisions with the dev/qa/prod gateways running on the same machine.
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, OrganizationId, Name, Description, IpAddress, Port, Type, FlushConnectionMs, EnableRealtime) VALUES
  ('87d94e14-e804-58b3-9f8c-a02e5de90aeb', 7000, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'ced1aca1-bd42-5a04-a6fb-40feb81c5a41', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12010, 'EDI_LEGACY', 400, true),
  ('5a7f98cd-aaa1-5b16-be30-d2f9fb86ed74', 7001, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '37a51eb2-3cb0-5505-a94c-4e94ad2718d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12011, 'EDI_LEGACY', 400, true),
  ('fb2ba632-80af-5836-a82f-d7d329ef924f', 7002, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '8c616bb0-439d-5863-a619-e50f8dd7fee4', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12012, 'EDI_LEGACY', 400, true),
  ('1858185e-cc6e-5a74-ac9a-5a4b3d9a7845', 7003, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'b184a6bc-2eb4-58ae-a91e-10da5538065b', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12013, 'EDI_LEGACY', 400, true),
  ('99c66f8b-a82f-5e85-bf4a-031d0b2be0bc', 7004, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '4a22b16d-8b1d-56b0-b357-f92aadfe7e65', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12014, 'EDI_LEGACY', 400, true),
  ('51d49dad-7891-5f19-b4f6-37fb69660dcc', 7005, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '6746f2d7-5d8a-52f8-9dc3-10bb137c42d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12015, 'EDI_LEGACY', 400, true),
  ('c465bd6a-3bad-5d5d-9e79-c2ecd8f61ea3', 7006, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '145c1fd1-5a2e-589a-bc7f-431bd9228082', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12016, 'EDI_LEGACY', 400, true),
  ('a907c9ff-8227-59e3-b66f-61e513c8bf5e', 7007, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'c28a137b-30b7-5df5-914b-1f2110934d6d', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12017, 'EDI_LEGACY', 400, true),
  ('e21b9d8e-d64b-5e12-a1f8-8aa7e2445395', 7008, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'f1e3685a-03b2-52a2-aaac-60c269cb0d99', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 12018, 'EDI_LEGACY', 400, true)
ON CONFLICT (Id) DO UPDATE
SET OrigId           = EXCLUDED.OrigId,
    SoftwareGatewayId= EXCLUDED.SoftwareGatewayId,
    LocationId       = EXCLUDED.LocationId,
    OrganizationId   = EXCLUDED.OrganizationId,
    Name             = EXCLUDED.Name,
    Description      = EXCLUDED.Description,
    IpAddress        = EXCLUDED.IpAddress,
    Port             = EXCLUDED.Port,
    Type             = EXCLUDED.Type,
    FlushConnectionMs= EXCLUDED.FlushConnectionMs,
    EnableRealtime   = EXCLUDED.EnableRealtime;
-- Base device group (already exists in DEV and QA). All legacy devices are in the same device group.
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'migrated group: 0a0840b7-2528-576a-94c2-6a9d31efb31f')
ON CONFLICT (Id) DO NOTHING;
-- Base device group devices (some already exists in DEV and QA)
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId) VALUES
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '87d94e14-e804-58b3-9f8c-a02e5de90aeb'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '5a7f98cd-aaa1-5b16-be30-d2f9fb86ed74'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'fb2ba632-80af-5836-a82f-d7d329ef924f'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '1858185e-cc6e-5a74-ac9a-5a4b3d9a7845'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '99c66f8b-a82f-5e85-bf4a-031d0b2be0bc'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '51d49dad-7891-5f19-b4f6-37fb69660dcc'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'c465bd6a-3bad-5d5d-9e79-c2ecd8f61ea3'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'a907c9ff-8227-59e3-b66f-61e513c8bf5e'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'e21b9d8e-d64b-5e12-a1f8-8aa7e2445395')
ON CONFLICT (DeviceGroupId, DeviceId) DO NOTHING;
-- Base SynapsePlano user (already exists in DEV and QA)
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description) VALUES
  ('45627c04-8d87-595a-a31b-2e675e22417a', 25, 'Demo', 'Account', '', false, 'America/Chicago', 'Synapse - Plano Demo Account')
ON CONFLICT (Id) DO NOTHING;
-- Base SynapsePlano auth method (already exists in DEV and QA)
INSERT INTO {{AuthMethod}} (Id, UserId, Type, Sub, Issuer, UserName, PasswordHash, Email, Metadata, LastLogin, FailedLoginAttempts, IsEnabled, ForcePasswordChange) VALUES
  ('692d620e-7dbf-5e7b-bb66-0167c3498f51', '45627c04-8d87-595a-a31b-2e675e22417a', 'USERNAME_PASSWORD', NULL, NULL, 'SynapsePlano', '5a6fd3906328e22b6e6df22e811f8d40038a92e98520a8199183841de3889564', NULL, NULL, '2025-07-01 01:31:10', 0, true, false)
ON CONFLICT (Id) DO NOTHING;
-- Base SynapsePlano membership (already exists in DEV and QA)
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  ('********-34a5-50f7-82d5-9039a8cf214b', '692d620e-7dbf-5e7b-bb66-0167c3498f51', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3')
ON CONFLICT (Id) DO NOTHING;
-- Base SynapsePlano device group role assignment (already exists in DEV and QA)
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  ('********-34a5-50f7-82d5-9039a8cf214b', '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea')
ON CONFLICT (MembershipId, DeviceGroupId, RoleId) DO NOTHING;

--- New Configuration ---

-- New locations
INSERT INTO {{Location}} (Id, Name, Description, Latitude, Longitude, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), '4th and Main', 'ALPHA', '33.7517577', '-84.41253', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_2'), '5th and Main', 'BRAVO', '33.748893', '-84.40453', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_3'), '6th and Main', 'CHARLIE', '33.7508979', '-84.4031675', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_4'), '2360', 'PLANO MMU 188:110', '33.7537577', '-84.3981712', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_5'), '2361', 'PLANO MMU 188:995', '33.7517577', '-84.3961712', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_6'), '2362', 'PLANO MMU 057:110', '33.7517577', '-84.3974713', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_7'), '2363', 'PLANO MMU 057:995', '33.7517577', '-84.3974713', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3');
-- Next gen devices. IsEnabled is false because we don't want the gateway to attempt to talk to them. They will show up in API calls. Device flag is used by FSA to identify the devices.
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, OrganizationId, DeviceFlag, Name, Description, IpAddress, Port, Type, FlushConnectionMs, EnableRealtime, SerialNumber, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_ALPHA'), 7100, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'ALPHA', 'ALPHA Next Gen Device', 'NA', '*************', 110, 'EDI_NEXT_GEN', 400, true, '101010', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_BETA'), 7101, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'BETA', 'BETA Next Gen Device', 'NA', '*************', 110, 'EDI_NEXT_GEN', 400, true, '202020', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_CHARLIE'), 7102, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'CHARLIE', 'CHARLIE Next Gen Device', 'NA', '*************', 110, 'EDI_NEXT_GEN', 400, true, '303030', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_110'), 7200, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'PLANO MMU 188:110', 'PLANO MMU 188:110 Next Gen Device', 'NA', '10.20.5.188', 110, 'EDI_NEXT_GEN', 400, true, '404040', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_995'), 7201, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'PLANO MMU 188:995', 'PLANO MMU 188:995 Next Gen Device', 'NA', '10.20.5.188', 995, 'EDI_NEXT_GEN', 400, true, '404040', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_110'), 7202, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'PLANO MMU 057:110', 'PLANO MMU 057:110 Next Gen Device', 'NA', '10.20.5.57', 110, 'EDI_NEXT_GEN', 400, true, '505050', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_995'), 7203, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'PLANO MMU 057:995', 'PLANO MMU 057:995 Next Gen Device', 'NA', '10.20.5.57', 995, 'EDI_NEXT_GEN', 400, true, '505050', false);
-- Seed next gen monitor_id and user_id. This is because these devices are not communicating with the gateway and hence not sending data to the cloud.
INSERT INTO {{DeviceMonitorName}} (DeviceId, MonitorId, MonitorName, PubsubTimestamp, UpdatedAt) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_ALPHA'), 11111111, '4th and Main', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_BETA'), 22222222, '5th and Main', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_CHARLIE'), 33333333, '6th and Main', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_110'), 44444444, '2360', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_995'), 55555555, '2361', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_110'), 66666666, '2362', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_995'), 77777777, '2363', NOW(), NOW());
-- New device groups. A new one is created for each device for grunular permissions.
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'ALPHA Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'BETA Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'CHARLIE Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', '188:110 Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', '188:995 Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', '057:110 Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', '057:995 Device Group');
-- New device group devices
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_ALPHA')), 
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_BETA')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_CHARLIE')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_110')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_995')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_110')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_995'));
-- New users. All users have sms disabled and will need to be enabled per user.
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jason'), 50001, 'Jason', 'DeVillier', '******-218-6057', false, 'America/Chicago', 'Synapse-Plano-Demo user jason', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jeff'), 50002, 'Jeff', 'Cornelius', '******-574-2528', false, 'America/Chicago', 'Synapse-Plano-Demo user jeff', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_brian'), 50003, 'Brian', 'Beckwith', '', false, 'America/Chicago', 'Synapse-Plano-Demo user brian', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_nicolas'), 50004, 'Nicolas', 'Chasteler', '', false, 'America/Chicago', 'Synapse-Plano-Demo user nicolas', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_daniel'), 50005, 'Daniel', 'Warunek', '', false, 'America/Chicago', 'Synapse-Plano-Demo user daniel', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_corey'), 50006, 'Corey', 'Pennycuff', '', false, 'America/Chicago', 'Synapse-Plano-Demo user corey', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_dan'), 50007, 'Dan', 'Skites', '', false, 'America/Chicago', 'Synapse-Plano-Demo user dan', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_walt'), 50008, 'Walt', 'Kicinski', '', false, 'America/Chicago', 'Synapse-Plano-Demo user walt', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_huy'), 50009, 'Huy', 'Dang', '', false, 'America/Chicago', 'Synapse-Plano-Demo user huy', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_mason'), 50010, 'Mason', 'Lambert', '', false, 'America/Chicago', 'Synapse-Plano-Demo user mason', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_allen'), 50011, 'Allen', 'Jacobs', '', false, 'America/Chicago', 'Synapse-Plano-Demo user allen', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jae'), 50012, 'Jae', 'Oh', '', false, 'America/Chicago', 'Synapse-Plano-Demo user jae', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jun'), 50013, 'Jun', 'Fu', '', false, 'America/Chicago', 'Synapse-Plano-Demo user jun', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_offshore'), 50014, 'Offshore', '', '', false, 'America/Chicago', 'Synapse-Plano-Demo user offshore', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show1'), 50100, 'Show1', '1', '', false, 'America/Chicago', 'Synapse-Plano-Demo user show1', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show2'), 50101, 'Show2', '2', '', false, 'America/Chicago', 'Synapse-Plano-Demo user show2', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show3'), 50102, 'Show3', '3', '', false, 'America/Chicago', 'Synapse-Plano-Demo user show3', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_appreview'), 50200, 'App', 'Review', '', false, 'America/Chicago', 'Synapse-Plano-Demo user app review', false);
-- New auth methods
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jason'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jason'), 'USERNAME_PASSWORD', 'jdevillier', '<EMAIL> ', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jeff'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jeff'), 'USERNAME_PASSWORD', 'jcornelius', '<EMAIL> ', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_brian'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_brian'), 'USERNAME_PASSWORD', 'bbeckwith', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_nicolas'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_nicolas'), 'USERNAME_PASSWORD', 'nchasteler', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_daniel'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_daniel'), 'USERNAME_PASSWORD', 'dwarunek', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_corey'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_corey'), 'USERNAME_PASSWORD', 'cpennycuff', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_dan'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_dan'), 'USERNAME_PASSWORD', 'dskites', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_walt'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_walt'), 'USERNAME_PASSWORD', 'wkicinski', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_huy'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_huy'), 'USERNAME_PASSWORD', 'hdang', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_mason'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_mason'), 'USERNAME_PASSWORD', 'mlambert', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_allen'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_allen'), 'USERNAME_PASSWORD', 'ajacobs', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jae'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jae'), 'USERNAME_PASSWORD', 'joh', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jun'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jun'), 'USERNAME_PASSWORD', 'jfu', '<EMAIL>', '', true),  
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_offshore'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_offshore'), 'USERNAME_PASSWORD', 'offshore', '<EMAIL>', '', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show1'), 'USERNAME_PASSWORD', 'show1', '<EMAIL>', '7d8c61ea70cf808b87ad4fa5f596b609c2cff72df200a770385ea29f4b02606d', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show2'), 'USERNAME_PASSWORD', 'show2', '<EMAIL>', '7d8c61ea70cf808b87ad4fa5f596b609c2cff72df200a770385ea29f4b02606d', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show3'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show3'), 'USERNAME_PASSWORD', 'show3', '<EMAIL>', '7d8c61ea70cf808b87ad4fa5f596b609c2cff72df200a770385ea29f4b02606d', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_appreview'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_appreview'), 'USERNAME_PASSWORD', 'appreview', '<EMAIL>', '60b4807e798624c086a55e0bc21f3ff1b31296346698951d48ad51a89f42f103', true);
-- New memberships
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jason'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jeff'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_brian'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_nicolas'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_daniel'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_corey'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_dan'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_walt'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_huy'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_mason'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_allen'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jae'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jun'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_offshore'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show2'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show3'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_appreview_plano'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_appreview'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3');
-- New device group role assignments. This is based on a matrix of users and devices so we know who can access what. All permissions are ready only through the cloud.
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
-- Legacy device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_appreview_plano'), '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'), -- App review only has access to legacy devices
-- Alpha device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Beta device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Charlie device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Plano 188:110 device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Plano 188:995 device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Plano 057:110 device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Plano 057:995 device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea')
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea')
;
