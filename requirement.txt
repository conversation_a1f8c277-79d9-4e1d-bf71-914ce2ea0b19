Title: Update APS reset fields in the database

Descriptions:
During testing, it was noticed that the following were missing:

Database requires entry into TemplateRolePermission for mun_admin and syn_admin. The permissions do not get correctly generated without this so this will need to be added for all template roles in the database. All roles should have false set except for the mun_admin and syn_admin which should be set to true. Note: there are 2 permissions, org_aps_factory_reset for municipalities and synapse_aps_factory_reset for synapse.

Rename the names and descriptions of the “APS Factory Reset” to “APS Password Factory Reset”. This is to be more clear as it is the tool used to reset the password back to the factory password. Note: Do not change the name of the permission as it will affect the frontend and permission middlewares.
